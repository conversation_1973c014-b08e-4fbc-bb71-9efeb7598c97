import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart'; // استخدام Syncfusion Charts بدلاً من fl_chart
import 'package:flutter_application_2/models/advanced_filter_options.dart';
import 'package:flutter_application_2/models/chart_enums.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';

import 'unified_filter_export_widget.dart';

/// كلاس لتمثيل نقطة بيانات في مخطط وقت الإكمال
class _CompletionTimeChartData {
  final String categoryName;
  final double value;
  final Color color;

  _CompletionTimeChartData(this.categoryName, this.value, this.color);
}

/// نموذج بيانات وقت الإكمال
class CompletionTimeData {
  /// اسم الفئة (نوع المهمة أو القسم)
  final String categoryName;

  /// متوسط وقت الإكمال (بالساعات)
  final double averageCompletionTime;

  /// عدد المهام
  final int taskCount;

  /// الحد الأدنى لوقت الإكمال
  final double minCompletionTime;

  /// الحد الأقصى لوقت الإكمال
  final double maxCompletionTime;

  /// لون البيانات (اختياري)
  final Color? color;

  /// إنشاء نموذج بيانات وقت الإكمال
  const CompletionTimeData({
    required this.categoryName,
    required this.averageCompletionTime,
    required this.taskCount,
    required this.minCompletionTime,
    required this.maxCompletionTime,
    this.color,
  });
}

/// مكون مخطط وقت الإكمال المحسن
///
/// يوفر هذا المكون مخططًا لوقت إكمال المهام مع دعم للتصفية والتصدير
class EnhancedCompletionTimeChart extends StatefulWidget {
  /// بيانات المخطط
  final List<CompletionTimeData> data;

  /// عنوان المخطط
  final String? title;

  /// ألوان المخطط (اختياري)
  final List<Color>? colors;

  /// دالة التصفية (اختياري)
  final Function(
          DateTime? startDate, DateTime? endDate, TimeFilterType filterType)?
      onFilterChanged;

  /// دالة التصدير (اختياري)
  final Function(String format)? onExport;

  /// دالة تغيير نوع المخطط (اختياري)
  final Function(ChartType)? onChartTypeChanged;

  /// إظهار خيارات التصفية (اختياري)
  final bool showFilterOptions;

  /// إظهار خيارات التصدير (اختياري)
  final bool showExportOptions;

  /// إظهار خيارات تغيير نوع المخطط (اختياري)
  final bool showChartTypeOptions;

  /// نوع المخطط الحالي
  final ChartType currentChartType;

  /// أنواع المخططات المدعومة (اختياري)
  final List<ChartType> supportedChartTypes;

  /// خيارات التصفية المتقدمة
  final AdvancedFilterOptions advancedFilterOptions;

  /// إنشاء مكون مخطط وقت الإكمال المحسن
  const EnhancedCompletionTimeChart({
    super.key,
    required this.data,
    this.title,
    this.colors,
    this.onFilterChanged,
    this.onExport,
    this.onChartTypeChanged,
    this.showFilterOptions = true,
    this.showExportOptions = true,
    this.showChartTypeOptions = true,
    this.currentChartType = ChartType.bar,
    this.supportedChartTypes = const [
      ChartType.bar,
      ChartType.line,
      ChartType.boxplot,
    ],
    required this.advancedFilterOptions,
  });

  @override
  State<EnhancedCompletionTimeChart> createState() =>
      _EnhancedCompletionTimeChartState();
}

class _EnhancedCompletionTimeChartState
    extends State<EnhancedCompletionTimeChart> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان المخطط وأزرار التصفية والتصدير
        if (widget.title != null ||
            widget.showFilterOptions ||
            widget.showExportOptions)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildHeader(),
          ),

        // المخطط
        Expanded(
          child: _buildChart(),
        ),

        // مفتاح المخطط
        Padding(
          padding: const EdgeInsets.only(top: 16),
          child: _buildLegend(),
        ),
      ],
    );
  }

  /// بناء رأس المخطط
  Widget _buildHeader() {
    return UnifiedFilterExportWidget(
      title: widget.title ?? 'تحليل وقت إكمال المهام',
      chartKey: 'completion_time_chart',
      onFilterChanged: (startDate, endDate, filterType, chartKey) {
        if (widget.onFilterChanged != null) {
          widget.onFilterChanged!(startDate, endDate, filterType);
        }
      },
      onExport: (format, title) {
        if (widget.onExport != null) {
          widget.onExport!(format);
        }
      },
      onChartTypeChanged: widget.onChartTypeChanged != null
          ? (chartType, chartKey) {
              widget.onChartTypeChanged!(chartType);
            }
          : null,
      showFilter: widget.showFilterOptions,
      showExport: widget.showExportOptions,
      showChartTypeSelector: widget.showChartTypeOptions,
      supportedChartTypes: widget.supportedChartTypes,
      filterType: TimeFilterType.month,
      startDate: DateTime.now().subtract(const Duration(days: 30)),
      endDate: DateTime.now(),
      currentChartType: widget.currentChartType,
      chartIcon: Icons.timer,
      chartType: widget.currentChartType,
      advancedFilterOptions: widget.advancedFilterOptions,
    );
  }

  /// بناء المخطط
  Widget _buildChart() {
    if (widget.data.isEmpty) {
      return UnifiedFilterExportWidget.buildNoDataMessage(
        context,
        message: 'لا توجد بيانات للعرض.\nقد يكون ذلك بسبب الفلتر المطبق.',
        onCancelFilter: () {
          // إلغاء الفلتر وإعادة تعيينه إلى الكل
          if (widget.onFilterChanged != null) {
            widget.onFilterChanged!(null, null, TimeFilterType.all);
          }
        },
      );
    }

    switch (widget.currentChartType) {
      case ChartType.bar:
        return _buildBarChart();
      case ChartType.line:
        return _buildLineChart();
      case ChartType.boxplot:
        return _buildBoxPlotChart();
      default:
        return _buildBarChart();
    }
  }

  /// بناء مخطط شريطي باستخدام Syncfusion Charts
  Widget _buildBarChart() {
    final defaultColors = [
      AppColors.primary,
      AppColors.statusCompleted,
      AppColors.statusInProgress,
      AppColors.statusPending,
      AppColors.priorityHigh,
      AppColors.priorityMedium,
      AppColors.priorityLow,
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.shade400, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: SfCartesianChart(
        // تفعيل الميزات التفاعلية المتقدمة من Syncfusion
        enableAxisAnimation: true,
        enableSideBySideSeriesPlacement: false,

        // تفعيل Tooltip المتقدم
        tooltipBehavior: TooltipBehavior(
          enable: true,
          format: 'point.x: point.y ساعة',
          header: '',
          canShowMarker: true,
          activationMode: ActivationMode.singleTap,
          animationDuration: 500,
          borderColor: Colors.blue,
          borderWidth: 2,
        ),

        // تفعيل Trackball المتقدم
        trackballBehavior: TrackballBehavior(
          enable: true,
          activationMode: ActivationMode.singleTap,
          lineType: TrackballLineType.vertical,
          tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
          markerSettings: const TrackballMarkerSettings(
            markerVisibility: TrackballVisibilityMode.visible,
            height: 8,
            width: 8,
            borderWidth: 2,
          ),
        ),

        // إعدادات المفتاح
        legend: Legend(
          isVisible: false,
        ),

        // إعدادات المحاور
        primaryXAxis: CategoryAxis(
          majorGridLines: const MajorGridLines(width: 0),
          axisLine: AxisLine(
            color: Colors.grey.withValues(alpha: 0.5),
            width: 1,
          ),
          labelStyle: const TextStyle(
            fontSize: 10,
          ),
        ),

        primaryYAxis: NumericAxis(
          title: AxisTitle(text: 'متوسط وقت الإكمال (ساعة)'),
          majorGridLines: MajorGridLines(
            width: 1,
            color: Colors.grey.withValues(alpha: 0.3),
          ),
          axisLine: AxisLine(
            color: Colors.grey.withValues(alpha: 0.5),
            width: 1,
          ),
          labelStyle: const TextStyle(
            fontSize: 12,
          ),
        ),

        // إعدادات السلاسل
        series: _buildBarSeries(defaultColors),
      ),
    );
  }

  /// بناء سلاسل المخطط الشريطي
  List<CartesianSeries> _buildBarSeries(List<Color> defaultColors) {
    final List<CartesianSeries> series = [];

    // تحويل البيانات إلى تنسيق Syncfusion
    final List<_CompletionTimeChartData> chartData = [];
    for (int i = 0; i < widget.data.length; i++) {
      final data = widget.data[i];
      final color = data.color ??
          widget.colors?[i % (widget.colors?.length ?? 1)] ??
          defaultColors[i % defaultColors.length];

      chartData.add(_CompletionTimeChartData(
        data.categoryName,
        data.averageCompletionTime,
        color,
      ));
    }

    series.add(
      ColumnSeries<_CompletionTimeChartData, String>(
        name: 'متوسط وقت الإكمال',
        dataSource: chartData,
        xValueMapper: (_CompletionTimeChartData data, _) => data.categoryName,
        yValueMapper: (_CompletionTimeChartData data, _) => data.value,
        pointColorMapper: (_CompletionTimeChartData data, _) => data.color,

        // إعدادات التفاعل
        enableTooltip: true,
        animationDuration: 1000,

        // تفعيل التحديد
        selectionBehavior: SelectionBehavior(
          enable: true,
        ),
      ),
    );

    return series;
  }

  /// بناء مخطط خطي باستخدام Syncfusion Charts
  Widget _buildLineChart() {
    final defaultColors = [
      AppColors.primary,
      AppColors.statusCompleted,
      AppColors.statusInProgress,
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.shade400, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: SfCartesianChart(
        // تفعيل الميزات التفاعلية المتقدمة من Syncfusion
        enableAxisAnimation: true,
        enableSideBySideSeriesPlacement: false,

        // تفعيل Tooltip المتقدم
        tooltipBehavior: TooltipBehavior(
          enable: true,
          format: 'point.x: point.y ساعة',
          header: '',
          canShowMarker: true,
          activationMode: ActivationMode.singleTap,
          animationDuration: 500,
          borderColor: Colors.blue,
          borderWidth: 2,
        ),

        // تفعيل Trackball المتقدم
        trackballBehavior: TrackballBehavior(
          enable: true,
          activationMode: ActivationMode.singleTap,
          lineType: TrackballLineType.vertical,
          tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
          markerSettings: const TrackballMarkerSettings(
            markerVisibility: TrackballVisibilityMode.visible,
            height: 8,
            width: 8,
            borderWidth: 2,
          ),
        ),

        // إعدادات المفتاح
        legend: Legend(
          isVisible: false,
        ),

        // إعدادات المحاور
        primaryXAxis: CategoryAxis(
          majorGridLines: const MajorGridLines(width: 0),
          axisLine: AxisLine(
            color: Colors.grey.withValues(alpha: 0.5),
            width: 1,
          ),
          labelStyle: const TextStyle(
            fontSize: 10,
          ),
        ),

        primaryYAxis: NumericAxis(
          title: AxisTitle(text: 'متوسط وقت الإكمال (ساعة)'),
          majorGridLines: MajorGridLines(
            width: 1,
            color: Colors.grey.withValues(alpha: 0.3),
          ),
          axisLine: AxisLine(
            color: Colors.grey.withValues(alpha: 0.5),
            width: 1,
          ),
          labelStyle: const TextStyle(
            fontSize: 12,
          ),
        ),

        // إعدادات السلاسل
        series: _buildLineSeries(defaultColors),
      ),
    );
  }

  /// بناء سلاسل المخطط الخطي
  List<CartesianSeries> _buildLineSeries(List<Color> defaultColors) {
    final List<CartesianSeries> series = [];

    // تحويل البيانات إلى تنسيق Syncfusion
    final List<_CompletionTimeChartData> chartData = [];
    for (int i = 0; i < widget.data.length; i++) {
      final data = widget.data[i];
      chartData.add(_CompletionTimeChartData(
        data.categoryName,
        data.averageCompletionTime,
        defaultColors[0],
      ));
    }

    series.add(
      LineSeries<_CompletionTimeChartData, String>(
        name: 'متوسط وقت الإكمال',
        dataSource: chartData,
        xValueMapper: (_CompletionTimeChartData data, _) => data.categoryName,
        yValueMapper: (_CompletionTimeChartData data, _) => data.value,
        color: defaultColors[0],
        width: 3,

        // إعدادات النقاط
        markerSettings: MarkerSettings(
          isVisible: true,
          height: 6,
          width: 6,
          borderColor: defaultColors[0],
          borderWidth: 2,
        ),

        // إعدادات التفاعل
        enableTooltip: true,
        animationDuration: 1000,

        // تفعيل التحديد
        selectionBehavior: SelectionBehavior(
          enable: true,
          selectedColor: defaultColors[0].withValues(alpha: 0.8),
          unselectedColor: defaultColors[0].withValues(alpha: 0.3),
        ),
      ),
    );

    return series;
  }

  /// بناء مخطط صندوقي
  Widget _buildBoxPlotChart() {
    // تنفيذ مخطط صندوقي لوقت الإكمال
    return const Center(
      child: Text('مخطط صندوقي لوقت الإكمال (قيد التطوير)'),
    );
  }

  /// بناء مفتاح المخطط
  Widget _buildLegend() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildLegendItem(
          'متوسط وقت الإكمال (ساعة)',
          AppColors.primary,
        ),
        const SizedBox(width: 16),
        _buildLegendItem(
          'عدد المهام',
          AppColors.statusCompleted,
        ),
      ],
    );
  }

  /// بناء عنصر مفتاح المخطط
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: AppStyles.caption,
        ),
      ],
    );
  }
}
