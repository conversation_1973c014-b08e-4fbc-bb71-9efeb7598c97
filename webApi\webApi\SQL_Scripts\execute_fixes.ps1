# ===================================================================
# PowerShell Script لتنفيذ إصلاحات قاعدة البيانات تلقائياً
# تاريخ الإنشاء: 2025-01-05
# الهدف: تنفيذ جميع إصلاحات UserPermissions بسهولة
# ===================================================================

param(
    [string]$ServerName = "localhost",
    [string]$DatabaseName = "TaskManagementDB",
    [string]$ConnectionString = "",
    [switch]$VerifyOnly = $false,
    [switch]$RollbackMode = $false
)

# إعداد الألوان للرسائل
$Host.UI.RawUI.ForegroundColor = "White"

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️ $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "🔍 $Message" -ForegroundColor Cyan
}

function Write-Header {
    param([string]$Message)
    Write-Host ""
    Write-Host "=================================================="
    Write-Host "🚀 $Message" -ForegroundColor Magenta
    Write-Host "=================================================="
}

# التحقق من وجود SQL Server Module
Write-Info "التحقق من وجود SQL Server PowerShell Module..."
if (-not (Get-Module -ListAvailable -Name SqlServer)) {
    Write-Warning "SQL Server PowerShell Module غير مثبت"
    Write-Info "جاري تثبيت SQL Server Module..."
    try {
        Install-Module -Name SqlServer -Force -AllowClobber -Scope CurrentUser
        Write-Success "تم تثبيت SQL Server Module بنجاح"
    }
    catch {
        Write-Error "فشل في تثبيت SQL Server Module: $($_.Exception.Message)"
        Write-Info "يرجى تثبيته يدوياً: Install-Module -Name SqlServer"
        exit 1
    }
}

Import-Module SqlServer -Force

# إعداد Connection String
if ([string]::IsNullOrEmpty($ConnectionString)) {
    $ConnectionString = "Server=$ServerName;Database=$DatabaseName;Integrated Security=True;TrustServerCertificate=True;"
}

Write-Header "بدء تنفيذ إصلاحات UserPermissions"
Write-Info "الخادم: $ServerName"
Write-Info "قاعدة البيانات: $DatabaseName"
Write-Info "الوقت: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

# التحقق من الاتصال بقاعدة البيانات
Write-Info "اختبار الاتصال بقاعدة البيانات..."
try {
    $testQuery = "SELECT 1"
    Invoke-Sqlcmd -ConnectionString $ConnectionString -Query $testQuery -QueryTimeout 10 | Out-Null
    Write-Success "تم الاتصال بقاعدة البيانات بنجاح"
}
catch {
    Write-Error "فشل في الاتصال بقاعدة البيانات: $($_.Exception.Message)"
    Write-Info "تحقق من:"
    Write-Info "1. اسم الخادم: $ServerName"
    Write-Info "2. اسم قاعدة البيانات: $DatabaseName"
    Write-Info "3. صلاحيات الوصول"
    exit 1
}

# تحديد السكريبت المطلوب تنفيذه
$ScriptPath = ""
$ScriptName = ""

if ($RollbackMode) {
    $ScriptName = "rollback_user_permissions_fixes.sql"
    $ScriptPath = Join-Path $PSScriptRoot $ScriptName
    Write-Warning "وضع التراجع مُفعل - سيتم التراجع عن جميع الإصلاحات!"
    $confirmation = Read-Host "هل أنت متأكد من التراجع؟ (yes/no)"
    if ($confirmation -ne "yes") {
        Write-Info "تم إلغاء العملية"
        exit 0
    }
}
elseif ($VerifyOnly) {
    $ScriptName = "verify_database_structure.sql"
    $ScriptPath = Join-Path $PSScriptRoot $ScriptName
    Write-Info "وضع التحقق مُفعل - سيتم التحقق من البنية فقط"
}
else {
    $ScriptName = "execute_all_fixes.sql"
    $ScriptPath = Join-Path $PSScriptRoot $ScriptName
    Write-Info "وضع التنفيذ مُفعل - سيتم تطبيق جميع الإصلاحات"
}

# التحقق من وجود السكريبت
if (-not (Test-Path $ScriptPath)) {
    Write-Error "السكريبت غير موجود: $ScriptPath"
    Write-Info "تأكد من وجود الملف في المجلد الصحيح"
    exit 1
}

Write-Info "السكريبت المحدد: $ScriptName"

# قراءة محتوى السكريبت
try {
    $SqlScript = Get-Content -Path $ScriptPath -Raw -Encoding UTF8
    Write-Success "تم قراءة السكريبت بنجاح"
}
catch {
    Write-Error "فشل في قراءة السكريبت: $($_.Exception.Message)"
    exit 1
}

# تنفيذ السكريبت
Write-Header "تنفيذ السكريبت"
try {
    $StartTime = Get-Date
    
    # تنفيذ السكريبت مع إظهار الرسائل
    $Results = Invoke-Sqlcmd -ConnectionString $ConnectionString -Query $SqlScript -QueryTimeout 300 -Verbose
    
    $EndTime = Get-Date
    $Duration = $EndTime - $StartTime
    
    Write-Success "تم تنفيذ السكريبت بنجاح"
    Write-Info "مدة التنفيذ: $($Duration.TotalSeconds) ثانية"
    
    # عرض النتائج إذا وجدت
    if ($Results) {
        Write-Info "نتائج التنفيذ:"
        $Results | Format-Table -AutoSize
    }
}
catch {
    Write-Error "فشل في تنفيذ السكريبت: $($_.Exception.Message)"
    Write-Info "تفاصيل الخطأ:"
    Write-Host $_.Exception.ToString() -ForegroundColor Red
    exit 1
}

# تنفيذ التحقق التلقائي (إذا لم يكن في وضع التحقق بالفعل)
if (-not $VerifyOnly -and -not $RollbackMode) {
    Write-Header "التحقق التلقائي من النتائج"
    
    $VerifyScriptPath = Join-Path $PSScriptRoot "verify_database_structure.sql"
    if (Test-Path $VerifyScriptPath) {
        try {
            $VerifyScript = Get-Content -Path $VerifyScriptPath -Raw -Encoding UTF8
            $VerifyResults = Invoke-Sqlcmd -ConnectionString $ConnectionString -Query $VerifyScript -QueryTimeout 60
            
            Write-Success "تم التحقق من البنية بنجاح"
            if ($VerifyResults) {
                $VerifyResults | Format-Table -AutoSize
            }
        }
        catch {
            Write-Warning "فشل في التحقق التلقائي: $($_.Exception.Message)"
            Write-Info "يمكنك تشغيل التحقق يدوياً: .\execute_fixes.ps1 -VerifyOnly"
        }
    }
}

# الخطوات التالية
Write-Header "الخطوات التالية"

if ($RollbackMode) {
    Write-Info "تم التراجع عن الإصلاحات. الخطوات التالية:"
    Write-Info "1. تحقق من أن التطبيق يعمل"
    Write-Info "2. إذا كنت تريد إعادة تطبيق الإصلاحات: .\execute_fixes.ps1"
}
elseif ($VerifyOnly) {
    Write-Info "تم التحقق من البنية. راجع النتائج أعلاه"
}
else {
    Write-Success "تم تطبيق جميع الإصلاحات بنجاح!"
    Write-Info "الخطوات التالية:"
    Write-Info "1. أعد تشغيل الباك اند (ASP.NET Core API)"
    Write-Info "2. اختبر إضافة صلاحية جديدة من Flutter"
    Write-Info "3. تحقق من عدم ظهور أخطاء في Console"
    Write-Info "4. للتحقق الإضافي: .\execute_fixes.ps1 -VerifyOnly"
}

Write-Header "انتهى التنفيذ بنجاح"
Write-Info "الوقت: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

# إيقاف مؤقت للقراءة
Write-Host ""
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
