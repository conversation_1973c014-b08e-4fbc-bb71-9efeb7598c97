# إصلاح خطأ SignalR: type 'Null' is not a subtype of type 'Object'

## 🔍 **تشخيص المشكلة**

### السبب الجذري:
الخطأ `type 'Null' is not a subtype of type 'Object'` يحدث في SignalR عندما:
1. يتم تمرير قيم `null` كمعاملات لدالة `invoke()` في اتصال SignalR
2. البيانات المستقبلة من الخادم تحتوي على قيم `null` غير متوقعة
3. عدم التحقق من صحة البيانات قبل الإرسال أو الاستقبال

### الملفات المتأثرة:
- `lib/services/unified_signalr_service.dart` - الملف الرئيسي للمشكلة
- `lib/screens/tasks/task_attachments_tab.dart` - معالجة إشعارات المرفقات
- `lib/controllers/task_controller.dart` - إدارة اتصالات SignalR للمهام

## 🛠️ **الحلول المطبقة**

### 1. تحسين دالة `_safeInvoke`
```dart
Future<void> _safeInvoke(HubConnection? connection, String methodName, List<Object?> args, String hubName) async {
  try {
    // التحقق من حالة الاتصال
    if (connection?.state != HubConnectionState.Connected) {
      debugPrint('⚠️ $hubName غير متصل. لا يمكن استدعاء $methodName');
      return;
    }

    // تصفية وتنظيف المعاملات لتجنب خطأ null subtype
    final safeArgs = <Object>[];
    for (final arg in args) {
      if (arg != null) {
        // التأكد من أن المعامل ليس null وقابل للتسلسل
        if (arg is String || arg is int || arg is double || arg is bool || arg is Map || arg is List) {
          safeArgs.add(arg);
        } else {
          // محاولة تحويل الكائن إلى JSON إذا كان يحتوي على toJson
          try {
            if (arg.runtimeType.toString().contains('toJson')) {
              final jsonData = (arg as dynamic).toJson();
              safeArgs.add(jsonData);
            } else {
              safeArgs.add(arg.toString());
            }
          } catch (_) {
            safeArgs.add(arg.toString());
          }
        }
      }
    }

    // استدعاء الدالة مع المعاملات الآمنة
    await connection?.invoke(methodName, args: safeArgs);
    debugPrint('✅ تم استدعاء $methodName في $hubName بنجاح');
    
  } catch (e) {
    final errorMessage = e.toString();
    debugPrint('❌ خطأ في استدعاء $methodName في $hubName: $errorMessage');
    
    // معالجة خاصة لخطأ null subtype
    if (errorMessage.contains('Null\' is not a subtype of type \'Object\'') ||
        errorMessage.contains('type \'Null\' is not a subtype of type \'Object\'')) {
      
      // تسجيل تفصيلي للخطأ
      debugPrint('🔧 [$hubName] خطأ null subtype في $methodName:');
      debugPrint('   📦 المعاملات الأصلية: ${args.map((a) => a?.runtimeType ?? 'null').toList()}');
      debugPrint('   🔍 قيم المعاملات: ${args.map((a) => a?.toString() ?? 'null').take(3).toList()}');
      
      // محاولة ثانية مع معاملات مبسطة
      try {
        final simplifiedArgs = args
            .where((arg) => arg != null && arg.toString().isNotEmpty)
            .map((arg) => arg.toString())
            .toList();
            
        if (simplifiedArgs.isNotEmpty && connection?.state == HubConnectionState.Connected) {
          debugPrint('🔄 محاولة ثانية مع معاملات مبسطة: $simplifiedArgs');
          await connection?.invoke(methodName, args: simplifiedArgs.cast<Object>());
          debugPrint('✅ نجحت المحاولة الثانية لاستدعاء $methodName في $hubName');
        } else {
          debugPrint('⚠️ لا توجد معاملات صالحة للمحاولة الثانية');
        }
      } catch (retryError) {
        debugPrint('❌ فشلت المحاولة الثانية لاستدعاء $methodName: $retryError');
      }
      return;
    }
    
    // عدم إعادة رمي الخطأ لتجنب crash التطبيق
    debugPrint('⚠️ تم تجاهل الخطأ لتجنب crash التطبيق');
  }
}
```

### 2. استبدال جميع استدعاءات `invoke` المباشرة
تم استبدال جميع استدعاءات `connection?.invoke()` المباشرة بـ `_safeInvoke()` في:

- `_joinUserNotificationGroup()`
- `_updateUnreadNotificationsCount()`
- `joinChatGroup()`
- `leaveChatGroup()`
- `joinTaskGroupForAttachments()`
- `leaveTaskGroupForAttachments()`
- `joinTaskGroup()`
- `leaveTaskGroup()`
- `sendTypingIndicator()`
- `sendStoppedTypingIndicator()`
- `sendGroupTypingIndicator()`
- `sendGroupStoppedTypingIndicator()`
- `joinTaskCommentsGroup()`
- `leaveTaskCommentsGroup()`
- `sendTaskComment()`

### 3. تحسين معالجات الأحداث
تم تحسين معالجات الأحداث لتتعامل مع القيم null بشكل آمن:

```dart
// مثال: معالج إضافة المرفقات
_taskHubConnection!.on('AttachmentAdded', (arguments) {
  try {
    if (arguments != null && arguments.isNotEmpty) {
      // التحقق من صحة المعاملات وتجنب null values
      final safeArguments = arguments.where((arg) => arg != null && arg.toString().isNotEmpty).toList();
      if (safeArguments.isNotEmpty) {
        debugPrint('📎 تم استقبال إشعار إضافة مرفق: $safeArguments');
        // سيتم معالجة هذا في TaskAttachmentsTab
      } else {
        debugPrint('⚠️ تم استقبال إشعار إضافة مرفق بمعاملات فارغة');
      }
    }
  } catch (e) {
    debugPrint('❌ خطأ في معالجة إشعار إضافة مرفق: $e');
    // تجاهل الخطأ لتجنب crash التطبيق
  }
});
```

### 4. تحسين دالة `on` للتوافق
```dart
void on(String methodName, Function(dynamic) callback) {
  _chatHubConnection?.on(methodName, (arguments) {
    try {
      if (arguments != null && arguments.isNotEmpty && arguments[0] != null) {
        // التحقق من صحة البيانات قبل استدعاء callback
        final data = arguments[0];
        if (data is Map<String, dynamic> && data.isNotEmpty) {
          callback(data);
        } else if (data is String && data.isNotEmpty) {
          callback(data);
        } else if (data is num || data is bool) {
          callback(data);
        } else {
          debugPrint('⚠️ تم تجاهل بيانات غير صالحة في $methodName: ${data.runtimeType}');
          callback(null);
        }
      } else {
        callback(null);
      }
    } catch (e) {
      debugPrint('❌ خطأ في معالجة SignalR callback لـ $methodName: $e');
      // تجاهل الخطأ وعدم استدعاء callback لتجنب crash
      try {
        callback(null);
      } catch (_) {
        // تجاهل خطأ callback أيضاً
      }
    }
  });
}
```

## 🔧 **إجراءات وقائية إضافية**

### 1. التحقق من البيانات قبل الإرسال
```dart
// مثال: التحقق من صحة بيانات الرسالة
if (message != null && message.toJson().isNotEmpty) {
  await _safeInvoke(_chatHubConnection, "SendTaskMessage", [taskId, message.toJson()], "Chat Hub");
}
```

### 2. معالجة آمنة للبيانات المستقبلة
```dart
// مثال: معالجة رسائل المهام
_chatHubConnection!.on("ReceiveTaskMessage", (arguments) {
  try {
    if (arguments != null && arguments.isNotEmpty && arguments[0] != null) {
      final messageData = arguments[0];
      
      // التحقق من أن البيانات صالحة
      if (messageData is Map<String, dynamic> && messageData.isNotEmpty) {
        // معالجة البيانات
        _newTaskMessages.add(messageData);
        _notifyMessagesControllers(messageData);
        AppLogger.debug("تم استقبال رسالة مهمة جديدة فورياً: ${messageData['id'] ?? 'unknown'}");
      } else {
        AppLogger.warning("تم استقبال رسالة مهمة بتنسيق غير صالح");
      }
    }
  } catch (e) {
    AppLogger.error("خطأ في معالجة رسالة المهمة: $e");
  }
});
```

## 📊 **النتائج المتوقعة**

بعد تطبيق هذه الحلول:

1. ✅ **منع crash التطبيق** - لن يتوقف التطبيق بسبب خطأ null subtype
2. ✅ **تحسين الاستقرار** - معالجة آمنة لجميع اتصالات SignalR
3. ✅ **تسجيل أفضل للأخطاء** - معلومات تشخيصية مفصلة
4. ✅ **إعادة المحاولة التلقائية** - محاولة ثانية مع معاملات مبسطة
5. ✅ **التوافق مع الكود الحالي** - لا حاجة لتغيير الكود الذي يستخدم الخدمة

## 🚀 **اختبار الحلول**

للتأكد من فعالية الحلول:

1. **تشغيل التطبيق** ومراقبة console للرسائل التشخيصية
2. **اختبار إضافة المرفقات** للمهام
3. **اختبار إرسال الرسائل والتعليقات**
4. **مراقبة عدم ظهور خطأ null subtype**

## 📝 **ملاحظات مهمة**

- تم الحفاظ على جميع الوظائف الحالية
- لا حاجة لتغيير الكود في الملفات الأخرى
- الحلول تعمل بشكل شفاف مع الكود الحالي
- تم إضافة تسجيل مفصل لتسهيل التشخيص المستقبلي
