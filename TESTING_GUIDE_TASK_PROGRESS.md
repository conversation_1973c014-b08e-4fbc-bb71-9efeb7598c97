# دليل اختبار تحسينات تقدم المهام

## السيناريوهات المطلوب اختبارها:

### 1. التحديث اليدوي للتقدم
**الخطوات:**
1. افتح تفاصيل أي مهمة
2. اضغط على زر "تحديث التقدم"
3. أدخل قيمة جديدة (مثل 45%)
4. احفظ التحديث

**النتيجة المتوقعة:**
- يجب أن تظهر القيمة الجديدة فوراً في جميع أماكن عرض التقدم
- لا حاجة لإعادة تشغيل التطبيق
- يجب حفظ القيمة في قاعدة البيانات

### 2. تحديث التقدم عند إكمال مهمة فرعية
**الخطوات:**
1. افتح مهمة تحتوي على مهام فرعية
2. لاحظ نسبة التقدم الحالية
3. اضغط على checkbox لإكمال مهمة فرعية
4. لاحظ التغيير في نسبة التقدم

**النتيجة المتوقعة:**
- يجب زيادة التقدم بـ 15% تلقائياً
- يجب ظهور التحديث فوراً في الواجهة
- يجب حفظ التحديث في قاعدة البيانات

### 3. تحديث التقدم عند إضافة تعليق
**الخطوات:**
1. افتح تفاصيل مهمة
2. لاحظ نسبة التقدم الحالية
3. أضف تعليق جديد
4. لاحظ التغيير في نسبة التقدم

**النتيجة المتوقعة:**
- يجب زيادة التقدم بـ 3% تلقائياً
- يجب ظهور التحديث فوراً في الواجهة
- يجب حفظ التحديث في قاعدة البيانات

### 4. تحديث التقدم عند رفع مرفق
**الخطوات:**
1. افتح تبويب المرفقات في تفاصيل المهمة
2. لاحظ نسبة التقدم الحالية
3. ارفع مرفق جديد
4. لاحظ التغيير في نسبة التقدم

**النتيجة المتوقعة:**
- يجب زيادة التقدم بـ 5% تلقائياً
- يجب ظهور التحديث فوراً في الواجهة
- يجب حفظ التحديث في قاعدة البيانات

### 5. تحديث التقدم عند إنشاء مستند
**الخطوات:**
1. افتح تبويب المستندات في تفاصيل المهمة
2. لاحظ نسبة التقدم الحالية
3. أنشئ مستند جديد
4. لاحظ التغيير في نسبة التقدم

**النتيجة المتوقعة:**
- يجب زيادة التقدم بـ 10% تلقائياً
- يجب ظهور التحديث فوراً في الواجهة
- يجب حفظ التحديث في قاعدة البيانات

### 6. تحديث التقدم عند تحويل المهمة
**الخطوات:**
1. افتح مهمة مُسندة لك
2. لاحظ نسبة التقدم الحالية
3. حوّل المهمة لمستخدم آخر
4. لاحظ التغيير في نسبة التقدم

**النتيجة المتوقعة:**
- يجب زيادة التقدم بـ 2% تلقائياً
- يجب عدم إعادة تعيين القيمة لرقم صغير
- يجب ظهور التحديث فوراً في الواجهة

### 7. اختبار آلية "البحث عن أكبر قيمة"
**الخطوات:**
1. قم بعدة تحديثات يدوية للتقدم (مثل 20%, 40%, 60%)
2. أضف تعليق أو مرفق
3. تحقق من أن الزيادة تُضاف للقيمة الأكبر (60%) وليس لقيمة عشوائية

**النتيجة المتوقعة:**
- يجب أن تكون القيمة الجديدة 63% أو 65% (حسب نوع النشاط)
- لا يجب أن تعود القيمة لرقم صغير مثل 5% أو 10%

## نقاط التحقق الإضافية:

### أماكن عرض التقدم التي يجب تحديثها:
1. **تبويب نظرة عامة:** CircularProgressIndicator مع النسبة المئوية
2. **تبويب التقدم:** CircularPercentIndicator الكبير
3. **لوحة المعلومات:** مؤشرات التقدم في البطاقات
4. **قائمة المهام:** مؤشرات التقدم في عناصر القائمة

### رسائل Debug المطلوب مراقبتها:
- `🔍 البحث عن أحدث تقدم للمهمة`
- `📊 التقدم الحالي: X%`
- `🔄 تحديث تلقائي للتقدم: X% → Y%`
- `✅ تم تحديث التقدم بنجاح`
- `✅ تم تحديث تقدم المهمة والواجهة بنجاح`

### اختبار الأداء:
- يجب أن تكون التحديثات سريعة (أقل من ثانية واحدة)
- لا يجب أن تتجمد الواجهة أثناء التحديث
- يجب أن تعمل التحديثات حتى مع الاتصال البطيء

## مشاكل محتملة وحلولها:

### إذا لم يظهر التحديث فوراً:
1. تحقق من رسائل Debug في وحدة التحكم
2. تأكد من أن `update()` و `refresh()` يتم استدعاؤهما
3. تحقق من أن GetBuilder يستخدم الـ id الصحيح

### إذا عادت القيمة لرقم صغير:
1. تحقق من أن `getCurrentTaskProgress()` يعمل بشكل صحيح
2. تأكد من أن API endpoint `/latest` يعمل
3. تحقق من أن قاعدة البيانات تحتوي على البيانات الصحيحة

### إذا لم يتم حفظ التحديث:
1. تحقق من اتصال الشبكة
2. تأكد من أن API endpoints تعمل بشكل صحيح
3. تحقق من صلاحيات المستخدم
