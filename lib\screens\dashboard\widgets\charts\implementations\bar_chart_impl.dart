import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import '../base/base_chart.dart';
import '../base/chart_interface.dart';
import '../../core/chart_data_processor.dart';

/// تنفيذ المخطط الشريطي
/// 
/// يوفر تنفيذ مبسط ومحسن للمخطط الشريطي باستخدام Syncfusion Charts
class BarChartImpl extends BaseChart {
  BarChartImpl({
    required super.data,
    required super.settings,
    required super.theme,
  });

  @override
  Widget buildChart() {
    final processedData = ChartDataProcessor.processData(
      chartType: ChartType.bar,
      rawData: data,
    );

    final chartData = processedData['data'] as Map<String, double>;
    
    if (chartData.isEmpty) {
      return buildEmptyMessage();
    }

    return _buildSyncfusionBarChart(chartData);
  }

  /// بناء المخطط الشريطي باستخدام Syncfusion
  Widget _buildSyncfusionBarChart(Map<String, double> chartData) {
    final dataPoints = chartData.entries
        .map((entry) => BarDataPoint(entry.key, entry.value))
        .toList();

    return SfCartesianChart(
      // إعدادات عامة
      backgroundColor: theme.backgroundColor,
      plotAreaBorderWidth: 0,
      
      // العنوان
      title: showTitle() ? ChartTitle(
        text: getTitle(),
        textStyle: theme.titleStyle,
      ) : null,

      // المحاور
      primaryXAxis: CategoryAxis(
        labelStyle: theme.labelStyle,
        axisLine: const AxisLine(width: 0),
        majorTickLines: const MajorTickLines(size: 0),
        title: _getXAxisTitle(),
      ),
      
      primaryYAxis: NumericAxis(
        labelStyle: theme.labelStyle,
        axisLine: const AxisLine(width: 0),
        majorTickLines: const MajorTickLines(size: 0),
        majorGridLines: MajorGridLines(
          width: _showGrid() ? 1 : 0,
          color: Colors.grey.shade300,
        ),
        title: _getYAxisTitle(),
        numberFormat: _getNumberFormat(),
      ),

      // السلسلة
      series: <ChartSeries<BarDataPoint, String>>[
        ColumnSeries<BarDataPoint, String>(
          dataSource: dataPoints,
          xValueMapper: (BarDataPoint data, _) => data.category,
          yValueMapper: (BarDataPoint data, _) => data.value,
          
          // الألوان
          pointColorMapper: (BarDataPoint data, int index) => 
              getColorByIndex(index),
          
          // التصميم
          borderRadius: BorderRadius.circular(4),
          width: _getBarWidth(),
          spacing: 0.1,
          
          // تسميات البيانات
          dataLabelSettings: DataLabelSettings(
            isVisible: _showDataLabels(),
            textStyle: theme.labelStyle,
            labelAlignment: ChartDataLabelAlignment.top,
            builder: (data, point, series, pointIndex, seriesIndex) {
              return Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: theme.backgroundColor.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  ChartDataProcessor.formatValue(
                    (data as BarDataPoint).value,
                    format: _getValueFormat(),
                  ),
                  style: theme.labelStyle,
                ),
              );
            },
          ),

          // التفاعل
          enableTooltip: true,
        ),
      ],

      // Tooltip
      tooltipBehavior: TooltipBehavior(
        enable: true,
        backgroundColor: theme.primaryColor,
        textStyle: TextStyle(color: theme.backgroundColor),
        builder: (data, point, series, pointIndex, seriesIndex) {
          final barData = data as BarDataPoint;
          return Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: theme.primaryColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  barData.category,
                  style: TextStyle(
                    color: theme.backgroundColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'القيمة: ${ChartDataProcessor.formatValue(barData.value)}',
                  style: TextStyle(color: theme.backgroundColor),
                ),
              ],
            ),
          );
        },
      ),

      // التكبير والتحريك
      zoomPanBehavior: ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
      ),
    );
  }

  @override
  bool validateData(Map<String, dynamic> data) {
    if (!super.validateData(data)) return false;
    
    // التحقق من وجود بيانات صالحة للمخطط الشريطي
    if (data.containsKey('categories') && data.containsKey('values')) {
      final categories = data['categories'];
      final values = data['values'];
      return categories is List && values is List && 
             categories.isNotEmpty && values.isNotEmpty;
    }
    
    // أو التحقق من البيانات كـ Map
    return data.values.any((value) => 
        value is num || (value is String && double.tryParse(value) != null));
  }

  @override
  List<LegendItem> getLegendItems() {
    final processedData = ChartDataProcessor.processData(
      chartType: ChartType.bar,
      rawData: data,
    );
    
    final chartData = processedData['data'] as Map<String, double>;
    
    return chartData.entries.map((entry) {
      final index = chartData.keys.toList().indexOf(entry.key);
      return LegendItem(
        label: entry.key,
        color: getColorByIndex(index),
      );
    }).toList();
  }

  // الحصول على إعدادات المخطط من settings
  AxisTitle? _getXAxisTitle() {
    final title = settings['xAxisTitle'] as String?;
    return title != null ? AxisTitle(text: title, textStyle: theme.labelStyle) : null;
  }

  AxisTitle? _getYAxisTitle() {
    final title = settings['yAxisTitle'] as String?;
    return title != null ? AxisTitle(text: title, textStyle: theme.labelStyle) : null;
  }

  bool _showGrid() => settings['showGrid'] ?? true;
  
  bool _showDataLabels() => settings['showDataLabels'] ?? false;
  
  double _getBarWidth() => settings['barWidth']?.toDouble() ?? 0.8;
  
  String? _getValueFormat() => settings['valueFormat'] as String?;
  
  String? _getNumberFormat() => settings['numberFormat'] as String?;
}

/// نقطة بيانات للمخطط الشريطي
class BarDataPoint {
  final String category;
  final double value;

  BarDataPoint(this.category, this.value);
}
