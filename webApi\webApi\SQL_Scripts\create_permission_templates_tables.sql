-- إنشاء جداول قوالب الصلاحيات
-- Create permission templates tables

-- التحقق من وجود جدول قوالب الصلاحيات وإنشاؤه إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='permission_templates' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[permission_templates] (
        [id] [int] IDENTITY(1,1) NOT NULL,
        [name] [nvarchar](100) NOT NULL,
        [description] [nvarchar](500) NULL,
        [type] [nvarchar](20) NOT NULL,
        [color] [nvarchar](20) NULL,
        [icon] [nvarchar](50) NULL,
        [is_active] [bit] NOT NULL DEFAULT 1,
        [is_default] [bit] NOT NULL DEFAULT 0,
        [created_by] [int] NOT NULL,
        [created_at] [bigint] NOT NULL,
        [updated_at] [bigint] NULL,
        [updated_by] [int] NULL,
        
        CONSTRAINT [PK_permission_templates] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_permission_templates_created_by_users] FOREIGN KEY ([created_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [FK_permission_templates_updated_by_users] FOREIGN KEY ([updated_by]) REFERENCES [dbo].[users] ([id])
    ) ON [PRIMARY]

    -- إضافة فهارس لتحسين الأداء
    CREATE INDEX [IX_permission_templates_type] ON [dbo].[permission_templates] ([type])
    CREATE INDEX [IX_permission_templates_is_active] ON [dbo].[permission_templates] ([is_active])
    CREATE INDEX [IX_permission_templates_is_default] ON [dbo].[permission_templates] ([is_default])
    CREATE INDEX [IX_permission_templates_created_by] ON [dbo].[permission_templates] ([created_by])

    PRINT 'تم إنشاء جدول permission_templates بنجاح'
END
ELSE
BEGIN
    PRINT 'جدول permission_templates موجود بالفعل'
END

-- التحقق من وجود جدول عناصر قوالب الصلاحيات وإنشاؤه إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='permission_template_items' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[permission_template_items] (
        [id] [int] IDENTITY(1,1) NOT NULL,
        [template_id] [int] NOT NULL,
        [permission_id] [int] NOT NULL,
        [is_enabled] [bit] NOT NULL DEFAULT 1,
        
        CONSTRAINT [PK_permission_template_items] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_permission_template_items_template] FOREIGN KEY ([template_id]) REFERENCES [dbo].[permission_templates] ([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_permission_template_items_permission] FOREIGN KEY ([permission_id]) REFERENCES [dbo].[permissions] ([id]),
        CONSTRAINT [UQ_permission_template_items_template_permission] UNIQUE ([template_id], [permission_id])
    ) ON [PRIMARY]

    -- إضافة فهارس لتحسين الأداء
    CREATE INDEX [IX_permission_template_items_template_id] ON [dbo].[permission_template_items] ([template_id])
    CREATE INDEX [IX_permission_template_items_permission_id] ON [dbo].[permission_template_items] ([permission_id])
    CREATE INDEX [IX_permission_template_items_is_enabled] ON [dbo].[permission_template_items] ([is_enabled])

    PRINT 'تم إنشاء جدول permission_template_items بنجاح'
END
ELSE
BEGIN
    PRINT 'جدول permission_template_items موجود بالفعل'
END

-- إدراج القوالب الافتراضية
IF NOT EXISTS (SELECT * FROM [dbo].[permission_templates] WHERE [type] = 'admin')
BEGIN
    DECLARE @CurrentTime BIGINT = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
    
    -- قالب المدير
    INSERT INTO [dbo].[permission_templates] 
    ([name], [description], [type], [color], [icon], [is_active], [is_default], [created_by], [created_at])
    VALUES 
    (N'مدير النظام', N'صلاحيات كاملة لإدارة جميع أجزاء النظام', 'admin', '#f44336', 'admin_panel_settings', 1, 1, 1, @CurrentTime)

    DECLARE @AdminTemplateId INT = SCOPE_IDENTITY()

    -- قالب المشرف
    INSERT INTO [dbo].[permission_templates] 
    ([name], [description], [type], [color], [icon], [is_active], [is_default], [created_by], [created_at])
    VALUES 
    (N'مشرف', N'صلاحيات إدارية محدودة للإشراف على الفرق والمشاريع', 'manager', '#ff9800', 'manage_accounts', 1, 1, 1, @CurrentTime)

    DECLARE @ManagerTemplateId INT = SCOPE_IDENTITY()

    -- قالب الموظف
    INSERT INTO [dbo].[permission_templates] 
    ([name], [description], [type], [color], [icon], [is_active], [is_default], [created_by], [created_at])
    VALUES 
    (N'موظف', N'صلاحيات أساسية للعمل اليومي وإدارة المهام الشخصية', 'employee', '#2196f3', 'person', 1, 1, 1, @CurrentTime)

    DECLARE @EmployeeTemplateId INT = SCOPE_IDENTITY()

    -- قالب المشاهد
    INSERT INTO [dbo].[permission_templates] 
    ([name], [description], [type], [color], [icon], [is_active], [is_default], [created_by], [created_at])
    VALUES 
    (N'مشاهد', N'صلاحيات القراءة فقط لمراجعة البيانات والتقارير', 'viewer', '#4caf50', 'visibility', 1, 1, 1, @CurrentTime)

    DECLARE @ViewerTemplateId INT = SCOPE_IDENTITY()

    PRINT 'تم إدراج القوالب الافتراضية بنجاح'
    PRINT 'معرف قالب المدير: ' + CAST(@AdminTemplateId AS NVARCHAR(10))
    PRINT 'معرف قالب المشرف: ' + CAST(@ManagerTemplateId AS NVARCHAR(10))
    PRINT 'معرف قالب الموظف: ' + CAST(@EmployeeTemplateId AS NVARCHAR(10))
    PRINT 'معرف قالب المشاهد: ' + CAST(@ViewerTemplateId AS NVARCHAR(10))
END

-- التحقق من النتائج
SELECT COUNT(*) as 'عدد القوالب' FROM [dbo].[permission_templates]
SELECT * FROM [dbo].[permission_templates] ORDER BY [type]

-- عرض إحصائيات الجداول
SELECT 
    'permission_templates' as 'الجدول',
    COUNT(*) as 'عدد السجلات'
FROM [dbo].[permission_templates]
UNION ALL
SELECT 
    'permission_template_items' as 'الجدول',
    COUNT(*) as 'عدد السجلات'
FROM [dbo].[permission_template_items]
