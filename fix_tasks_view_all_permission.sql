-- إصلاح صلاحية عرض جميع المهام للمستخدم
-- تاريخ الإنشاء: 2025-01-10
-- الهدف: إضافة صلاحية tasks.view_all للمستخدم ID: 21

-- التحقق من وجود الصلاحية في جدول الصلاحيات
SELECT * FROM permissions WHERE name = 'tasks.view_all';

-- إذا لم توجد الصلاحية، أضفها
INSERT INTO permissions (name, permission_group, description, created_at, updated_at)
SELECT 'tasks.view_all', 'tasks', 'عرض جميع المهام في النظام', GETDATE(), GETDATE()
WHERE NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tasks.view_all');

-- الحصول على معرف الصلاحية
DECLARE @PermissionId INT;
SELECT @PermissionId = id FROM permissions WHERE name = 'tasks.view_all';

-- التحقق من وجود الصلاحية للمستخدم
SELECT * FROM user_permissions WHERE user_id = 21 AND permission_id = @PermissionId;

-- إضافة الصلاحية للمستخدم إذا لم توجد
INSERT INTO user_permissions (user_id, permission_id, is_active, granted_at, expires_at)
SELECT 21, @PermissionId, 1, GETDATE(), NULL
WHERE NOT EXISTS (
    SELECT 1 FROM user_permissions 
    WHERE user_id = 21 AND permission_id = @PermissionId
);

-- تحديث الصلاحية إذا كانت موجودة ولكن غير مفعلة
UPDATE user_permissions 
SET is_active = 1, granted_at = GETDATE()
WHERE user_id = 21 AND permission_id = @PermissionId AND is_active = 0;

-- التحقق من النتيجة النهائية
SELECT 
    u.name as user_name,
    p.name as permission_name,
    up.is_active,
    up.granted_at
FROM user_permissions up
JOIN users u ON up.user_id = u.id
JOIN permissions p ON up.permission_id = p.id
WHERE u.id = 21 AND p.name = 'tasks.view_all';

-- عرض جميع صلاحيات المهام للمستخدم
SELECT 
    p.name as permission_name,
    up.is_active,
    up.granted_at
FROM user_permissions up
JOIN permissions p ON up.permission_id = p.id
WHERE up.user_id = 21 AND p.name LIKE 'tasks.%'
ORDER BY p.name;
