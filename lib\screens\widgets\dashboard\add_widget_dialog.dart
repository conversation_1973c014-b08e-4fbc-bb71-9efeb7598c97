import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

import '../../../services/dashboard_service.dart';
import '../../../models/dashboard_model.dart' as dashboard_model;
import '../../../models/dashboard_models.dart' as dashboard_models;

/// مربع حوار إضافة عنصر جديد إلى لوحة المعلومات
class AddWidgetDialog extends StatefulWidget {
  const AddWidgetDialog({super.key});

  @override
  State<AddWidgetDialog> createState() => _AddWidgetDialogState();
}

class _AddWidgetDialogState extends State<AddWidgetDialog> {
  // نوع العنصر المحدد
  dashboard_model.BasicDashboardWidgetType? _selectedType;

  // عنوان العنصر
  final TextEditingController _titleController = TextEditingController();

  // خدمة لوحة المعلومات
  final DashboardService _dashboardService = Get.find<DashboardService>();

  // حالة الإضافة
  bool _isAdding = false;

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تحديد ألوان حسب السمة - مستوحاة من Monday.com
    final Color accentColor = const Color(0xFF00A9FF); // أزرق Monday.com

    final Color cardColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF292F4C)
        : Colors.white;

    final Color bgColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF181B34)
        : const Color(0xFFF6F7FB);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: cardColor,
      child: Container(
        width: 600,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(
                  Icons.add_box_outlined,
                  color: accentColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'إضافة عنصر جديد',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Get.back(),
                  tooltip: 'إغلاق',
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),

            // حقل العنوان
            TextField(
              controller: _titleController,
              decoration: InputDecoration(
                labelText: 'عنوان العنصر',
                hintText: 'أدخل عنوانًا للعنصر',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.title),
              ),
            ),
            const SizedBox(height: 24),

            // اختيار نوع العنصر
            const Text(
              'اختر نوع العنصر:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // شبكة أنواع العناصر
            GridView.count(
              shrinkWrap: true,
              crossAxisCount: 3,
              childAspectRatio: 1.2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                _buildWidgetTypeButton(
                  title: 'حالة المهام',
                  icon: Icons.pie_chart,
                  color: Colors.blue,
                  type: dashboard_model.BasicDashboardWidgetType.taskStatusChart,
                ),
                _buildWidgetTypeButton(
                  title: 'تقدم المهام',
                  icon: Icons.show_chart,
                  color: Colors.green,
                  type: dashboard_model.BasicDashboardWidgetType.taskProgressChart,
                ),
                _buildWidgetTypeButton(
                  title: 'عدادات المهام',
                  icon: Icons.dashboard,
                  color: Colors.purple,
                  type: dashboard_model.BasicDashboardWidgetType.taskCounters,
                ),
                _buildWidgetTypeButton(
                  title: 'أداء المستخدمين',
                  icon: Icons.people,
                  color: Colors.orange,
                  type: dashboard_model.BasicDashboardWidgetType.userPerformanceChart,
                ),
                _buildWidgetTypeButton(
                  title: 'أداء الأقسام',
                  icon: Icons.business,
                  color: Colors.teal,
                  type: dashboard_model.BasicDashboardWidgetType.departmentPerformanceChart,
                ),
                _buildWidgetTypeButton(
                  title: 'تتبع الوقت',
                  icon: Icons.timer,
                  color: Colors.red,
                  type: dashboard_model.BasicDashboardWidgetType.timeTrackingChart,
                ),
                _buildWidgetTypeButton(
                  title: 'قائمة المهام',
                  icon: Icons.list_alt,
                  color: Colors.indigo,
                  type: dashboard_model.BasicDashboardWidgetType.taskList,
                ),
                _buildWidgetTypeButton(
                  title: 'مؤشر أداء',
                  icon: Icons.speed,
                  color: Colors.amber,
                  type: dashboard_model.BasicDashboardWidgetType.kpi,
                ),
                _buildWidgetTypeButton(
                  title: 'مخصص',
                  icon: Icons.extension,
                  color: Colors.grey,
                  type: dashboard_model.BasicDashboardWidgetType.custom,
                ),
              ],
            ),
            const SizedBox(height: 24),

            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _isAdding ? null : _addWidget,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: accentColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  child: _isAdding
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('إضافة'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر نوع العنصر
  Widget _buildWidgetTypeButton({
    required String title,
    required IconData icon,
    required Color color,
    required dashboard_model.BasicDashboardWidgetType type,
  }) {
    final isSelected = _selectedType == type;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedType = type;
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? color.withValues(alpha: 26) : Colors.transparent, // 0.1 * 255 = 26
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? color : Colors.grey.withValues(alpha: 77), // 0.3 * 255 = 77
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? color : null,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// إضافة العنصر
  void _addWidget() async {
    // التحقق من الإدخال
    if (_titleController.text.trim().isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال عنوان للعنصر',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (_selectedType == null) {
      Get.snackbar(
        'خطأ',
        'يرجى اختيار نوع العنصر',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    setState(() {
      _isAdding = true;
    });

    try {
      // الحصول على لوحة المعلومات الحالية
      final currentDashboard = _dashboardService.currentDashboard;

      // إنشاء إعدادات افتراضية حسب نوع العنصر
      final Map<String, dynamic> settings = _getDefaultSettings(_selectedType!);

      // إنشاء العنصر للخدمة (استخدام النموذج المتوافق مع الخدمة)
      final serviceWidget = dashboard_models.DashboardWidget(
        id: int.parse(const Uuid().v4().replaceAll('-', '').substring(0, 8), radix: 16),
        dashboardId: currentDashboard?.id ?? 1, // سيتم تحديثه لاحقًا
        title: _titleController.text.trim(),
        type: _getTypeString(_selectedType!),
        positionX: 0, // سيتم تحديثه لاحقًا
        positionY: 0, // سيتم تحديثه لاحقًا
        width: _getDefaultWidth(_selectedType!),
        height: _getDefaultHeight(_selectedType!),
        config: _getConfigString(settings),
        isVisible: true,
        createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000, // تحويل إلى ثواني
        updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      );

      // إضافة العنصر
      if (currentDashboard != null) {
        await _dashboardService.addWidget(currentDashboard.id.toString(), serviceWidget);

        Get.back(result: true);
        Get.snackbar(
          'تم',
          'تمت إضافة العنصر بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'خطأ',
          'لا توجد لوحة معلومات حالية',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إضافة العنصر: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isAdding = false;
      });
    }
  }

  /// الحصول على الإعدادات الافتراضية حسب نوع العنصر
  Map<String, dynamic> _getDefaultSettings(dashboard_model.BasicDashboardWidgetType type) {
    switch (type) {
      case dashboard_model.BasicDashboardWidgetType.taskStatusChart:
        return {
          'chartType': 'pie',
          'showLegend': true,
          'showValues': true,
          'showPercentages': true,
        };
      case dashboard_model.BasicDashboardWidgetType.taskProgressChart:
        return {
          'chartType': 'line',
          'showGrid': true,
          'showDots': true,
          'showBelowArea': true,
          'timeRange': 'month',
        };
      case dashboard_model.BasicDashboardWidgetType.taskCounters:
        return {
          'showTotal': true,
          'showCompleted': true,
          'showOverdue': true,
          'showInProgress': true,
        };
      case dashboard_model.BasicDashboardWidgetType.userPerformanceChart:
        return {
          'chartType': 'bar',
          'showGrid': true,
          'showValues': true,
          'maxUsers': 5,
          'sortBy': 'completed',
        };
      case dashboard_model.BasicDashboardWidgetType.departmentPerformanceChart:
        return {
          'chartType': 'bar',
          'showGrid': true,
          'showValues': true,
          'sortBy': 'completed',
        };
      case dashboard_model.BasicDashboardWidgetType.timeTrackingChart:
        return {
          'chartType': 'bar',
          'showGrid': true,
          'showValues': true,
          'timeRange': 'week',
        };
      case dashboard_model.BasicDashboardWidgetType.taskList:
        return {
          'maxItems': 10,
          'showStatus': true,
          'showDueDate': true,
          'showAssignee': true,
          'sortBy': 'dueDate',
          'sortDirection': 'asc',
        };
      case dashboard_model.BasicDashboardWidgetType.kpi:
        return {
          'kpiType': 'completionRate',
          'showIcon': true,
          'showChange': true,
          'timeRange': 'week',
        };
      case dashboard_model.BasicDashboardWidgetType.custom:
        return {
          'chartType': 'custom',
          'dataSource': 'tasks',
        };
    }
  }

  /// الحصول على العرض الافتراضي حسب نوع العنصر
  int _getDefaultWidth(dashboard_model.BasicDashboardWidgetType type) {
    switch (type) {
      case dashboard_model.BasicDashboardWidgetType.taskCounters:
        return 12;
      case dashboard_model.BasicDashboardWidgetType.taskList:
        return 8;
      default:
        return 6;
    }
  }

  /// الحصول على الارتفاع الافتراضي حسب نوع العنصر
  int _getDefaultHeight(dashboard_model.BasicDashboardWidgetType type) {
    switch (type) {
      case dashboard_model.BasicDashboardWidgetType.taskCounters:
        return 2;
      case dashboard_model.BasicDashboardWidgetType.taskList:
        return 6;
      case dashboard_model.BasicDashboardWidgetType.kpi:
        return 2;
      default:
        return 4;
    }
  }

  /// تحويل نوع العنصر إلى نص
  String _getTypeString(dashboard_model.BasicDashboardWidgetType type) {
    switch (type) {
      case dashboard_model.BasicDashboardWidgetType.taskStatusChart:
        return 'taskStatusChart';
      case dashboard_model.BasicDashboardWidgetType.taskProgressChart:
        return 'taskProgressChart';
      case dashboard_model.BasicDashboardWidgetType.taskCounters:
        return 'taskCounters';
      case dashboard_model.BasicDashboardWidgetType.userPerformanceChart:
        return 'userPerformanceChart';
      case dashboard_model.BasicDashboardWidgetType.departmentPerformanceChart:
        return 'departmentPerformanceChart';
      case dashboard_model.BasicDashboardWidgetType.timeTrackingChart:
        return 'timeTrackingChart';
      case dashboard_model.BasicDashboardWidgetType.taskList:
        return 'taskList';
      case dashboard_model.BasicDashboardWidgetType.kpi:
        return 'kpi';
      case dashboard_model.BasicDashboardWidgetType.custom:
        return 'custom';
    }
  }

  /// تحويل الإعدادات إلى نص JSON
  String _getConfigString(Map<String, dynamic> settings) {
    try {
      return settings.toString();
    } catch (e) {
      return '{}';
    }
  }
}
