import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:syncfusion_flutter_charts/charts.dart';


/// نموذج بيانات لمجموعة في المخطط التراكمي
class StackedBarChartGroup {
  final String x;
  final Map<String, double> values;

  StackedBarChartGroup({required this.x, required this.values});
}

class EnhancedStackedBarChart extends StatelessWidget {
  final String title;
  final List<StackedBarChartGroup> data;

  const EnhancedStackedBarChart({
    super.key,
    required this.title,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    final allKeys = data.expand((g) => g.values.keys).toSet().toList();
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(title, style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 24),
            Expanded(
              child: SfCartesianChart(
                legend: Legend(isVisible: true, position: LegendPosition.bottom),
                primaryXAxis: CategoryAxis(),
                primaryYAxis: NumericAxis(),
                series: _buildStackedBarSeries(allKeys),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<StackedBarSeries<StackedBarChartGroup, String>> _buildStackedBarSeries(List<String> allKeys) {
    return allKeys.map((key) {
      return StackedBarSeries<StackedBarChartGroup, String>(
        dataSource: data,
        xValueMapper: (group, _) => group.x,
        yValueMapper: (group, _) => group.values[key] ?? 0,
        color: _getColor(allKeys.indexOf(key)),
        name: key,
        borderRadius: const BorderRadius.all(Radius.zero),
      );
    }).toList();
  }

  Color _getColor(int index) {
    final colors = [
      AppColors.primary,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
    ];
    return colors[index % colors.length];
  }
}