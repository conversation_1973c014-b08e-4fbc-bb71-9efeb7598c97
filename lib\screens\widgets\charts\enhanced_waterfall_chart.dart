import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/chart_enums.dart';

import '../../../constants/app_styles.dart';

import 'unified_filter_export_widget.dart';

/// نموذج بيانات الشلال
class WaterfallItem {
  /// اسم العنصر
  final String label;

  /// قيمة العنصر
  final double value;

  /// هل هو عنصر إجمالي
  final bool isTotal;

  /// لون العنصر (اختياري)
  final Color? color;

  /// إنشاء عنصر شلال
  const WaterfallItem({
    required this.label,
    required this.value,
    this.isTotal = false,
    this.color,
  });
}

/// مكون مخطط الشلال المحسن
///
/// يوفر هذا المكون مخططًا شلاليًا لعرض التغيرات التراكمية في القيم
/// مفيد لتحليل البيانات المالية وتأثير العوامل المختلفة على النتيجة النهائية
class EnhancedWaterfallChart extends StatefulWidget {
  /// بيانات المخطط
  final Map<String, double> data;

  /// عنوان المخطط
  final String? title;

  /// عنوان المحور السيني
  final String? xAxisTitle;

  /// عنوان المحور الصادي
  final String? yAxisTitle;

  /// ألوان العناصر (اختياري)
  final Map<String, Color>? itemColors;

  /// لون العناصر الإيجابية
  final Color positiveColor;

  /// لون العناصر السلبية
  final Color negativeColor;

  /// لون العناصر الإجمالية
  final Color totalColor;

  /// هل يتم عرض القيم
  final bool showValues;

  /// هل يتم عرض الشبكة
  final bool showGrid;

  /// هل يتم عرض وسيلة الإيضاح
  final bool showLegend;

  /// دالة استدعاء عند تغيير التصفية
  final Function(DateTime?, DateTime?, TimeFilterType)? onFilterChanged;

  /// دالة استدعاء عند التصدير
  final Function(String)? onExport;

  /// دالة استدعاء عند تغيير نوع المخطط
  final Function(ChartType)? onChartTypeChanged;

  /// هل يتم عرض خيارات التصفية
  final bool showFilterOptions;

  /// هل يتم عرض خيارات التصدير
  final bool showExportOptions;

  /// هل يتم عرض خيارات نوع المخطط
  final bool showChartTypeOptions;

  /// تنسيقات التصدير المدعومة
  final List<String> supportedExportFormats;

  /// أنواع المخططات المدعومة
  final List<ChartType> supportedChartTypes;

  /// قائمة العناصر الإجمالية (اختياري)
  final List<String> totalItems;

  /// إنشاء مكون مخطط الشلال المحسن
  const EnhancedWaterfallChart({
    super.key,
    required this.data,
    this.title,
    this.xAxisTitle,
    this.yAxisTitle,
    this.itemColors,
    this.positiveColor = Colors.green,
    this.negativeColor = Colors.red,
    this.totalColor = Colors.blue,
    this.showValues = true,
    this.showGrid = true,
    this.showLegend = true,
    this.onFilterChanged,
    this.onExport,
    this.onChartTypeChanged,
    this.showFilterOptions = false,
    this.showExportOptions = false,
    this.showChartTypeOptions = false,
    this.supportedExportFormats = const ['pdf', 'excel', 'csv', 'png'],
    this.supportedChartTypes = const [
      ChartType.waterfall,
      ChartType.bar,
    ],
    this.totalItems = const [],
  });

  @override
  State<EnhancedWaterfallChart> createState() => _EnhancedWaterfallChartState();
}

class _EnhancedWaterfallChartState extends State<EnhancedWaterfallChart> {
  @override
  Widget build(BuildContext context) {
    // التحقق من وجود بيانات
    if (widget.data.isEmpty) {
      return UnifiedFilterExportWidget.buildNoDataMessage(
        context,
        message: 'لا توجد بيانات للعرض.\nقد يكون ذلك بسبب الفلتر المطبق.',
        onCancelFilter: () {
          // إلغاء الفلتر وإعادة تعيينه إلى الكل
          if (widget.onFilterChanged != null) {
            widget.onFilterChanged!(null, null, TimeFilterType.all);
          }
        },
      );
    }

    return Column(
      children: [
        // مكون التصفية والتصدير
        if (widget.showFilterOptions ||
            widget.showExportOptions ||
            widget.showChartTypeOptions)
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.end,
          //   children: [
          //     if (widget.showFilterOptions)
          //       IconButton(
          //         icon: const Icon(Icons.filter_list_alt),
          //         onPressed: () {
          //           // Show filter options
          //         },
          //         tooltip: 'تصفية',
          //       ),
          //     if (widget.showExportOptions)
          //       IconButton(
          //         icon: const Icon(Icons.download),
          //         onPressed: () {
          //           // Show export options
          //         },
          //         tooltip: 'تصدير',
          //       ),
          //     if (widget.showChartTypeOptions)
          //       IconButton(
          //         icon: const Icon(Icons.bar_chart),
          //         onPressed: () {
          //           // Show chart type options
          //         },
          //         tooltip: 'تغيير نوع المخطط',
          //       ),
          //   ],
          // ),

          // عنوان المخطط
          if (widget.title != null)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                widget.title!,
                style: AppStyles.titleMedium,
                textAlign: TextAlign.center,
              ),
            ),

        // مخطط الشلال
        Expanded(
          child: _buildWaterfallChart(),
        ),
      ],
    );
  }

  /// بناء مخطط الشلال
  Widget _buildWaterfallChart() {
    // تحويل البيانات إلى قائمة من عناصر الشلال
    final items = _prepareWaterfallData();

    return LayoutBuilder(
      builder: (context, constraints) {
        return _WaterfallLayout(
          items: items,
          width: constraints.maxWidth,
          height: constraints.maxHeight,
          showValues: widget.showValues,
          showGrid: widget.showGrid,
          showLegend: widget.showLegend,
          xAxisTitle: widget.xAxisTitle,
          yAxisTitle: widget.yAxisTitle,
        );
      },
    );
  }

  /// إعداد بيانات الشلال
  List<WaterfallItem> _prepareWaterfallData() {
    final items = <WaterfallItem>[];

    // إضافة العناصر مع حساب المجموع التراكمي
    for (final entry in widget.data.entries) {
      final isTotal = widget.totalItems.contains(entry.key);
      final color = widget.itemColors?[entry.key] ??
          (isTotal
              ? widget.totalColor
              : (entry.value >= 0
                  ? widget.positiveColor
                  : widget.negativeColor));

      items.add(WaterfallItem(
        label: entry.key,
        value: entry.value,
        isTotal: isTotal,
        color: color,
      ));
    }

    return items;
  }
}

/// مكون تخطيط الشلال
class _WaterfallLayout extends StatelessWidget {
  final List<WaterfallItem> items;
  final double width;
  final double height;
  final bool showValues;
  final bool showGrid;
  final bool showLegend;
  final String? xAxisTitle;
  final String? yAxisTitle;

  const _WaterfallLayout({
    required this.items,
    required this.width,
    required this.height,
    required this.showValues,
    required this.showGrid,
    required this.showLegend,
    this.xAxisTitle,
    this.yAxisTitle,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(width, height),
      painter: _WaterfallPainter(
        items: items,
        showValues: showValues,
        showGrid: showGrid,
        xAxisTitle: xAxisTitle,
        yAxisTitle: yAxisTitle,
      ),
    );
  }
}

/// رسام الشلال
class _WaterfallPainter extends CustomPainter {
  final List<WaterfallItem> items;
  final bool showValues;
  final bool showGrid;
  final String? xAxisTitle;
  final String? yAxisTitle;

  _WaterfallPainter({
    required this.items,
    required this.showValues,
    required this.showGrid,
    this.xAxisTitle,
    this.yAxisTitle,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (items.isEmpty) return;

    final chartArea = Rect.fromLTWH(
      60, // هامش للمحور الصادي
      20, // هامش علوي
      size.width - 80, // عرض المخطط مع هامش
      size.height - 60, // ارتفاع المخطط مع هامش
    );

    // رسم المحاور
    _drawAxes(canvas, chartArea);

    // رسم الشبكة إذا كان مطلوبًا
    if (showGrid) {
      _drawGrid(canvas, chartArea);
    }

    // حساب القيم القصوى والدنيا
    double minValue = 0;
    double maxValue = 0;
    double runningTotal = 0;

    for (final item in items) {
      if (item.isTotal) {
        // استخدام القيمة المطلقة للعنصر الإجمالي
        maxValue = maxValue.abs() > item.value.abs() ? maxValue : item.value;
        minValue = minValue.abs() > item.value.abs() && item.value < 0
            ? minValue
            : (item.value < 0 ? item.value : minValue);
      } else {
        runningTotal += item.value;
        maxValue = maxValue > runningTotal ? maxValue : runningTotal;
        minValue = minValue < runningTotal && runningTotal < 0
            ? minValue
            : (runningTotal < 0 ? runningTotal : minValue);
      }
    }

    // إضافة هامش للقيم القصوى والدنيا
    maxValue = maxValue * 1.1;
    minValue = minValue * 1.1;

    // التأكد من أن الحد الأدنى أقل من الصفر إذا كانت هناك قيم سالبة
    if (minValue > 0 && items.any((item) => item.value < 0)) {
      minValue = -maxValue * 0.1;
    }

    // رسم الأعمدة
    _drawBars(canvas, chartArea, minValue, maxValue);

    // رسم عناوين المحاور
    if (xAxisTitle != null) {
      _drawXAxisTitle(canvas, chartArea, xAxisTitle!);
    }

    if (yAxisTitle != null) {
      _drawYAxisTitle(canvas, chartArea, yAxisTitle!);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;

  /// رسم المحاور
  void _drawAxes(Canvas canvas, Rect chartArea) {
    final paint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // رسم المحور السيني
    canvas.drawLine(
      Offset(chartArea.left, chartArea.bottom),
      Offset(chartArea.right, chartArea.bottom),
      paint,
    );

    // رسم المحور الصادي
    canvas.drawLine(
      Offset(chartArea.left, chartArea.top),
      Offset(chartArea.left, chartArea.bottom),
      paint,
    );
  }

  /// رسم الشبكة
  void _drawGrid(Canvas canvas, Rect chartArea) {
    final paint = Paint()
      ..color = Colors.grey.withAlpha(76) // 0.3 * 255 = 76
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // رسم خطوط الشبكة الأفقية
    for (int i = 1; i <= 5; i++) {
      final y = chartArea.top + (chartArea.height * i / 5);
      canvas.drawLine(
        Offset(chartArea.left, y),
        Offset(chartArea.right, y),
        paint,
      );
    }
  }

  /// رسم الأعمدة
  void _drawBars(
      Canvas canvas, Rect chartArea, double minValue, double maxValue) {
    final barWidth = chartArea.width / (items.length * 2);
    double runningTotal = 0;
    double previousTotal = 0;

    for (int i = 0; i < items.length; i++) {
      final item = items[i];

      // حساب موقع العمود
      final x = chartArea.left + (i * 2 + 1) * barWidth;

      // تحديد ارتفاع العمود
      double barHeight;
      double barBottom;
      double barTop;

      if (item.isTotal) {
        // العناصر الإجمالية تبدأ من الصفر
        barHeight =
            _normalizeValue(item.value, minValue, maxValue) * chartArea.height;
        barBottom = chartArea.bottom;
        if (item.value >= 0) {
          barTop = barBottom - barHeight;
        } else {
          barTop = barBottom;
          barBottom = barTop + barHeight;
        }
      } else {
        // العناصر العادية تبدأ من المجموع السابق
        previousTotal = runningTotal;
        runningTotal += item.value;

        final previousY = chartArea.bottom -
            _normalizeValue(previousTotal, minValue, maxValue) *
                chartArea.height;
        final currentY = chartArea.bottom -
            _normalizeValue(runningTotal, minValue, maxValue) *
                chartArea.height;

        barTop = currentY < previousY ? currentY : previousY;
        barBottom = currentY > previousY ? currentY : previousY;
        barHeight = (barBottom - barTop).abs();
      }

      // رسم العمود
      final paint = Paint()
        ..color = item.color ?? (item.value >= 0 ? Colors.green : Colors.red)
        ..style = PaintingStyle.fill;

      canvas.drawRect(
        Rect.fromLTWH(x - barWidth / 2, barTop, barWidth, barHeight),
        paint,
      );

      // رسم حدود العمود
      final borderPaint = Paint()
        ..color = Colors.black
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      canvas.drawRect(
        Rect.fromLTWH(x - barWidth / 2, barTop, barWidth, barHeight),
        borderPaint,
      );

      // رسم التسمية
      _drawLabel(canvas, item.label, x, chartArea.bottom + 10);

      // رسم القيمة إذا كان مطلوبًا
      if (showValues) {
        _drawValue(canvas, item.value.toStringAsFixed(1), x, barTop - 10);
      }
    }
  }

  /// تطبيع القيمة بين الحد الأدنى والحد الأقصى
  double _normalizeValue(double value, double min, double max) {
    if (max == min) return 0.5;
    return (value - min) / (max - min);
  }

  /// رسم التسمية
  void _drawLabel(Canvas canvas, String label, double x, double y) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: label,
        style: const TextStyle(
          color: Colors.black,
          fontSize: 10,
        ),
      ),
      textDirection: TextDirection.rtl,
    );

    textPainter.layout(maxWidth: 50);

    textPainter.paint(
      canvas,
      Offset(x - textPainter.width / 2, y),
    );
  }

  /// رسم القيمة
  void _drawValue(Canvas canvas, String value, double x, double y) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: value,
        style: const TextStyle(
          color: Colors.black,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.rtl,
    );

    textPainter.layout();

    textPainter.paint(
      canvas,
      Offset(x - textPainter.width / 2, y),
    );
  }

  /// رسم عنوان المحور السيني
  void _drawXAxisTitle(Canvas canvas, Rect chartArea, String title) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: title,
        style: const TextStyle(
          color: Colors.black,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.rtl,
    );

    textPainter.layout();

    textPainter.paint(
      canvas,
      Offset(
        chartArea.center.dx - textPainter.width / 2,
        chartArea.bottom + 30,
      ),
    );
  }

  /// رسم عنوان المحور الصادي
  void _drawYAxisTitle(Canvas canvas, Rect chartArea, String title) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: title,
        style: const TextStyle(
          color: Colors.black,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.rtl,
    );

    textPainter.layout();

    // حفظ حالة الكانفاس
    canvas.save();

    // تدوير الكانفاس لرسم النص عموديًا
    canvas.translate(
        chartArea.left - 40, chartArea.center.dy + textPainter.width / 2);
    canvas.rotate(-3.14159 / 2);

    textPainter.paint(canvas, Offset.zero);

    // استعادة حالة الكانفاس
    canvas.restore();
  }
}
