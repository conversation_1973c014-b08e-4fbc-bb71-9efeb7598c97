USE databasetasks;

-- التحقق النهائي من الإصلاح
SELECT TOP 20
    id, 
    name, 
    description, 
    permission_group,
    CASE 
        WHEN description LIKE '%O%' OR description LIKE '%U%' OR description LIKE '%?%' THEN 'Still Encoded'
        ELSE 'Fixed'
    END as status
FROM permissions 
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60
)
ORDER BY id;

-- عرض السجلات التي لا تزال تحتوي على رموز
SELECT 
    id, 
    name, 
    description
FROM permissions 
WHERE description LIKE '%O%' OR description LIKE '%U%' OR description LIKE '%?%'
ORDER BY id;
