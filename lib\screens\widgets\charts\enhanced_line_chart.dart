import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:flutter_application_2/models/advanced_filter_options.dart';
import 'package:flutter_application_2/models/chart_enums.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../utils/chart_fullscreen_helper.dart';
import 'unified_filter_export_widget.dart';

/// كلاس لتمثيل نقطة بيانات في المخطط
class ChartDataPoint {
  final dynamic x;
  final double y;
  
  ChartDataPoint(this.x, this.y);
}

/// مكون رسم بياني خطي محسن باستخدام Syncfusion Charts
class EnhancedLineChart extends StatefulWidget {
  /// بيانات الرسم البياني
  final Map<String, Map<String, double>> data;

  /// ألوان الخطوط (اختياري)
  final Map<String, Color>? lineColors;

  /// عنوان المخطط
  final String? title;

  /// عنوان المحور السيني
  final String? xAxisTitle;

  /// عنوان المحور الصادي
  final String? yAxisTitle;

  /// إظهار الشبكة
  final bool showGrid;

  /// إظهار النقاط
  final bool showDots;

  /// إظهار المنطقة تحت الخط
  final bool showBelowArea;

  /// تنسيق X كتاريخ
  final bool formatXAsDate;

  /// تنسيق التاريخ
  final String dateFormat;

  /// معالج تغيير الفلتر
  final Function(DateTime?, DateTime?, TimeFilterType)? onFilterChanged;

  /// معالج التصدير
  final Function(String)? onExport;

  /// إظهار خيارات الفلتر
  final bool showFilterOptions;

  /// إظهار خيارات التصدير
  final bool showExportOptions;

  /// صيغ التصدير المدعومة
  final List<String> supportedExportFormats;

  /// نوع المخطط
  final ChartType chartType;

  /// خيارات الفلتر المتقدمة
  final AdvancedFilterOptions advancedFilterOptions;

  const EnhancedLineChart({
    super.key,
    required this.data,
    this.lineColors,
    this.title,
    this.xAxisTitle,
    this.yAxisTitle,
    this.showGrid = true,
    this.showDots = true,
    this.showBelowArea = true,
    this.formatXAsDate = false,
    this.dateFormat = 'yyyy-MM-dd',
    this.onFilterChanged,
    this.onExport,
    this.showFilterOptions = false,
    this.showExportOptions = false,
    this.supportedExportFormats = const ['pdf', 'excel', 'csv'],
    required this.chartType,
    required this.advancedFilterOptions,
  });

  @override
  State<EnhancedLineChart> createState() => _EnhancedLineChartState();
}

class _EnhancedLineChartState extends State<EnhancedLineChart> {
  List<String> _keys = [];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void didUpdateWidget(EnhancedLineChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.data != widget.data) {
      _initializeData();
    }
  }

  void _initializeData() {
    _keys = widget.data.keys.toList();
  }

  void _handleFilterChanged(DateTime? startDate, DateTime? endDate, TimeFilterType filterType, String chartKey) {
    if (widget.onFilterChanged != null) {
      widget.onFilterChanged!(startDate, endDate, filterType);
    }
  }

  void _handleExport(String format, String chartKey) {
    if (widget.onExport != null) {
      widget.onExport!(format);
    }
  }

  void _handleChartTypeChanged(ChartType newType, String chartKey) {
    // يمكن إضافة منطق تغيير نوع المخطط هنا
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // مكون التصفية والتصدير
        if (widget.showFilterOptions || widget.showExportOptions)
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: UnifiedFilterExportWidget(
              title: widget.title ?? 'رسم بياني خطي',
              chartKey: 'line_chart_syncfusion',
              filterType: TimeFilterType.month,
              chartType: widget.chartType,
              onFilterChanged: _handleFilterChanged,
              onExport: _handleExport,
              onChartTypeChanged: _handleChartTypeChanged,
              showFilter: widget.showFilterOptions,
              showExport: widget.showExportOptions,
              showChartTypeSelector: true,
              showFullscreenButton: true,
              onFullscreenPressed: () {
                ChartFullscreenHelper.showFullscreenChart(
                  context: context,
                  title: widget.title ?? 'رسم بياني خطي',
                  chartContent: _buildChart(), chartKey: '',
                );
              },
              supportedChartTypes: const [
                ChartType.line,
                ChartType.bar,
                ChartType.area,
              ],
              currentChartType: widget.chartType,
              advancedFilterOptions: widget.advancedFilterOptions,
              startDate: null,
              endDate: null,
            ),
          ),

        // العنوان
        if (!widget.showFilterOptions && 
            !widget.showExportOptions &&
            widget.title != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: Text(
              widget.title!,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

        // المخطط
        Expanded(
          child: widget.data.isEmpty
              ? UnifiedFilterExportWidget.buildNoDataMessage(
                  context,
                  message: 'لا توجد بيانات صالحة للعرض.\nقد يكون ذلك بسبب الفلتر المطبق.',
                  onCancelFilter: () {
                    if (widget.onFilterChanged != null) {
                      widget.onFilterChanged!(null, null, TimeFilterType.all);
                    }
                  },
                )
              : _buildChart(),
        ),
      ],
    );
  }

  Widget _buildChart() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.shade400, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      width: double.infinity,
      padding: const EdgeInsets.all(8),
      child: SfCartesianChart(
        // تفعيل الميزات التفاعلية المتقدمة من Syncfusion
        enableAxisAnimation: true,
        enableSideBySideSeriesPlacement: false,
        
        // تفعيل Tooltip المتقدم
        tooltipBehavior: TooltipBehavior(
          enable: true,
          format: 'point.x: point.y',
          header: '',
          canShowMarker: true,
          activationMode: ActivationMode.singleTap,
          animationDuration: 500,
          borderColor: Colors.blue,
          borderWidth: 2,
        ),
        
        // تفعيل Trackball المتقدم
        trackballBehavior: TrackballBehavior(
          enable: true,
          activationMode: ActivationMode.singleTap,
          lineType: TrackballLineType.vertical,
          tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
          markerSettings: const TrackballMarkerSettings(
            markerVisibility: TrackballVisibilityMode.visible,
            height: 8,
            width: 8,
            borderWidth: 2,
          ),
          tooltipSettings: const InteractiveTooltip(
            enable: true,
            color: Colors.black87,
            textStyle: TextStyle(color: Colors.white),
          ),
        ),
        
        // تفعيل Crosshair
        crosshairBehavior: CrosshairBehavior(
          enable: true,
          activationMode: ActivationMode.longPress,
          lineType: CrosshairLineType.both,
          lineColor: Colors.blue.withValues(alpha: 0.7),
          lineWidth: 1,
        ),
        
        // تفعيل Zoom والPan
        zoomPanBehavior: ZoomPanBehavior(
          enablePinching: true,
          enablePanning: true,
          enableDoubleTapZooming: true,
          enableMouseWheelZooming: true,
          zoomMode: ZoomMode.xy,
          maximumZoomLevel: 0.01,
        ),
        
        // إعدادات المفتاح
        legend: Legend(
          isVisible: _keys.isNotEmpty,
          position: LegendPosition.bottom,
          overflowMode: LegendItemOverflowMode.wrap,
          textStyle: const TextStyle(fontSize: 12),
          itemPadding: 8,
        ),
        
        // إعدادات المحاور
        primaryXAxis: widget.formatXAsDate 
            ? DateTimeAxis(
                title: AxisTitle(text: widget.xAxisTitle ?? ''),
                majorGridLines: MajorGridLines(
                  width: widget.showGrid ? 1 : 0,
                  color: Get.isDarkMode ? Colors.white10 : Colors.black12,
                ),
                axisLine: AxisLine(
                  color: Get.isDarkMode ? Colors.white70 : Colors.black54,
                  width: 1,
                ),
                labelStyle: TextStyle(
                  color: Get.isDarkMode ? Colors.white : Colors.black,
                  fontSize: 10,
                ),
                dateFormat: DateFormat(widget.dateFormat),
              )
            : NumericAxis(
                title: AxisTitle(text: widget.xAxisTitle ?? ''),
                majorGridLines: MajorGridLines(
                  width: widget.showGrid ? 1 : 0,
                  color: Get.isDarkMode ? Colors.white10 : Colors.black12,
                ),
                axisLine: AxisLine(
                  color: Get.isDarkMode ? Colors.white70 : Colors.black54,
                  width: 1,
                ),
                labelStyle: TextStyle(
                  color: Get.isDarkMode ? Colors.white : Colors.black,
                  fontSize: 10,
                ),
              ),
        
        primaryYAxis: NumericAxis(
          title: AxisTitle(text: widget.yAxisTitle ?? ''),
          majorGridLines: MajorGridLines(
            width: widget.showGrid ? 1 : 0,
            color: Get.isDarkMode ? Colors.white10 : Colors.black12,
          ),
          axisLine: AxisLine(
            color: Get.isDarkMode ? Colors.white70 : Colors.black54,
            width: 1,
          ),
          labelStyle: TextStyle(
            color: Get.isDarkMode ? Colors.white : Colors.black,
            fontSize: 12,
          ),
        ),
        
        // إعدادات السلاسل
        series: _buildSyncfusionSeries(),
      ),
    );
  }

  /// بناء سلاسل Syncfusion Charts
  List<CartesianSeries> _buildSyncfusionSeries() {
    final List<CartesianSeries> series = [];
    final List<Color> defaultColors = [
      Colors.blue, Colors.green, Colors.red, Colors.purple,
      Colors.orange, Colors.teal, Colors.indigo, Colors.amber,
      Colors.cyan, Colors.pink,
    ];

    for (int i = 0; i < _keys.length; i++) {
      final String key = _keys[i];
      final Color color = widget.lineColors?[key] ?? 
                         defaultColors[i % defaultColors.length];
      
      // تحويل البيانات إلى تنسيق Syncfusion
      final List<ChartDataPoint> chartData = [];
      for (final entry in widget.data.entries) {
        if (entry.value.containsKey(key)) {
          final xValue = widget.formatXAsDate 
              ? DateTime.tryParse(entry.key) ?? DateTime.now()
              : double.tryParse(entry.key) ?? 0.0;
          chartData.add(ChartDataPoint(xValue, entry.value[key]!));
        }
      }

      series.add(
        LineSeries<ChartDataPoint, dynamic>(
          name: key,
          dataSource: chartData,
          xValueMapper: (ChartDataPoint data, _) => data.x,
          yValueMapper: (ChartDataPoint data, _) => data.y,
          color: color,
          width: 3,
          
          // إعدادات النقاط
          markerSettings: MarkerSettings(
            isVisible: widget.showDots,
            height: 6,
            width: 6,
            borderColor: color,
            borderWidth: 2,
          ),
          
          // إعدادات التفاعل
          enableTooltip: true,
          animationDuration: 1000,
          
          // تفعيل التحديد
          selectionBehavior: SelectionBehavior(
            enable: true,
            selectedColor: color.withValues(alpha: 0.8),
            unselectedColor: color.withValues(alpha: 0.3),
          ),
        ),
      );
    }

    return series;
  }
}
