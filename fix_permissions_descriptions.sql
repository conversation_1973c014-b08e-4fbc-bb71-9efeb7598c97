-- سكريبت إصلاح أوصاف الصلاحيات المرمزة
-- تاريخ الإنشاء: 2025-01-06
-- الهدف: إصلاح الأوصاف المرمزة في جدول الصلاحيات

-- بدء المعاملة
BEGIN TRANSACTION;

-- إصلا<PERSON> المجموعة الأولى: السجلات 40-71 (الأوصاف المرمزة الأولى)
UPDATE permissions SET description = 'إدارة لوحة التحكم' WHERE id = 40;
UPDATE permissions SET description = 'عرض المهام' WHERE id = 41;
UPDATE permissions SET description = 'إنشاء مهام جديدة' WHERE id = 42;
UPDATE permissions SET description = 'تعديل المهام' WHERE id = 43;
UPDATE permissions SET description = 'حذف المهام' WHERE id = 44;
UPDATE permissions SET description = 'تعيين المهام للمستخدمين' WHERE id = 45;
UPDATE permissions SET description = 'تحديث المهام الخاصة' WHERE id = 46;
UPDATE permissions SET description = 'عرض المستخدمين' WHERE id = 47;
UPDATE permissions SET description = 'إنشاء مستخدمين جدد' WHERE id = 48;
UPDATE permissions SET description = 'تعديل بيانات المستخدمين' WHERE id = 49;
UPDATE permissions SET description = 'إدارة أدوار المستخدمين' WHERE id = 51;
UPDATE permissions SET description = 'عرض التقارير' WHERE id = 52;
UPDATE permissions SET description = 'إنشاء تقارير جديدة' WHERE id = 53;
UPDATE permissions SET description = 'تصدير التقارير' WHERE id = 54;
UPDATE permissions SET description = 'إدارة النظام' WHERE id = 55;
UPDATE permissions SET description = 'إنشاء نسخ احتياطية' WHERE id = 56;
UPDATE permissions SET description = 'استعادة النسخ الاحتياطية' WHERE id = 57;
UPDATE permissions SET description = 'إدارة قاعدة البيانات' WHERE id = 58;
UPDATE permissions SET description = 'عرض الملف الشخصي' WHERE id = 59;
UPDATE permissions SET description = 'تعديل الملف الشخصي' WHERE id = 60;
UPDATE permissions SET description = 'عرض الإشعارات' WHERE id = 61;
UPDATE permissions SET description = 'إدارة الإشعارات' WHERE id = 62;
UPDATE permissions SET description = 'إدارة التقويم' WHERE id = 64;
UPDATE permissions SET description = 'عرض الأقسام' WHERE id = 65;
UPDATE permissions SET description = 'إدارة الأقسام' WHERE id = 66;
UPDATE permissions SET description = 'عرض جميع المهام' WHERE id = 71;

-- إصلاح المجموعة الثانية: السجلات 1205-1227 (الأوصاف المرمزة الثانية)
UPDATE permissions SET description = 'عرض تفاصيل المهام والانتقال إليها' WHERE id = 1205;
UPDATE permissions SET description = 'تحديث نسبة إنجاز المهام' WHERE id = 1206;
UPDATE permissions SET description = 'تصفية وفلترة المهام' WHERE id = 1207;
UPDATE permissions SET description = 'ترتيب المهام حسب معايير مختلفة' WHERE id = 1208;
UPDATE permissions SET description = 'إدارة لوحة المهام وإضافة أعمدة' WHERE id = 1209;
UPDATE permissions SET description = 'تثبيت وإلغاء تثبيت الرسائل' WHERE id = 1210;
UPDATE permissions SET description = 'تعديل الرسائل المرسلة' WHERE id = 1211;
UPDATE permissions SET description = 'حذف الرسائل' WHERE id = 1212;
UPDATE permissions SET description = 'الرد على الرسائل' WHERE id = 1213;
UPDATE permissions SET description = 'تحديد الرسائل للمتابعة' WHERE id = 1214;
UPDATE permissions SET description = 'تعيين مدير للقسم' WHERE id = 1215;
UPDATE permissions SET description = 'إضافة مستخدمين للقسم' WHERE id = 1216;
UPDATE permissions SET description = 'إزالة مستخدمين من القسم' WHERE id = 1217;
UPDATE permissions SET description = 'إدارة شاملة لمستخدمي القسم' WHERE id = 1218;
UPDATE permissions SET description = 'عرض تقارير المساهمات' WHERE id = 1219;
UPDATE permissions SET description = 'تصدير التقارير كـ PDF' WHERE id = 1220;
UPDATE permissions SET description = 'تقارير عبء العمل والأداء' WHERE id = 1221;
UPDATE permissions SET description = 'تغيير كلمة المرور' WHERE id = 1222;
UPDATE permissions SET description = 'إصلاح قاعدة البيانات' WHERE id = 1223;
UPDATE permissions SET description = 'إنشاء نسخ احتياطية لقاعدة البيانات' WHERE id = 1224;
UPDATE permissions SET description = 'استعادة النسخ الاحتياطية لقاعدة البيانات' WHERE id = 1225;
UPDATE permissions SET description = 'اختبار الصلاحيات والتحقق منها' WHERE id = 1226;
UPDATE permissions SET description = 'أدوات التشخيص والتطوير' WHERE id = 1227;

-- إصلاح المجموعة الثالثة: السجلات الأخيرة المرمزة
UPDATE permissions SET description = 'إعدادات الإشعارات' WHERE id = 1232;
UPDATE permissions SET description = 'الوصول الديناميكي للتقارير' WHERE id = 1233;
UPDATE permissions SET description = 'إصلاح وصيانة قاعدة البيانات' WHERE id = 1234;
UPDATE permissions SET description = 'عرض وثائق الأرشيف' WHERE id = 1235;
UPDATE permissions SET description = 'إدارة تاريخ البحث' WHERE id = 1236;

-- إصلاح أسماء الصلاحيات المرمزة أيضاً
UPDATE permissions SET name = 'settings.notifications' WHERE id = 1232;
UPDATE permissions SET name = 'reports.dynamic_access' WHERE id = 1233;
UPDATE permissions SET name = 'admin.database_repair' WHERE id = 1234;
UPDATE permissions SET name = 'archive.view_documents' WHERE id = 1235;
UPDATE permissions SET name = 'search.manage_history' WHERE id = 1236;

-- إصلاح مجموعات الصلاحيات المرمزة
UPDATE permissions SET permission_group = 'Settings' WHERE id = 1232;
UPDATE permissions SET permission_group = 'Reports' WHERE id = 1233;
UPDATE permissions SET permission_group = 'Admin' WHERE id = 1234;
UPDATE permissions SET permission_group = 'Archive' WHERE id = 1235;
UPDATE permissions SET permission_group = 'Search' WHERE id = 1236;

-- إصلاح الفئات المرمزة
UPDATE permissions SET category = 'إعدادات' WHERE id = 1232;
UPDATE permissions SET category = 'تقارير' WHERE id = 1233;
UPDATE permissions SET category = 'إدارة' WHERE id = 1234;
UPDATE permissions SET category = 'أرشيف' WHERE id = 1235;
UPDATE permissions SET category = 'بحث' WHERE id = 1236;

-- تحديث تاريخ التعديل لجميع السجلات المُصلحة
UPDATE permissions 
SET updated_at = UNIX_TIMESTAMP() 
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
);

-- التحقق من النتائج
SELECT 
    id, 
    name, 
    description, 
    permission_group,
    category
FROM permissions 
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
)
ORDER BY id;

-- إنهاء المعاملة
COMMIT;

-- رسالة تأكيد
SELECT 'تم إصلاح جميع الأوصاف المرمزة بنجاح!' as result;
