@echo off
chcp 65001 >nul
title إصلاح قاعدة البيانات - UserPermissions

echo.
echo ================================================== 
echo 🚀 إصلاح قاعدة البيانات لحل مشاكل UserPermissions
echo ==================================================
echo.

echo 🔍 اختر العملية المطلوبة:
echo.
echo 1. تطبيق جميع الإصلاحات (مُوصى به)
echo 2. التحقق من البنية فقط
echo 3. التراجع عن الإصلاحات (خطر!)
echo 4. خروج
echo.

set /p choice="أدخل رقم الخيار (1-4): "

if "%choice%"=="1" goto apply_fixes
if "%choice%"=="2" goto verify_only
if "%choice%"=="3" goto rollback_mode
if "%choice%"=="4" goto exit
goto invalid_choice

:apply_fixes
echo.
echo 🚀 تطبيق جميع الإصلاحات...
echo ================================================== 
powershell.exe -ExecutionPolicy Bypass -File "%~dp0execute_fixes.ps1"
goto end

:verify_only
echo.
echo 🔍 التحقق من البنية...
echo ================================================== 
powershell.exe -ExecutionPolicy Bypass -File "%~dp0execute_fixes.ps1" -VerifyOnly
goto end

:rollback_mode
echo.
echo ⚠️ تحذير: وضع التراجع خطير!
echo سيتم حذف جميع الإصلاحات المطبقة
echo.
set /p confirm="هل أنت متأكد؟ (yes/no): "
if not "%confirm%"=="yes" goto exit

echo.
echo 🔄 التراجع عن الإصلاحات...
echo ================================================== 
powershell.exe -ExecutionPolicy Bypass -File "%~dp0execute_fixes.ps1" -RollbackMode
goto end

:invalid_choice
echo.
echo ❌ خيار غير صحيح. يرجى اختيار رقم من 1 إلى 4
echo.
pause
goto start

:end
echo.
echo ================================================== 
echo 🎉 انتهت العملية
echo ================================================== 
echo.
pause
goto exit

:exit
exit /b 0
