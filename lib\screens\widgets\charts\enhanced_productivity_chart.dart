import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart'; // استخدام Syncfusion Charts بدلاً من fl_chart
import 'package:flutter_application_2/models/advanced_filter_options.dart';
import 'package:flutter_application_2/models/chart_enums.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';

import 'unified_filter_export_widget.dart';

/// كلاس لتمثيل نقطة بيانات في مخطط الإنتاجية
class _ProductivityChartData {
  final String userName;
  final double value;
  final Color color;

  _ProductivityChartData(this.userName, this.value, this.color);
}

/// نموذج بيانات الإنتاجية
class ProductivityData {
  /// اسم المستخدم
  final String userName;

  /// عدد المهام المكتملة
  final int completedTasks;

  /// متوسط وقت الإكمال (بالساعات)
  final double averageCompletionTime;

  /// معدل الإنتاجية (المهام لكل يوم)
  final double productivityRate;

  /// لون البيانات (اختياري)
  final Color? color;

  /// إنشاء نموذج بيانات الإنتاجية
  const ProductivityData({
    required this.userName,
    required this.completedTasks,
    required this.averageCompletionTime,
    required this.productivityRate,
    this.color,
  });
}

/// مكون مخطط الإنتاجية المحسن
///
/// يوفر هذا المكون مخططًا للإنتاجية مع دعم للتصفية والتصدير
class EnhancedProductivityChart extends StatefulWidget {
  /// بيانات المخطط
  final List<ProductivityData> data;

  /// عنوان المخطط
  final String? title;

  /// ألوان المخطط (اختياري)
  final List<Color>? colors;

  /// دالة التصفية (اختياري)
  final Function(
          DateTime? startDate, DateTime? endDate, TimeFilterType filterType)?
      onFilterChanged;

  /// دالة التصدير (اختياري)
  final Function(String format)? onExport;

  /// دالة تغيير نوع المخطط (اختياري)
  final Function(ChartType)? onChartTypeChanged;

  /// إظهار خيارات التصفية (اختياري)
  final bool showFilterOptions;

  /// إظهار خيارات التصدير (اختياري)
  final bool showExportOptions;

  /// إظهار خيارات تغيير نوع المخطط (اختياري)
  final bool showChartTypeOptions;

  /// نوع المخطط الحالي
  final ChartType currentChartType;

  /// أنواع المخططات المدعومة (اختياري)
  final List<ChartType> supportedChartTypes;

  /// خيارات التصفية المتقدمة
  final AdvancedFilterOptions advancedFilterOptions;

  /// إنشاء مكون مخطط الإنتاجية المحسن
  const EnhancedProductivityChart({
    super.key,
    required this.data,
    this.title,
    this.colors,
    this.onFilterChanged,
    this.onExport,
    this.onChartTypeChanged,
    this.showFilterOptions = true,
    this.showExportOptions = true,
    this.showChartTypeOptions = true,
    this.currentChartType = ChartType.bar,
    this.supportedChartTypes = const [
      ChartType.bar,
      ChartType.line,
      ChartType.radar,
    ],
    required this.advancedFilterOptions,
  });

  @override
  State<EnhancedProductivityChart> createState() =>
      _EnhancedProductivityChartState();
}

class _EnhancedProductivityChartState extends State<EnhancedProductivityChart> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان المخطط وأزرار التصفية والتصدير
        if (widget.title != null ||
            widget.showFilterOptions ||
            widget.showExportOptions)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildHeader(),
          ),

        // المخطط
        Expanded(
          child: _buildChart(),
        ),

        // مفتاح المخطط
        Padding(
          padding: const EdgeInsets.only(top: 16),
          child: _buildLegend(),
        ),
      ],
    );
  }

  /// بناء رأس المخطط
  Widget _buildHeader() {
    return UnifiedFilterExportWidget(
      title: widget.title ?? 'تحليل إنتاجية المستخدمين',
      chartKey: 'productivity_chart',
      onFilterChanged: (startDate, endDate, filterType, chartKey) {
        if (widget.onFilterChanged != null) {
          widget.onFilterChanged!(startDate, endDate, filterType);
        }
      },
      onExport: (format, title) {
        if (widget.onExport != null) {
          widget.onExport!(format);
        }
      },
      onChartTypeChanged: widget.onChartTypeChanged != null
          ? (chartType, chartKey) {
              widget.onChartTypeChanged!(chartType);
            }
          : null,
      showFilter: widget.showFilterOptions,
      showExport: widget.showExportOptions,
      showChartTypeSelector: widget.showChartTypeOptions,
      supportedChartTypes: widget.supportedChartTypes,
      filterType: TimeFilterType.month,
      startDate: DateTime.now().subtract(const Duration(days: 30)),
      endDate: DateTime.now(),
      currentChartType: widget.currentChartType,
      chartIcon: Icons.person_pin_circle,
      chartType: widget.currentChartType,
      advancedFilterOptions: widget.advancedFilterOptions,
    );
  }

  /// بناء المخطط
  Widget _buildChart() {
    if (widget.data.isEmpty) {
      return UnifiedFilterExportWidget.buildNoDataMessage(
        context,
        message: 'لا توجد بيانات للعرض.\nقد يكون ذلك بسبب الفلتر المطبق.',
        onCancelFilter: () {
          // إلغاء الفلتر وإعادة تعيينه إلى الكل
          if (widget.onFilterChanged != null) {
            widget.onFilterChanged!(null, null, TimeFilterType.all);
          }
        },
      );
    }

    switch (widget.currentChartType) {
      case ChartType.bar:
        return _buildBarChart();
      case ChartType.line:
        return _buildLineChart();
      case ChartType.radar:
        return _buildRadarChart();
      default:
        return _buildBarChart();
    }
  }

  /// بناء مخطط شريطي باستخدام Syncfusion Charts
  Widget _buildBarChart() {
    final defaultColors = [
      AppColors.primary,
      AppColors.statusCompleted,
      AppColors.statusInProgress,
      AppColors.statusPending,
      AppColors.priorityHigh,
      AppColors.priorityMedium,
      AppColors.priorityLow,
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.shade400, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: SfCartesianChart(
        // تفعيل الميزات التفاعلية المتقدمة من Syncfusion
        enableAxisAnimation: true,
        enableSideBySideSeriesPlacement: false,

        // تفعيل Tooltip المتقدم
        tooltipBehavior: TooltipBehavior(
          enable: true,
          format: 'point.x: point.y مهمة/يوم',
          header: '',
          canShowMarker: true,
          activationMode: ActivationMode.singleTap,
          animationDuration: 500,
          borderColor: Colors.blue,
          borderWidth: 2,
        ),

        // تفعيل Trackball المتقدم
        trackballBehavior: TrackballBehavior(
          enable: true,
          activationMode: ActivationMode.singleTap,
          lineType: TrackballLineType.vertical,
          tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
          markerSettings: const TrackballMarkerSettings(
            markerVisibility: TrackballVisibilityMode.visible,
            height: 8,
            width: 8,
            borderWidth: 2,
          ),
        ),

        // إعدادات المفتاح
        legend: Legend(
          isVisible: false,
        ),

        // إعدادات المحاور
        primaryXAxis: CategoryAxis(
          majorGridLines: const MajorGridLines(width: 0),
          axisLine: AxisLine(
            color: Colors.grey.withValues(alpha: 0.5),
            width: 1,
          ),
          labelStyle: const TextStyle(
            fontSize: 10,
          ),
        ),

        primaryYAxis: NumericAxis(
          title: AxisTitle(text: 'معدل الإنتاجية (مهمة/يوم)'),
          majorGridLines: MajorGridLines(
            width: 1,
            color: Colors.grey.withValues(alpha: 0.3),
          ),
          axisLine: AxisLine(
            color: Colors.grey.withValues(alpha: 0.5),
            width: 1,
          ),
          labelStyle: const TextStyle(
            fontSize: 12,
          ),
        ),

        // إعدادات السلاسل
        series: _buildBarSeries(defaultColors),
      ),
    );
  }

  /// بناء سلاسل المخطط الشريطي
  List<CartesianSeries> _buildBarSeries(List<Color> defaultColors) {
    final List<CartesianSeries> series = [];

    // تحويل البيانات إلى تنسيق Syncfusion
    final List<_ProductivityChartData> chartData = [];
    for (int i = 0; i < widget.data.length; i++) {
      final data = widget.data[i];
      final color = data.color ??
          widget.colors?[i % (widget.colors?.length ?? 1)] ??
          defaultColors[i % defaultColors.length];

      chartData.add(_ProductivityChartData(
        data.userName,
        data.productivityRate,
        color,
      ));
    }

    series.add(
      ColumnSeries<_ProductivityChartData, String>(
        name: 'معدل الإنتاجية',
        dataSource: chartData,
        xValueMapper: (_ProductivityChartData data, _) => data.userName,
        yValueMapper: (_ProductivityChartData data, _) => data.value,
        pointColorMapper: (_ProductivityChartData data, _) => data.color,

        // إعدادات التفاعل
        enableTooltip: true,
        animationDuration: 1000,

        // تفعيل التحديد
        selectionBehavior: SelectionBehavior(
          enable: true,
        ),
      ),
    );

    return series;
  }

  /// بناء مخطط خطي باستخدام Syncfusion Charts
  Widget _buildLineChart() {
    final defaultColors = [
      AppColors.primary,
      AppColors.statusCompleted,
      AppColors.statusInProgress,
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.shade400, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: SfCartesianChart(
        // تفعيل الميزات التفاعلية المتقدمة من Syncfusion
        enableAxisAnimation: true,
        enableSideBySideSeriesPlacement: false,

        // تفعيل Tooltip المتقدم
        tooltipBehavior: TooltipBehavior(
          enable: true,
          format: 'point.x: point.y مهمة/يوم',
          header: '',
          canShowMarker: true,
          activationMode: ActivationMode.singleTap,
          animationDuration: 500,
          borderColor: Colors.blue,
          borderWidth: 2,
        ),

        // تفعيل Trackball المتقدم
        trackballBehavior: TrackballBehavior(
          enable: true,
          activationMode: ActivationMode.singleTap,
          lineType: TrackballLineType.vertical,
          tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
          markerSettings: const TrackballMarkerSettings(
            markerVisibility: TrackballVisibilityMode.visible,
            height: 8,
            width: 8,
            borderWidth: 2,
          ),
        ),

        // إعدادات المفتاح
        legend: Legend(
          isVisible: false,
        ),

        // إعدادات المحاور
        primaryXAxis: CategoryAxis(
          majorGridLines: const MajorGridLines(width: 0),
          axisLine: AxisLine(
            color: Colors.grey.withValues(alpha: 0.5),
            width: 1,
          ),
          labelStyle: const TextStyle(
            fontSize: 10,
          ),
        ),

        primaryYAxis: NumericAxis(
          title: AxisTitle(text: 'معدل الإنتاجية (مهمة/يوم)'),
          majorGridLines: MajorGridLines(
            width: 1,
            color: Colors.grey.withValues(alpha: 0.3),
          ),
          axisLine: AxisLine(
            color: Colors.grey.withValues(alpha: 0.5),
            width: 1,
          ),
          labelStyle: const TextStyle(
            fontSize: 12,
          ),
        ),

        // إعدادات السلاسل
        series: _buildLineSeries(defaultColors),
      ),
    );
  }

  /// بناء سلاسل المخطط الخطي
  List<CartesianSeries> _buildLineSeries(List<Color> defaultColors) {
    final List<CartesianSeries> series = [];

    // تحويل البيانات إلى تنسيق Syncfusion
    final List<_ProductivityChartData> chartData = [];
    for (int i = 0; i < widget.data.length; i++) {
      final data = widget.data[i];
      chartData.add(_ProductivityChartData(
        data.userName,
        data.productivityRate,
        defaultColors[0],
      ));
    }

    series.add(
      LineSeries<_ProductivityChartData, String>(
        name: 'معدل الإنتاجية',
        dataSource: chartData,
        xValueMapper: (_ProductivityChartData data, _) => data.userName,
        yValueMapper: (_ProductivityChartData data, _) => data.value,
        color: defaultColors[0],
        width: 3,

        // إعدادات النقاط
        markerSettings: MarkerSettings(
          isVisible: true,
          height: 6,
          width: 6,
          borderColor: defaultColors[0],
          borderWidth: 2,
        ),

        // إعدادات التفاعل
        enableTooltip: true,
        animationDuration: 1000,

        // تفعيل التحديد
        selectionBehavior: SelectionBehavior(
          enable: true,
          selectedColor: defaultColors[0].withValues(alpha: 0.8),
          unselectedColor: defaultColors[0].withValues(alpha: 0.3),
        ),
      ),
    );

    return series;
  }

  /// بناء مخطط راداري
  Widget _buildRadarChart() {
    // تنفيذ مخطط راداري للإنتاجية
    return const Center(
      child: Text('مخطط راداري للإنتاجية (قيد التطوير)'),
    );
  }

  /// بناء مفتاح المخطط
  Widget _buildLegend() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildLegendItem(
          'معدل الإنتاجية (مهمة/يوم)',
          AppColors.primary,
        ),
        const SizedBox(width: 16),
        _buildLegendItem(
          'المهام المكتملة',
          AppColors.statusCompleted,
        ),
      ],
    );
  }

  /// بناء عنصر مفتاح المخطط
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: AppStyles.caption,
        ),
      ],
    );
  }
}
