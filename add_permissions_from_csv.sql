-- ==========================================
-- سكريبت إضافة الصلاحيات من ملف CSV
-- تاريخ الإنشاء: 2025-01-12
-- الهدف: إضافة الصلاحيات الجديدة فقط مع تجنب التكرار
-- ==========================================

USE databasetasks;
GO

-- التحقق من وجود جدول الصلاحيات
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'permissions')
BEGIN
    PRINT '❌ خطأ: جدول permissions غير موجود!'
    RETURN
END

-- إنشاء جدول مؤقت للصلاحيات من ملف CSV
IF OBJECT_ID('tempdb..#CsvPermissions') IS NOT NULL
    DROP TABLE #CsvPermissions

CREATE TABLE #CsvPermissions (
    id INT,
    name NVARCHAR(255),
    description NVARCHAR(500),
    permission_group NVARCHAR(100),
    created_at BIGINT,
    updated_at BIGINT,
    category NVARCHAR(100),
    level INT,
    icon NVARCHAR(50),
    color NVARCHAR(20),
    is_default BIT,
    screen_id INT,
    action_id INT
)

-- إدراج البيانات من ملف CSV
INSERT INTO #CsvPermissions (id, name, description, permission_group, created_at, updated_at, category, level, icon, color, is_default, screen_id, action_id)
VALUES
-- مجموعة Dashboard Management
(40, 'dashboard.admin', N'إدارة لوحة المعلومات', N'Dashboard', 1750176586, 1751832925, N'إدارة', 4, NULL, NULL, 1),
-- مجموعة Tasks
(41, 'tasks.view', N'عرض المهام', N'Tasks', 1750176586, 1751832925, N'عرض', 1, NULL, NULL, 1),
(42, 'tasks.create', N'إنشاء مهام جديدة', N'Tasks', 1750176586, 1751832925, N'إنشاء', 1, NULL, NULL, 1),
(43, 'tasks.edit', N'تعديل المهام', N'Tasks', 1750176586, 1751832925, N'تعديل', 1, NULL, NULL, 1),
(44, 'tasks.delete', N'حذف المهام', N'Tasks', 1750176586, 1751832925, N'حذف', 1, NULL, NULL, 1),
(45, 'tasks.assign', N'تعيين المهام للمستخدمين', N'Tasks', 1750176586, 1751832925, N'تعيين', 1, NULL, NULL, 1),
(46, 'tasks.update_own', N'تحديث المهام الخاصة', N'Tasks', 1750176586, 1751832925, N'تحديث', 1, NULL, NULL, 1),
(71, 'tasks.view_all', N'عرض جميع المهام', N'Tasks', 1750176586, 1751832925, N'عرض متقدم', 5, NULL, NULL, 0),
-- مجموعة Users
(47, 'users.view', N'عرض المستخدمين', N'Users', 1750176586, 1751832925, N'عرض', 1, NULL, NULL, 1),
(48, 'users.create', N'إنشاء مستخدمين جدد', N'Users', 1750176586, 1751832925, N'إنشاء', 3, NULL, NULL, 1),
(49, 'users.edit', N'تعديل بيانات المستخدمين', N'Users', 1750176586, 1751832925, N'تعديل', 3, NULL, NULL, 1),
(51, 'users.manage_roles', N'إدارة أدوار المستخدمين', N'Users', 1750176586, 1751832925, N'إدارة', 4, NULL, NULL, 1),
(72, 'users.manage_permissions', N'إدارة صلاحيات المستخدمين', N'Users', 1751646909, NULL, N'إدارة متقدمة', 4, 'security', '#E91E63', 0),
-- مجموعة Reports
(52, 'reports.view', N'عرض التقارير', N'Reports', 1750176586, 1751832925, N'عرض', 2, NULL, NULL, 1),
(53, 'reports.create', N'إنشاء تقارير جديدة', N'Reports', 1750176586, 1751832925, N'إنشاء', 3, NULL, NULL, 1),
(54, 'reports.export', N'تصدير التقارير', N'Reports', 1750176586, 1751832925, N'تصدير', 2, NULL, NULL, 1),
-- مجموعة System
(55, 'system.manage', N'إدارة النظام', N'System', 1750176586, 1751832925, N'إدارة', 5, NULL, NULL, 1),
(56, 'system.backup', N'إنشاء نسخ احتياطية', N'System', 1750176586, 1751832925, N'نسخ احتياطي', 5, NULL, NULL, 1),
(57, 'system.restore', N'استعادة النسخ الاحتياطية', N'System', 1750176586, 1751832925, N'استعادة', 5, NULL, NULL, 1),
(58, 'database.manage', N'إدارة قاعدة البيانات', N'System', 1750176586, 1751832925, N'إدارة', 5, NULL, NULL, 1),
(83, 'system.logs', N'عرض سجلات النظام', N'System', 1751646909, NULL, N'مراقبة', 4, 'description', '#9E9E9E', 0),
-- مجموعة Profile
(59, 'profile.view', N'عرض الملف الشخصي', N'Profile', 1750176586, 1751832925, N'عرض', 1, NULL, NULL, 1),
(60, 'profile.edit', N'تعديل الملف الشخصي', N'Profile', 1750176586, 1751832925, N'تعديل', 1, NULL, NULL, 1),
-- مجموعة Notifications
(61, 'notifications.view', N'عرض الإشعارات', N'Notifications', 1750176586, 1751832925, N'عرض', 1, NULL, NULL, 1),
(62, 'notifications.manage', N'إدارة الإشعارات', N'Notifications', 1750176586, 1751832925, N'إدارة', 3, NULL, NULL, 1),
(85, 'notifications.send', N'إرسال إشعارات للمستخدمين', N'Notifications', 1751646909, NULL, N'إرسال', 3, 'send', '#FF9800', 0),
-- مجموعة Calendar
(64, 'calendar.manage', N'إدارة التقويم', N'Calendar', 1750176586, 1751832925, N'إدارة', 2, NULL, NULL, 1),
(76, 'calendar.create_events', N'إنشاء أحداث في التقويم', N'Calendar', 1751646909, NULL, N'إنشاء', 2, 'event', '#FF5722', 0),
-- مجموعة Departments
(65, 'departments.view', N'عرض الأقسام', N'Departments', 1750176586, 1751832925, N'عرض', 3, NULL, NULL, 1),
(66, 'departments.manage', N'إدارة الأقسام', N'Departments', 1750176586, 1751832925, N'إدارة', 4, NULL, NULL, 1),
-- مجموعة Chat
(73, 'chat.view', N'الوصول للمحادثات', N'Chat', 1751646909, NULL, N'عرض', 1, 'chat', '#00BCD4', 1),
(74, 'chat.send', N'إرسال الرسائل', N'Chat', 1751646909, NULL, N'إرسال', 1, 'send', '#00BCD4', 1),
(75, 'chat.delete_messages', N'حذف الرسائل', N'Chat', 1751646909, NULL, N'حذف', 3, 'delete', '#F44336', 0),
-- مجموعة Archive
(77, 'archive.view', N'عرض الأرشيف', N'Archive', 1751646909, NULL, N'عرض', 1, 'archive', '#607D8B', 1),
(78, 'archive.upload', N'رفع المستندات للأرشيف', N'Archive', 1751646909, NULL, N'رفع', 2, 'cloud_upload', '#607D8B', 0),
(79, 'archive.download', N'تحميل المستندات من الأرشيف', N'Archive', 1751646909, NULL, N'تحميل', 1, 'cloud_download', '#607D8B', 1),
(80, 'archive.delete', N'حذف المستندات من الأرشيف', N'Archive', 1751646909, NULL, N'حذف', 3, 'delete_forever', '#F44336', 0),
(81, 'archive.manage_categories', N'إدارة تصنيفات الأرشيف', N'Archive', 1751646909, NULL, N'إدارة', 3, 'category', '#607D8B', 0),
-- مجموعة Admin
(82, 'admin.view', N'الوصول للوحة الإدارة', N'Admin', 1751646909, NULL, N'وصول', 4, 'admin_panel_settings', '#9E9E9E', 0),
-- مجموعة PowerBI
(84, 'powerbi.view', N'عرض تقارير Power BI', N'PowerBI', 1751646909, NULL, N'عرض', 2, 'analytics', '#FFD700', 0)

PRINT '📊 تم تحميل ' + CAST(@@ROWCOUNT AS NVARCHAR(10)) + ' صلاحية من ملف CSV'

-- بدء المعاملة
BEGIN TRANSACTION

DECLARE @AddedCount INT = 0
DECLARE @SkippedCount INT = 0
DECLARE @CurrentTime BIGINT = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())

PRINT '🚀 بدء عملية إضافة الصلاحيات...'
PRINT '=================================='

-- إضافة الصلاحيات الجديدة فقط
INSERT INTO permissions (
    name, 
    description, 
    permission_group, 
    category, 
    level, 
    icon, 
    color, 
    is_default, 
    created_at,
    updated_at,
    screen_id,
    action_id
)
SELECT 
    csv.name,
    csv.description,
    csv.permission_group,
    csv.category,
    ISNULL(csv.level, 1),
    csv.icon,
    csv.color,
    ISNULL(csv.is_default, 0),
    @CurrentTime,
    NULL,
    csv.screen_id,
    csv.action_id
FROM #CsvPermissions csv
WHERE NOT EXISTS (
    SELECT 1 
    FROM permissions p 
    WHERE p.name = csv.name
)

SET @AddedCount = @@ROWCOUNT

-- حساب الصلاحيات المتجاهلة
SELECT @SkippedCount = COUNT(*)
FROM #CsvPermissions csv
WHERE EXISTS (
    SELECT 1 
    FROM permissions p 
    WHERE p.name = csv.name
)

-- عرض النتائج
PRINT '✅ تم إضافة ' + CAST(@AddedCount AS NVARCHAR(10)) + ' صلاحية جديدة'
PRINT '⚠️ تم تجاهل ' + CAST(@SkippedCount AS NVARCHAR(10)) + ' صلاحية موجودة مسبقاً'
PRINT '📈 إجمالي الصلاحيات في النظام: ' + CAST((SELECT COUNT(*) FROM permissions) AS NVARCHAR(10))

-- تأكيد المعاملة
COMMIT TRANSACTION

-- تنظيف الجدول المؤقت
DROP TABLE #CsvPermissions

PRINT '🎉 تمت العملية بنجاح!'
PRINT '=================================='

-- عرض عينة من الصلاحيات المضافة حديثاً
SELECT TOP 10
    id,
    name,
    description,
    permission_group,
    category,
    level,
    created_at
FROM permissions 
WHERE created_at = @CurrentTime
ORDER BY id DESC

GO
