import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/advanced_filter_options.dart' as filter_options;
import 'package:flutter_application_2/models/chart_enums.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';

import 'unified_filter_export_widget.dart';

/// نموذج بيانات الفقاعة
class BubbleData {
  /// قيمة المحور س
  final double x;

  /// قيمة المحور ص
  final double y;

  /// حجم الفقاعة
  final double size;

  /// تسمية الفقاعة (اختياري)
  final String? label;

  /// لون الفقاعة (اختياري)
  final Color? color;

  /// إنشاء نموذج بيانات الفقاعة
  const BubbleData({
    required this.x,
    required this.y,
    required this.size,
    this.label,
    this.color,
  });
}

/// مكون مخطط فقاعي محسن
///
/// يوفر هذا المكون مخططًا فقاعيًا متقدمًا مع دعم للتصفية والتصدير
class EnhancedBubbleChart extends StatefulWidget {
  /// بيانات المخطط
  /// مفتاح الخريطة هو اسم السلسلة، والقيمة هي قائمة من بيانات الفقاعات
  final Map<String, List<BubbleData>> data;

  /// عنوان المخطط
  final String? title;

  /// عنوان المحور س
  final String? xAxisTitle;

  /// عنوان المحور ص
  final String? yAxisTitle;

  /// ألوان السلاسل (اختياري)
  final Map<String, Color>? seriesColors;

  /// القيمة الدنيا للمحور س (اختياري)
  final double? minX;

  /// القيمة القصوى للمحور س (اختياري)
  final double? maxX;

  /// القيمة الدنيا للمحور ص (اختياري)
  final double? minY;

  /// القيمة القصوى للمحور ص (اختياري)
  final double? maxY;

  /// هل يتم عرض خطوط الشبكة
  final bool showGrid;

  /// هل يتم عرض تسميات الفقاعات
  final bool showLabels;

  /// هل يتم عرض مفتاح المخطط
  final bool showLegend;

  /// دالة التصفية (اختياري)
  final Function(
          DateTime? startDate, DateTime? endDate, TimeFilterType filterType)?
      onFilterChanged;

  /// دالة التصدير (اختياري)
  final Function(String format)? onExport;

  /// دالة تغيير نوع المخطط (اختياري)
  final Function(ChartType)? onChartTypeChanged;

  /// إظهار خيارات التصفية (اختياري)
  final bool showFilterOptions;

  /// إظهار خيارات التصدير (اختياري)
  final bool showExportOptions;

  /// إظهار خيارات تغيير نوع المخطط (اختياري)
  final bool showChartTypeOptions;

  /// تنسيقات التصدير المدعومة (اختياري)
  final List<String> supportedExportFormats;

  /// أنواع المخططات المدعومة (اختياري)
  final List<ChartType> supportedChartTypes;

  /// الفاصل الأفقي للشبكة
  final double? horizontalInterval;

  /// الفاصل الرأسي للشبكة
  final double? verticalInterval;

  /// نوع المخطط
  final ChartType chartType;

  /// خيارات التصفية المتقدمة
  final filter_options.AdvancedFilterOptions advancedFilterOptions;

  /// إنشاء مكون مخطط فقاعي محسن
  const EnhancedBubbleChart({
    super.key,
    required this.data,
    this.title,
    this.xAxisTitle,
    this.yAxisTitle,
    this.seriesColors,
    this.minX,
    this.maxX,
    this.minY,
    this.maxY,
    this.showGrid = true,
    this.showLabels = true,
    this.showLegend = true,
    this.onFilterChanged,
    this.onExport,
    this.onChartTypeChanged,
    this.showFilterOptions = false,
    this.showExportOptions = false,
    this.showChartTypeOptions = false,
    this.supportedExportFormats = const ['pdf', 'excel', 'csv', 'image'],
    this.supportedChartTypes = const [
      ChartType.bubble,
      ChartType.scatter,
    ],
    this.horizontalInterval,
    this.verticalInterval,
    required this.chartType,
    required this.advancedFilterOptions,
  });

  @override
  State<EnhancedBubbleChart> createState() => _EnhancedBubbleChartState();
}

class _EnhancedBubbleChartState extends State<EnhancedBubbleChart> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان المخطط وأزرار التصفية والتصدير
        if (widget.title != null ||
            widget.showFilterOptions ||
            widget.showExportOptions)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildHeader(),
          ),

        // المخطط الفقاعي
        Expanded(
          child: _buildBubbleChart(),
        ),

        // عناوين المحاور
        if (widget.xAxisTitle != null || widget.yAxisTitle != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (widget.yAxisTitle != null)
                  Text(
                    widget.yAxisTitle!,
                    style: AppStyles.caption,
                  )
                else
                  const SizedBox.shrink(),
                if (widget.xAxisTitle != null)
                  Text(
                    widget.xAxisTitle!,
                    style: AppStyles.caption,
                  )
                else
                  const SizedBox.shrink(),
              ],
            ),
          ),

        // مفتاح المخطط
        if (widget.showLegend && widget.data.length > 1)
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: _buildLegend(),
          ),
      ],
    );
  }

  /// بناء رأس المخطط
  Widget _buildHeader() {
    // استخدام مكون UnifiedFilterExportWidget بدلاً من الأكواد المكررة
    return UnifiedFilterExportWidget(
      title: widget.title ?? 'مخطط فقاعي',
      chartKey: 'bubble_chart',
      onFilterChanged: (startDate, endDate, filterType, chartKey) {
        if (widget.onFilterChanged != null) {
          widget.onFilterChanged!(startDate, endDate, filterType);
        }
      },
      onExport: (format, title) {
        if (widget.onExport != null) {
          widget.onExport!(format);
        }
      },
      onChartTypeChanged: widget.onChartTypeChanged != null
          ? (chartType, chartKey) {
              widget.onChartTypeChanged!(chartType);
            }
          : null,
      showFilter: widget.showFilterOptions,
      showExport: widget.showExportOptions,
      showChartTypeSelector: widget.showChartTypeOptions,
      supportedChartTypes: widget.supportedChartTypes,
      filterType: TimeFilterType.month,
      startDate: DateTime.now().subtract(const Duration(days: 30)),
      endDate: DateTime.now(),
      currentChartType: ChartType.bubble,
      chartType: ChartType.bubble,
      advancedFilterOptions: const filter_options.AdvancedFilterOptions(),
    );
  }

  // تم تعليق هذه الدوال لأنها لم تعد مستخدمة بعد استخدام UnifiedFilterExportWidget

  /*
  /// بناء أزرار تغيير نوع المخطط
  Widget _buildChartTypeButtons() {
    // استخدام أزرار مباشرة بدلاً من مكون ChartTypeSelector لتجنب التكرار
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      // child: Row(
      //   mainAxisAlignment: MainAxisAlignment.center,
      //   children: widget.supportedChartTypes.map((type) {
      //     final isSelected = type == ChartType.bubble;
      //     return Padding(
      //       padding: const EdgeInsets.symmetric(horizontal: 2.0),
      //       child: ChoiceChip(
      //         label: Icon(
      //           ChartTypeUtils.getChartTypeIcon(type),
      //           size: 20,
      //           color: isSelected ? Colors.white : Colors.grey.shade700,
      //         ),
      //         selected: isSelected,
      //         onSelected: (selected) {
      //           if (selected) {
      //             widget.onChartTypeChanged!(type);
      //           }
      //         },
      //         backgroundColor: Colors.grey.shade200,
      //         selectedColor: AppColors.primary,
      //         tooltip: ChartTypeUtils.getChartTypeLabel(type),
      //       ),
      //     );
      //   }).toList(),
      // ),
    );
  }

  /// عرض مربع حوار التصفية
  void _showFilterDialog() {
    // يمكن هنا عرض مربع حوار للتصفية
  }
  */

  /// حساب فاصل آمن للشبكة
  double _calculateSafeInterval(double min, double max) {
    if (!min.isFinite || !max.isFinite || min.isNaN || max.isNaN || min >= max) {
      return 1.0; // قيمة افتراضية آمنة
    }
    
    final range = max - min;
    if (range <= 0 || !range.isFinite || range.isNaN) {
      return 1.0;
    }
    
    // حساب فاصل معقول (حوالي 5-10 خطوط شبكة)
    final rawInterval = range / 5;
    
    // تقريب إلى رقم "لطيف"
    if (rawInterval < 1) {
      return 0.1 * (rawInterval * 10).ceil();
    } else if (rawInterval < 10) {
      return rawInterval.ceil().toDouble();
    } else {
      return 10.0 * (rawInterval / 10).ceil();
    }
  }

  /// بناء المخطط الفقاعي
  Widget _buildBubbleChart() {
    // التحقق من وجود بيانات
    if (widget.data.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات للعرض',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    // تحديد القيم الدنيا والقصوى
    double minX = widget.minX ?? double.infinity;
    double maxX = widget.maxX ?? -double.infinity;
    double minY = widget.minY ?? double.infinity;
    double maxY = widget.maxY ?? -double.infinity;

    // حساب القيم من البيانات إذا لم تكن محددة
    bool hasValidData = false;
    for (final series in widget.data.values) {
      for (final bubble in series) {
        // التحقق من صحة القيم
        if (bubble.x.isFinite && bubble.y.isFinite && !bubble.x.isNaN && !bubble.y.isNaN) {
          hasValidData = true;
          if (minX.isInfinite || bubble.x < minX) minX = bubble.x;
          if (maxX.isInfinite || bubble.x > maxX) maxX = bubble.x;
          if (minY.isInfinite || bubble.y < minY) minY = bubble.y;
          if (maxY.isInfinite || bubble.y > maxY) maxY = bubble.y;
        }
      }
    }

    // التحقق من صحة البيانات المحسوبة
    if (!hasValidData ||
        minX.isInfinite || maxX.isInfinite ||
        minY.isInfinite || maxY.isInfinite ||
        minX.isNaN || maxX.isNaN ||
        minY.isNaN || maxY.isNaN) {
      return const Center(
        child: Text(
          'البيانات غير صالحة للعرض',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    // التأكد من أن القيم ليست متساوية
    if (minX == maxX) {
      minX -= 1;
      maxX += 1;
    }
    if (minY == maxY) {
      minY -= 1;
      maxY += 1;
    }

    // إضافة هامش للقيم
    final xRange = maxX - minX;
    final yRange = maxY - minY;
    final xMargin = xRange > 0 ? xRange * 0.1 : 1.0;
    final yMargin = yRange > 0 ? yRange * 0.1 : 1.0;

    minX -= xMargin;
    maxX += xMargin;
    minY -= yMargin;
    maxY += yMargin;

    // إنشاء ألوان مختلفة لكل سلسلة بيانات
    final defaultColors = [
      AppColors.primary,
      AppColors.statusCompleted,
      AppColors.statusInProgress,
      AppColors.statusPending,
      AppColors.priorityHigh,
      AppColors.priorityMedium,
      AppColors.priorityLow,
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.shade400, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: SfCartesianChart(
        // تفعيل الميزات التفاعلية المتقدمة من Syncfusion
        enableAxisAnimation: true,
        enableSideBySideSeriesPlacement: true,

        // تفعيل Tooltip المتقدم
        tooltipBehavior: TooltipBehavior(
          enable: true,
          format: 'point.x: point.y',
          header: '',
          canShowMarker: true,
          activationMode: ActivationMode.singleTap,
          animationDuration: 500,
          borderColor: Colors.blue,
          borderWidth: 2,
        ),

        // إعدادات المحاور
        primaryXAxis: NumericAxis(
          title: AxisTitle(text: widget.xAxisTitle ?? ''),
          minimum: minX,
          maximum: maxX,
          interval: _calculateSafeInterval(minX, maxX), // استخدام فاصل آمن
          majorGridLines: MajorGridLines(
            width: widget.showGrid ? 1 : 0,
            color: Colors.grey.withAlpha(76),
          ),
          labelStyle: const TextStyle(fontSize: 10),
        ),

        primaryYAxis: NumericAxis(
          title: AxisTitle(text: widget.yAxisTitle ?? ''),
          minimum: minY,
          maximum: maxY,
          interval: _calculateSafeInterval(minY, maxY), // استخدام فاصل آمن
          majorGridLines: MajorGridLines(
            width: widget.showGrid ? 1 : 0,
            color: Colors.grey.withAlpha(76),
          ),
          labelStyle: const TextStyle(fontSize: 10),
        ),

        // إعدادات المفتاح
        legend: widget.showLegend ? Legend(
          isVisible: true,
          position: LegendPosition.bottom,
          overflowMode: LegendItemOverflowMode.wrap,
          textStyle: const TextStyle(fontSize: 12),
          itemPadding: 8,
        ) : Legend(isVisible: false),

        // إعدادات السلاسل
        series: _createSyncfusionBubbleSeries(defaultColors),
      ),
    );
  }

  /// إنشاء سلاسل Syncfusion للمخطط الفقاعي
  List<CartesianSeries> _createSyncfusionBubbleSeries(List<Color> defaultColors) {
    final List<CartesianSeries> series = [];
    final seriesNames = widget.data.keys.toList();

    for (int i = 0; i < seriesNames.length; i++) {
      final seriesName = seriesNames[i];
      final seriesData = widget.data[seriesName]!;
      final color = widget.seriesColors?[seriesName] ??
          defaultColors[i % defaultColors.length];

      // تصفية البيانات الصحيحة فقط
      final validData = seriesData.where((bubble) =>
          bubble.x.isFinite && bubble.y.isFinite &&
          !bubble.x.isNaN && !bubble.y.isNaN &&
          bubble.size.isFinite && !bubble.size.isNaN && bubble.size > 0).toList();

      if (validData.isNotEmpty) {
        series.add(
          BubbleSeries<BubbleData, double>(
            name: seriesName,
            dataSource: validData,
            xValueMapper: (BubbleData data, _) => data.x,
            yValueMapper: (BubbleData data, _) => data.y,
            sizeValueMapper: (BubbleData data, _) => data.size.clamp(1.0, 50.0),
            color: color,
            opacity: 0.7,
            borderColor: Colors.white,
            borderWidth: 1,
            dataLabelSettings: DataLabelSettings(
              isVisible: widget.showLabels,
              labelPosition: ChartDataLabelPosition.outside,
              textStyle: const TextStyle(fontSize: 10),
            ),
            enableTooltip: true,
            animationDuration: 1000,
            selectionBehavior: SelectionBehavior(
              enable: true,
              selectedColor: color.withAlpha(128),
              unselectedColor: color.withAlpha(76),
            ),
          ),
        );
      }
    }

    return series;
  }

  /// بناء مفتاح المخطط
  Widget _buildLegend() {
    final seriesNames = widget.data.keys.toList();
    final defaultColors = [
      AppColors.primary,
      AppColors.statusCompleted,
      AppColors.statusInProgress,
      AppColors.statusPending,
      AppColors.priorityHigh,
      AppColors.priorityMedium,
      AppColors.priorityLow,
    ];

    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 16,
      runSpacing: 8,
      children: seriesNames.asMap().entries.map((entry) {
        final index = entry.key;
        final seriesName = entry.value;
        final color = widget.seriesColors?[seriesName] ??
            defaultColors[index % defaultColors.length];

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              seriesName,
              style: AppStyles.caption,
            ),
          ],
        );
      }).toList(),
    );
  }
}
