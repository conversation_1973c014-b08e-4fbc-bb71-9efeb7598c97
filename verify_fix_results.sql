-- التحقق من نتائج إصلاح الصلاحيات المرمزة
-- تاريخ التحقق: 2025-01-06

USE databasetasks;
GO

-- 1. عرض السجلات المُصلحة مع الأوصاف الجديدة
SELECT 
    id,
    name,
    description,
    permission_group,
    category,
    'Fixed' as status
FROM permissions 
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
)
ORDER BY id;

-- 2. البحث عن أي سجلات لا تزال تحتوي على ترميز خاطئ
SELECT 
    'Records still with encoding issues' as check_type,
    COUNT(*) as count
FROM permissions 
WHERE description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%?%';

-- 3. عرض السجلات التي لا تزال تحتوي على مشاكل ترميز (إن وجدت)
SELECT 
    id,
    name,
    description,
    'Still has encoding issues' as status
FROM permissions 
WHERE description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%?%'
ORDER BY id;

-- 4. إحصائيات شاملة للصلاحيات
SELECT 
    'Total Permissions Statistics' as report_type,
    COUNT(*) as total_permissions,
    SUM(CASE WHEN description NOT LIKE '%Ø%' AND description NOT LIKE '%Ù%' AND description NOT LIKE '%?%' THEN 1 ELSE 0 END) as clean_descriptions,
    SUM(CASE WHEN description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%?%' THEN 1 ELSE 0 END) as problematic_descriptions
FROM permissions;

-- 5. عرض توزيع الصلاحيات حسب المجموعات
SELECT 
    permission_group,
    COUNT(*) as total_permissions,
    SUM(CASE WHEN description NOT LIKE '%Ø%' AND description NOT LIKE '%Ù%' AND description NOT LIKE '%?%' THEN 1 ELSE 0 END) as clean_descriptions
FROM permissions 
GROUP BY permission_group
ORDER BY permission_group;

-- 6. التحقق من السجلات المُحدثة حديثاً
SELECT 
    'Recently Updated Records' as check_type,
    COUNT(*) as count
FROM permissions 
WHERE updated_at > DATEDIFF(SECOND, '1970-01-01', DATEADD(MINUTE, -10, GETUTCDATE()));

PRINT N'تم الانتهاء من التحقق من نتائج الإصلاح';
GO
