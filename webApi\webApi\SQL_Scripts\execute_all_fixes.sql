-- ===================================================================
-- سكريبت التنفيذ الشامل لجميع إصلاحات UserPermissions
-- تاريخ الإنشاء: 2025-01-05
-- الهدف: تطبيق جميع الإصلاحات المطلوبة تلقائياً
-- ===================================================================

USE [databasetasks]
GO

SET NOCOUNT ON
GO

PRINT '🚀 بدء تطبيق إصلاحات UserPermissions الشاملة...'
PRINT '=================================================='
PRINT 'الوقت: ' + CONVERT(NVARCHAR(50), GETDATE(), 120)
PRINT '=================================================='

-- ===================================================================
-- المرحلة 1: النسخ الاحتياطي والتحضير
-- ===================================================================

PRINT '💾 المرحلة 1: التحضير والنسخ الاحتياطي...'

-- إنشاء جدول نسخ احتياطي لـ user_permissions
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'user_permissions_backup')
BEGIN
    SELECT * INTO user_permissions_backup FROM [dbo].[user_permissions]
    PRINT '✅ تم إنشاء نسخة احتياطية من user_permissions'
END
ELSE
BEGIN
    PRINT '⚠️ النسخة الاحتياطية موجودة بالفعل'
END

-- ===================================================================
-- المرحلة 2: إصلاح جدول user_permissions
-- ===================================================================

PRINT '🔧 المرحلة 2: إصلاح جدول user_permissions...'

-- إضافة عمود created_at إذا لم يكن موجوداً
IF NOT EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'user_permissions' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    ALTER TABLE [dbo].[user_permissions]
    ADD [created_at] BIGINT NOT NULL 
    DEFAULT (datediff(second,'1970-01-01',getutcdate()))
    
    PRINT '✅ تم إضافة عمود created_at إلى user_permissions'
    
    -- تحديث القيم الموجودة
    UPDATE [dbo].[user_permissions] 
    SET [created_at] = [granted_at]
    WHERE [created_at] IS NULL OR [created_at] = 0
    
    PRINT '✅ تم تحديث قيم created_at للسجلات الموجودة'
END
ELSE
BEGIN
    PRINT '✅ عمود created_at موجود بالفعل'
    
    -- تحديث القيم الفارغة
    UPDATE [dbo].[user_permissions] 
    SET [created_at] = [granted_at]
    WHERE [created_at] IS NULL OR [created_at] = 0
    
    DECLARE @UpdatedRows INT = @@ROWCOUNT
    IF @UpdatedRows > 0
        PRINT '✅ تم تحديث ' + CAST(@UpdatedRows AS NVARCHAR(10)) + ' سجل'
END

-- ===================================================================
-- المرحلة 3: إنشاء الجداول الجديدة
-- ===================================================================

PRINT '🏗️ المرحلة 3: إنشاء الجداول الجديدة...'

-- إنشاء جدول custom_roles
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'custom_roles')
BEGIN
    CREATE TABLE [dbo].[custom_roles] (
        [id] INT IDENTITY(1,1) NOT NULL,
        [name] NVARCHAR(100) NOT NULL,
        [description] NVARCHAR(255) NULL,
        [parent_role_id] INT NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL DEFAULT (datediff(second,'1970-01-01',getutcdate())),
        [updated_at] BIGINT NULL,
        [is_deleted] BIT NOT NULL DEFAULT (0),
        
        CONSTRAINT [PK_custom_roles] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_custom_roles_created_by_users] FOREIGN KEY ([created_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [FK_custom_roles_parent] FOREIGN KEY ([parent_role_id]) REFERENCES [dbo].[custom_roles] ([id])
    )
    PRINT '✅ تم إنشاء جدول custom_roles'
END
ELSE
BEGIN
    PRINT '✅ جدول custom_roles موجود بالفعل'
END

-- إنشاء جدول user_custom_roles
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'user_custom_roles')
BEGIN
    CREATE TABLE [dbo].[user_custom_roles] (
        [id] INT IDENTITY(1,1) NOT NULL,
        [user_id] INT NOT NULL,
        [custom_role_id] INT NOT NULL,
        [assigned_at] BIGINT NOT NULL DEFAULT (datediff(second,'1970-01-01',getutcdate())),
        [is_deleted] BIT NOT NULL DEFAULT (0),
        
        CONSTRAINT [PK_user_custom_roles] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [UQ_user_custom_roles] UNIQUE ([user_id], [custom_role_id]),
        CONSTRAINT [FK_user_custom_roles_user] FOREIGN KEY ([user_id]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [FK_user_custom_roles_custom_role] FOREIGN KEY ([custom_role_id]) REFERENCES [dbo].[custom_roles] ([id])
    )
    PRINT '✅ تم إنشاء جدول user_custom_roles'
END
ELSE
BEGIN
    PRINT '✅ جدول user_custom_roles موجود بالفعل'
END

-- إنشاء جدول custom_role_permissions
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'custom_role_permissions')
BEGIN
    CREATE TABLE [dbo].[custom_role_permissions] (
        [id] INT IDENTITY(1,1) NOT NULL,
        [custom_role_id] INT NOT NULL,
        [permission_id] INT NOT NULL,
        [created_at] BIGINT NOT NULL DEFAULT (datediff(second,'1970-01-01',getutcdate())),
        [created_by] INT NOT NULL,
        [is_deleted] BIT NOT NULL DEFAULT (0),
        
        CONSTRAINT [PK_custom_role_permissions] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [UQ_custom_role_permissions] UNIQUE ([custom_role_id], [permission_id]),
        CONSTRAINT [FK_custom_role_permissions_custom_role] FOREIGN KEY ([custom_role_id]) REFERENCES [dbo].[custom_roles] ([id]),
        CONSTRAINT [FK_custom_role_permissions_permission] FOREIGN KEY ([permission_id]) REFERENCES [dbo].[permissions] ([id]),
        CONSTRAINT [FK_custom_role_permissions_created_by_users] FOREIGN KEY ([created_by]) REFERENCES [dbo].[users] ([id])
    )
    PRINT '✅ تم إنشاء جدول custom_role_permissions'
END
ELSE
BEGIN
    PRINT '✅ جدول custom_role_permissions موجود بالفعل'
END

-- ===================================================================
-- المرحلة 4: إنشاء الفهارس
-- ===================================================================

PRINT '📊 المرحلة 4: إنشاء الفهارس...'

-- فهارس user_permissions
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_user_permissions_created_at')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_user_permissions_created_at] 
    ON [dbo].[user_permissions] ([created_at])
    PRINT '✅ تم إنشاء فهرس IX_user_permissions_created_at'
END

IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_user_permissions_active_deleted')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_user_permissions_active_deleted] 
    ON [dbo].[user_permissions] ([is_active], [is_deleted])
    PRINT '✅ تم إنشاء فهرس IX_user_permissions_active_deleted'
END

-- فهارس custom_roles
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_custom_roles_is_deleted')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_custom_roles_is_deleted] 
    ON [dbo].[custom_roles] ([is_deleted])
    PRINT '✅ تم إنشاء فهرس IX_custom_roles_is_deleted'
END

-- فهارس user_custom_roles
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_user_custom_roles_is_deleted')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_user_custom_roles_is_deleted] 
    ON [dbo].[user_custom_roles] ([is_deleted])
    PRINT '✅ تم إنشاء فهرس IX_user_custom_roles_is_deleted'
END

-- ===================================================================
-- المرحلة 5: اختبار البنية
-- ===================================================================

PRINT '🧪 المرحلة 5: اختبار البنية...'

BEGIN TRY
    -- اختبار إدراج في user_permissions
    DECLARE @TestUserId INT = (SELECT TOP 1 id FROM users WHERE is_deleted = 0)
    DECLARE @TestPermissionId INT = (SELECT TOP 1 id FROM permissions)
    DECLARE @TestGrantedBy INT = (SELECT TOP 1 id FROM users WHERE is_deleted = 0)
    DECLARE @CurrentTime BIGINT = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
    
    IF @TestUserId IS NOT NULL AND @TestPermissionId IS NOT NULL AND @TestGrantedBy IS NOT NULL
    BEGIN
        BEGIN TRANSACTION TestInsert
        
        INSERT INTO [dbo].[user_permissions] 
        (user_id, permission_id, granted_by, granted_at, is_active, expires_at, is_deleted, created_at)
        VALUES 
        (@TestUserId, @TestPermissionId, @TestGrantedBy, @CurrentTime, 1, NULL, 0, @CurrentTime)
        
        ROLLBACK TRANSACTION TestInsert
        PRINT '✅ اختبار إدراج user_permissions نجح'
    END
    ELSE
    BEGIN
        PRINT '⚠️ لا توجد بيانات كافية لاختبار user_permissions'
    END
    
END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION
    PRINT '❌ فشل اختبار الإدراج: ' + ERROR_MESSAGE()
END CATCH

-- ===================================================================
-- المرحلة 6: التحقق النهائي والإحصائيات
-- ===================================================================

PRINT '📊 المرحلة 6: التحقق النهائي...'

-- إحصائيات الجداول
SELECT 
    'user_permissions' as TableName,
    COUNT(*) as TotalRecords,
    COUNT(CASE WHEN created_at IS NOT NULL AND created_at > 0 THEN 1 END) as ValidCreatedAt,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as ActiveRecords
FROM [dbo].[user_permissions]

UNION ALL

SELECT 
    'custom_roles' as TableName,
    COUNT(*) as TotalRecords,
    COUNT(CASE WHEN is_deleted = 0 THEN 1 END) as ActiveRecords,
    0 as ValidCreatedAt
FROM [dbo].[custom_roles]

UNION ALL

SELECT 
    'user_custom_roles' as TableName,
    COUNT(*) as TotalRecords,
    COUNT(CASE WHEN is_deleted = 0 THEN 1 END) as ActiveRecords,
    0 as ValidCreatedAt
FROM [dbo].[user_custom_roles]

UNION ALL

SELECT 
    'custom_role_permissions' as TableName,
    COUNT(*) as TotalRecords,
    COUNT(CASE WHEN is_deleted = 0 THEN 1 END) as ActiveRecords,
    0 as ValidCreatedAt
FROM [dbo].[custom_role_permissions]

-- عدد الفهارس المنشأة
SELECT 
    COUNT(*) as TotalIndexes
FROM sys.indexes i
INNER JOIN sys.tables t ON i.object_id = t.object_id
WHERE t.name IN ('user_permissions', 'custom_roles', 'user_custom_roles', 'custom_role_permissions')
AND i.name IS NOT NULL
AND i.name LIKE 'IX_%'

PRINT '=================================================='
PRINT '🎉 تم تطبيق جميع الإصلاحات بنجاح!'
PRINT '=================================================='
PRINT 'الوقت: ' + CONVERT(NVARCHAR(50), GETDATE(), 120)
PRINT ''
PRINT '📋 ملخص ما تم تطبيقه:'
PRINT '✅ إضافة عمود created_at إلى user_permissions'
PRINT '✅ إنشاء جدول custom_roles'
PRINT '✅ إنشاء جدول user_custom_roles'  
PRINT '✅ إنشاء جدول custom_role_permissions'
PRINT '✅ إنشاء الفهارس لتحسين الأداء'
PRINT '✅ اختبار البنية والتأكد من سلامتها'
PRINT ''
PRINT '🚀 الخطوات التالية:'
PRINT '1. أعد تشغيل التطبيق (الباك اند)'
PRINT '2. اختبر إضافة صلاحية جديدة من Flutter'
PRINT '3. تحقق من عدم ظهور أخطاء في Console'
PRINT '4. شغل verify_database_structure.sql للتحقق الإضافي'

GO
