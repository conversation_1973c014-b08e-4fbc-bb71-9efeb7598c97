-- سكريبت النسخ الاحتياطي والتراجع لجدول الصلاحيات
-- تاريخ الإنشاء: 2025-01-06
-- الهدف: إنشاء نسخة احتياطية قبل الإصلاح وإمكانية التراجع

-- ==========================================
-- الجزء الأول: إنشاء نسخة احتياطية
-- ==========================================

-- إنشاء جدول النسخة الاحتياطية
DROP TABLE IF EXISTS permissions_backup_20250106;

CREATE TABLE permissions_backup_20250106 AS 
SELECT * FROM permissions;

-- التحقق من نجاح النسخة الاحتياطية
SELECT 
    'تم إنشاء النسخة الاحتياطية بنجاح' as status,
    COUNT(*) as total_records_backed_up,
    NOW() as backup_time
FROM permissions_backup_20250106;

-- ==========================================
-- الجزء الثاني: سكريبت التراجع (Rollback)
-- ==========================================

-- في حالة الحاجة للتراجع، قم بتشغيل الأوامر التالية:

/*
-- بدء معاملة التراجع
BEGIN TRANSACTION;

-- استعادة السجلات المتأثرة من النسخة الاحتياطية
UPDATE permissions p
JOIN permissions_backup_20250106 pb ON p.id = pb.id
SET 
    p.description = pb.description,
    p.name = pb.name,
    p.permission_group = pb.permission_group,
    p.category = pb.category,
    p.updated_at = pb.updated_at
WHERE p.id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
);

-- التحقق من التراجع
SELECT 
    'تم التراجع بنجاح' as status,
    COUNT(*) as restored_records
FROM permissions p
JOIN permissions_backup_20250106 pb ON p.id = pb.id
WHERE p.description = pb.description;

-- إنهاء معاملة التراجع
COMMIT;
*/

-- ==========================================
-- الجزء الثالث: مقارنة البيانات قبل وبعد الإصلاح
-- ==========================================

-- مقارنة الأوصاف قبل وبعد الإصلاح
SELECT 
    p.id,
    p.name as current_name,
    pb.name as backup_name,
    p.description as current_description,
    pb.description as backup_description,
    CASE 
        WHEN p.description != pb.description THEN 'تم التعديل'
        ELSE 'لم يتم التعديل'
    END as modification_status
FROM permissions p
JOIN permissions_backup_20250106 pb ON p.id = pb.id
WHERE p.id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
)
ORDER BY p.id;

-- ==========================================
-- الجزء الرابع: تنظيف النسخة الاحتياطية (اختياري)
-- ==========================================

-- في حالة التأكد من نجاح الإصلاح وعدم الحاجة للتراجع
-- يمكن حذف النسخة الاحتياطية لتوفير المساحة

/*
-- حذف النسخة الاحتياطية (تشغيل فقط عند التأكد)
DROP TABLE IF EXISTS permissions_backup_20250106;
SELECT 'تم حذف النسخة الاحتياطية' as cleanup_status;
*/

-- ==========================================
-- الجزء الخامس: إحصائيات التعديل
-- ==========================================

-- عرض إحصائيات شاملة للتعديلات
SELECT 
    'إحصائيات التعديل' as report_type,
    COUNT(*) as total_compared,
    SUM(CASE WHEN p.description != pb.description THEN 1 ELSE 0 END) as modified_descriptions,
    SUM(CASE WHEN p.name != pb.name THEN 1 ELSE 0 END) as modified_names,
    SUM(CASE WHEN p.permission_group != pb.permission_group THEN 1 ELSE 0 END) as modified_groups
FROM permissions p
JOIN permissions_backup_20250106 pb ON p.id = pb.id
WHERE p.id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
);

-- عرض السجلات التي تم إصلاحها بنجاح
SELECT 
    p.id,
    p.name,
    p.description as fixed_description,
    p.permission_group
FROM permissions p
WHERE p.id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
)
AND p.description REGEXP '[ا-ي]'  -- التحقق من وجود أحرف عربية صحيحة
ORDER BY p.id;
