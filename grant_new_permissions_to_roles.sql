-- =====================================================
-- سكريبت منح الصلاحيات الجديدة للأدوار الأساسية
-- تاريخ الإنشاء: 2025-01-06
-- الوصف: منح الصلاحيات الجديدة للأدوار حسب مستوى الصلاحية
-- =====================================================

-- التحقق من وجود الجداول المطلوبة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'permissions')
   OR NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'roles')
   OR NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'role_permissions')
BEGIN
    PRINT 'خطأ: أحد الجداول المطلوبة غير موجود!'
    PRINT 'الجداول المطلوبة: permissions, roles, role_permissions'
    RETURN
END

-- بدء المعاملة
BEGIN TRANSACTION;

BEGIN TRY
    PRINT 'بدء منح الصلاحيات الجديدة للأدوار...'
    PRINT ''
    
    -- =====================================================
    -- 👑 منح جميع الصلاحيات لدور المدير العام (Super Admin)
    -- =====================================================
    
    PRINT '👑 منح الصلاحيات لدور المدير العام...'
    
    -- الحصول على معرف دور المدير العام
    DECLARE @SuperAdminRoleId INT = (SELECT id FROM roles WHERE name = 'Super Admin' OR name = 'مدير عام' OR name = 'super_admin')
    
    IF @SuperAdminRoleId IS NOT NULL
    BEGIN
        -- منح جميع الصلاحيات الجديدة للمدير العام
        INSERT INTO role_permissions (role_id, permission_id, granted_at)
        SELECT DISTINCT @SuperAdminRoleId, p.id, GETDATE()
        FROM permissions p
        WHERE (p.type = 'messages' AND p.scope IN ('mark_followup', 'pin', 'edit', 'delete', 'reply'))
           OR (p.type = 'admin' AND p.scope IN ('test_permissions', 'debug', 'database_repair'))
           OR (p.type = 'settings' AND p.scope = 'notifications')
           OR (p.type = 'reports' AND p.scope = 'dynamic_access')
           OR (p.type = 'archive' AND p.scope = 'view_documents')
           OR (p.type = 'search' AND p.scope = 'manage_history')
        AND NOT EXISTS (
            SELECT 1 FROM role_permissions rp 
            WHERE rp.role_id = @SuperAdminRoleId AND rp.permission_id = p.id
        )
        
        PRINT '✅ تم منح جميع الصلاحيات الجديدة (12 صلاحية) للمدير العام'
    END
    ELSE
        PRINT '⚠️ لم يتم العثور على دور المدير العام'

    -- =====================================================
    -- 🔧 منح صلاحيات الإدارة لدور المدير (Admin)
    -- =====================================================
    
    PRINT ''
    PRINT '🔧 منح الصلاحيات لدور المدير...'
    
    -- الحصول على معرف دور المدير
    DECLARE @AdminRoleId INT = (SELECT id FROM roles WHERE name = 'Admin' OR name = 'مدير' OR name = 'admin')
    
    IF @AdminRoleId IS NOT NULL
    BEGIN
        -- منح صلاحيات الإدارة والنظام
        INSERT INTO role_permissions (role_id, permission_id, granted_at)
        SELECT DISTINCT @AdminRoleId, p.id, GETDATE()
        FROM permissions p
        WHERE (p.type = 'admin' AND p.scope IN ('test_permissions', 'debug', 'database_repair'))
           OR (p.type = 'settings' AND p.scope = 'notifications')
           OR (p.type = 'reports' AND p.scope = 'dynamic_access')
           OR (p.type = 'archive' AND p.scope = 'view_documents')
           OR (p.type = 'search' AND p.scope = 'manage_history')
        AND NOT EXISTS (
            SELECT 1 FROM role_permissions rp 
            WHERE rp.role_id = @AdminRoleId AND rp.permission_id = p.id
        )
        
        PRINT '✅ تم منح صلاحيات الإدارة والنظام (7 صلاحيات) للمدير'
    END
    ELSE
        PRINT '⚠️ لم يتم العثور على دور المدير'

    -- =====================================================
    -- 👥 منح صلاحيات الرسائل لدور المشرف (Moderator)
    -- =====================================================
    
    PRINT ''
    PRINT '👥 منح الصلاحيات لدور المشرف...'
    
    -- الحصول على معرف دور المشرف
    DECLARE @ModeratorRoleId INT = (SELECT id FROM roles WHERE name = 'Moderator' OR name = 'مشرف' OR name = 'moderator')
    
    IF @ModeratorRoleId IS NOT NULL
    BEGIN
        -- منح صلاحيات الرسائل والبحث
        INSERT INTO role_permissions (role_id, permission_id, granted_at)
        SELECT DISTINCT @ModeratorRoleId, p.id, GETDATE()
        FROM permissions p
        WHERE (p.type = 'messages' AND p.scope IN ('pin', 'edit', 'delete', 'reply'))
           OR (p.type = 'search' AND p.scope = 'manage_history')
           OR (p.type = 'archive' AND p.scope = 'view_documents')
        AND NOT EXISTS (
            SELECT 1 FROM role_permissions rp 
            WHERE rp.role_id = @ModeratorRoleId AND rp.permission_id = p.id
        )
        
        PRINT '✅ تم منح صلاحيات الرسائل والبحث (6 صلاحيات) للمشرف'
    END
    ELSE
        PRINT '⚠️ لم يتم العثور على دور المشرف'

    -- =====================================================
    -- 📊 منح صلاحيات التقارير لدور محلل البيانات
    -- =====================================================
    
    PRINT ''
    PRINT '📊 منح الصلاحيات لدور محلل البيانات...'
    
    -- الحصول على معرف دور محلل البيانات
    DECLARE @AnalystRoleId INT = (SELECT id FROM roles WHERE name LIKE '%analyst%' OR name LIKE '%محلل%' OR name LIKE '%تقارير%')
    
    IF @AnalystRoleId IS NOT NULL
    BEGIN
        -- منح صلاحيات التقارير والأرشيف
        INSERT INTO role_permissions (role_id, permission_id, granted_at)
        SELECT DISTINCT @AnalystRoleId, p.id, GETDATE()
        FROM permissions p
        WHERE (p.type = 'reports' AND p.scope = 'dynamic_access')
           OR (p.type = 'archive' AND p.scope = 'view_documents')
           OR (p.type = 'search' AND p.scope = 'manage_history')
        AND NOT EXISTS (
            SELECT 1 FROM role_permissions rp 
            WHERE rp.role_id = @AnalystRoleId AND rp.permission_id = p.id
        )
        
        PRINT '✅ تم منح صلاحيات التقارير والأرشيف (3 صلاحيات) لمحلل البيانات'
    END
    ELSE
        PRINT '⚠️ لم يتم العثور على دور محلل البيانات'

    -- =====================================================
    -- 👤 منح صلاحيات أساسية لدور المستخدم العادي
    -- =====================================================
    
    PRINT ''
    PRINT '👤 منح الصلاحيات لدور المستخدم العادي...'
    
    -- الحصول على معرف دور المستخدم العادي
    DECLARE @UserRoleId INT = (SELECT id FROM roles WHERE name = 'User' OR name = 'مستخدم' OR name = 'user')
    
    IF @UserRoleId IS NOT NULL
    BEGIN
        -- منح صلاحيات أساسية للمستخدم العادي
        INSERT INTO role_permissions (role_id, permission_id, granted_at)
        SELECT DISTINCT @UserRoleId, p.id, GETDATE()
        FROM permissions p
        WHERE (p.type = 'messages' AND p.scope IN ('reply', 'mark_followup'))
           OR (p.type = 'archive' AND p.scope = 'view_documents')
        AND NOT EXISTS (
            SELECT 1 FROM role_permissions rp 
            WHERE rp.role_id = @UserRoleId AND rp.permission_id = p.id
        )
        
        PRINT '✅ تم منح الصلاحيات الأساسية (3 صلاحيات) للمستخدم العادي'
    END
    ELSE
        PRINT '⚠️ لم يتم العثور على دور المستخدم العادي'

    -- =====================================================
    -- التحقق من النتائج النهائية
    -- =====================================================
    
    PRINT ''
    PRINT '📊 ملخص منح الصلاحيات للأدوار:'
    PRINT '=================================='
    
    -- عرض إحصائيات منح الصلاحيات
    SELECT 
        r.name AS role_name,
        COUNT(rp.permission_id) AS new_permissions_count,
        STRING_AGG(p.type + '.' + p.scope, ', ') AS granted_permissions
    FROM roles r
    LEFT JOIN role_permissions rp ON r.id = rp.role_id
    LEFT JOIN permissions p ON rp.permission_id = p.id
    WHERE (p.type = 'messages' AND p.scope IN ('mark_followup', 'pin', 'edit', 'delete', 'reply'))
       OR (p.type = 'admin' AND p.scope IN ('test_permissions', 'debug', 'database_repair'))
       OR (p.type = 'settings' AND p.scope = 'notifications')
       OR (p.type = 'reports' AND p.scope = 'dynamic_access')
       OR (p.type = 'archive' AND p.scope = 'view_documents')
       OR (p.type = 'search' AND p.scope = 'manage_history')
    GROUP BY r.id, r.name
    ORDER BY COUNT(rp.permission_id) DESC

    -- تأكيد المعاملة
    COMMIT TRANSACTION;
    
    PRINT ''
    PRINT '🎉 تم إكمال منح الصلاحيات للأدوار بنجاح!'
    PRINT '✅ جميع الأدوار تم تحديثها بالصلاحيات المناسبة'
    PRINT ''
    PRINT '📝 ملاحظات مهمة:'
    PRINT '• تأكد من مراجعة الصلاحيات الممنوحة لكل دور'
    PRINT '• يمكنك تعديل الصلاحيات حسب احتياجات مؤسستك'
    PRINT '• استخدم واجهة إدارة الأدوار لإجراء تعديلات إضافية'

END TRY
BEGIN CATCH
    -- في حالة حدوث خطأ
    ROLLBACK TRANSACTION;
    
    PRINT ''
    PRINT '❌ حدث خطأ أثناء منح الصلاحيات:'
    PRINT 'رقم الخطأ: ' + CAST(ERROR_NUMBER() AS VARCHAR(10))
    PRINT 'رسالة الخطأ: ' + ERROR_MESSAGE()
    PRINT 'السطر: ' + CAST(ERROR_LINE() AS VARCHAR(10))
    
END CATCH

-- نهاية السكريبت
