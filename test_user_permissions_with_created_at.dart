import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/user_permission_model.dart';

/// اختبار UserPermission مع createdAt
void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'User Permissions Test with CreatedAt',
      home: UserPermissionsTestScreen(),
    );
  }
}

class UserPermissionsTestScreen extends StatefulWidget {
  @override
  _UserPermissionsTestScreenState createState() => _UserPermissionsTestScreenState();
}

class _UserPermissionsTestScreenState extends State<UserPermissionsTestScreen> {
  
  void testUserPermissionWithCreatedAt() {
    print('🧪 اختبار UserPermission مع createdAt...');
    
    try {
      final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      
      // إنشاء UserPermission مع createdAt
      final userPermission = UserPermission(
        id: 0,
        userId: 20,
        permissionId: 45,
        grantedBy: 21,
        grantedAt: currentTime,
        isActive: true,
        isDeleted: false,
        createdAt: currentTime,
      );
      
      print('✅ تم إنشاء UserPermission مع createdAt بنجاح');
      print('📋 البيانات: ${userPermission.toJson()}');
      print('📋 البيانات للAPI: ${userPermission.toJsonForApi()}');
      
      // التحقق من صحة البيانات
      final json = userPermission.toJsonForApi();
      validateUserPermissionData(json);
      
      print('✅ جميع التحققات نجحت مع createdAt');
      
    } catch (e) {
      print('❌ خطأ في اختبار UserPermission مع createdAt: $e');
    }
  }
  
  void testUserPermissionFromJsonWithCreatedAt() {
    print('\n🧪 اختبار تحليل UserPermission من JSON مع createdAt...');
    
    try {
      // JSON مع createdAt
      final jsonWithCreatedAt = {
        'id': 1,
        'userId': 20,
        'permissionId': 45,
        'grantedBy': 21,
        'grantedAt': 1751754054,
        'isActive': true,
        'expiresAt': null,
        'isDeleted': false,
        'createdAt': 1751754054,
      };
      
      final userPermission = UserPermission.fromJson(jsonWithCreatedAt);
      print('✅ تم تحليل JSON مع createdAt بنجاح');
      print('📋 البيانات المحللة: ${userPermission.toJson()}');
      
      // JSON بدون createdAt (يجب أن يستخدم grantedAt)
      final jsonWithoutCreatedAt = {
        'id': 2,
        'userId': 20,
        'permissionId': 15,
        'grantedBy': 21,
        'grantedAt': 1751754054,
        'isActive': true,
        'expiresAt': null,
        'isDeleted': false,
      };
      
      final userPermission2 = UserPermission.fromJson(jsonWithoutCreatedAt);
      print('✅ تم تحليل JSON بدون createdAt بنجاح (استخدم grantedAt)');
      print('📋 البيانات المحللة: ${userPermission2.toJson()}');
      print('📋 createdAt = ${userPermission2.createdAt}, grantedAt = ${userPermission2.grantedAt}');
      
    } catch (e) {
      print('❌ خطأ في تحليل JSON مع createdAt: $e');
    }
  }
  
  void testDataValidationWithCreatedAt() {
    print('\n🧪 اختبار التحقق من صحة البيانات مع createdAt...');
    
    final currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    
    // البيانات الصحيحة
    final validData = {
      'id': 0,
      'userId': 20,
      'permissionId': 45,
      'grantedBy': 21,
      'grantedAt': currentTime,
      'isActive': true,
      'expiresAt': null,
      'isDeleted': false,
      'createdAt': currentTime,
    };
    
    print('📋 البيانات الصحيحة: $validData');
    
    try {
      validateUserPermissionData(validData);
      print('✅ البيانات الصحيحة مع createdAt تم التحقق منها بنجاح');
    } catch (e) {
      print('❌ البيانات الصحيحة فشلت في التحقق: $e');
    }
    
    // البيانات بدون createdAt
    final dataWithoutCreatedAt = {
      'id': 0,
      'userId': 20,
      'permissionId': 45,
      'grantedBy': 21,
      'grantedAt': currentTime,
      'isActive': true,
      'expiresAt': null,
      'isDeleted': false,
    };
    
    print('\n📋 البيانات بدون createdAt: $dataWithoutCreatedAt');
    
    try {
      validateUserPermissionData(dataWithoutCreatedAt);
      print('❌ البيانات بدون createdAt لم تفشل كما متوقع');
    } catch (e) {
      print('✅ البيانات بدون createdAt فشلت كما متوقع: $e');
    }
  }
  
  void validateUserPermissionData(Map<String, dynamic> data) {
    // التحقق من الحقول المطلوبة
    if (data['userId'] == null || data['userId'] <= 0) {
      throw Exception('userId مطلوب ويجب أن يكون أكبر من 0');
    }
    
    if (data['permissionId'] == null || data['permissionId'] <= 0) {
      throw Exception('permissionId مطلوب ويجب أن يكون أكبر من 0');
    }
    
    if (data['grantedBy'] == null || data['grantedBy'] <= 0) {
      throw Exception('grantedBy مطلوب ويجب أن يكون أكبر من 0');
    }
    
    if (data['grantedAt'] == null || data['grantedAt'] <= 0) {
      throw Exception('grantedAt مطلوب ويجب أن يكون أكبر من 0');
    }
    
    if (data['createdAt'] == null || data['createdAt'] <= 0) {
      throw Exception('createdAt مطلوب ويجب أن يكون أكبر من 0');
    }
    
    if (data['isActive'] == null) {
      throw Exception('isActive مطلوب');
    }
    
    if (data['isDeleted'] == null) {
      throw Exception('isDeleted مطلوب');
    }
  }
  
  void printDatabaseSchema() {
    print('\n📊 مخطط قاعدة البيانات:');
    print('CREATE TABLE [dbo].[user_permissions] (');
    print('    [id]            INT    IDENTITY (1, 1) NOT NULL,');
    print('    [user_id]       INT    NOT NULL,');
    print('    [permission_id] INT    NOT NULL,');
    print('    [granted_by]    INT    NOT NULL,');
    print('    [granted_at]    BIGINT NOT NULL,');
    print('    [is_active]     BIT    DEFAULT ((1)) NOT NULL,');
    print('    [expires_at]    BIGINT NULL,');
    print('    [is_deleted]    BIT    DEFAULT ((0)) NOT NULL,');
    print('    [created_at]    BIGINT DEFAULT (datediff(second,\'1970-01-01\',getutcdate())) NOT NULL,');
    print('    PRIMARY KEY CLUSTERED ([id] ASC),');
    print('    CONSTRAINT [UQ_user_permission] UNIQUE NONCLUSTERED ([user_id] ASC, [permission_id] ASC)');
    print(');');
    print('\n🔍 الملاحظات:');
    print('- created_at مطلوب (NOT NULL) مع قيمة افتراضية');
    print('- granted_at مطلوب (NOT NULL)');
    print('- يوجد قيد فريد على (user_id, permission_id)');
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('User Permissions Test with CreatedAt'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: testUserPermissionWithCreatedAt,
              child: Text('اختبار UserPermission مع createdAt'),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: testUserPermissionFromJsonWithCreatedAt,
              child: Text('اختبار تحليل JSON مع createdAt'),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: testDataValidationWithCreatedAt,
              child: Text('اختبار التحقق من البيانات'),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: printDatabaseSchema,
              child: Text('عرض مخطط قاعدة البيانات'),
            ),
            SizedBox(height: 20),
            Text(
              'تحقق من Console للنتائج',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}
