-- ===================================================================
-- إضافة الصلاحيات المتبقية إلى قاعدة البيانات
-- تاريخ الإنشاء: 2025-01-06
-- الوصف: إضافة الصلاحيات المتبقية (الملف الشخصي، قاعدة البيانات، الاختبار)
-- ===================================================================

USE [databasetasks]
GO

PRINT '🚀 بدء إضافة الصلاحيات المتبقية...'

-- ===================================================================
-- 6. صلاحيات الملف الشخصي
-- ===================================================================

PRINT '👤 إضافة صلاحيات الملف الشخصي...'

-- تغيير كلمة المرور
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'profile.change_password')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('profile.change_password', N'تغيير كلمة المرور', 'profile', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: profile.change_password'
END

-- ===================================================================
-- 7. صلاحيات قاعدة البيانات
-- ===================================================================

PRINT '🗄️ إضافة صلاحيات قاعدة البيانات...'

-- إصلاح قاعدة البيانات
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'database.repair')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('database.repair', N'إصلاح قاعدة البيانات', 'database', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: database.repair'
END

-- نسخ احتياطي لقاعدة البيانات
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'database.backup')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('database.backup', N'إنشاء نسخ احتياطية لقاعدة البيانات', 'database', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: database.backup'
END

-- استعادة قاعدة البيانات
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'database.restore')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('database.restore', N'استعادة النسخ الاحتياطية لقاعدة البيانات', 'database', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: database.restore'
END

-- ===================================================================
-- 8. صلاحيات الاختبار والتشخيص
-- ===================================================================

PRINT '🧪 إضافة صلاحيات الاختبار...'

-- اختبار الصلاحيات
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'admin.test_permissions')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('admin.test_permissions', N'اختبار الصلاحيات والتحقق منها', 'admin', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: admin.test_permissions'
END

-- أدوات التشخيص
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'admin.debug')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('admin.debug', N'أدوات التشخيص والتطوير', 'admin', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: admin.debug'
END

PRINT '✅ تم الانتهاء من إضافة جميع الصلاحيات المتبقية!'
PRINT '📊 عدد الصلاحيات المضافة في هذه المجموعة: 5 صلاحيات'

-- ===================================================================
-- التحقق من النتائج النهائية
-- ===================================================================

PRINT '🔍 التحقق من جميع الصلاحيات المضافة...'

SELECT 
    [permission_group] as 'مجموعة الصلاحيات',
    COUNT(*) as 'عدد الصلاحيات'
FROM [dbo].[permissions] 
WHERE [name] IN (
    'tasks.view_details', 'tasks.update_progress', 'tasks.filter', 'tasks.sort', 'tasks.manage_board',
    'attachments.upload', 'attachments.delete', 'attachments.view',
    'messages.pin', 'messages.edit', 'messages.delete', 'messages.reply', 'messages.mark_followup',
    'departments.assign_manager', 'departments.add_users', 'departments.remove_users', 'departments.manage_users',
    'reports.contributions', 'reports.pdf', 'reports.workload',
    'profile.change_password',
    'database.repair', 'database.backup', 'database.restore',
    'admin.test_permissions', 'admin.debug'
)
GROUP BY [permission_group]
ORDER BY [permission_group]

PRINT ''
PRINT '📊 إجمالي الصلاحيات المضافة: 25 صلاحية'
PRINT '🎉 تم الانتهاء بنجاح من إضافة جميع الصلاحيات المفقودة!'

-- عرض قائمة بجميع الصلاحيات المضافة
PRINT ''
PRINT '📋 قائمة الصلاحيات المضافة:'
SELECT [name] as 'اسم الصلاحية', [permission_group] as 'المجموعة', [description] as 'الوصف'
FROM [dbo].[permissions] 
WHERE [name] IN (
    'tasks.view_details', 'tasks.update_progress', 'tasks.filter', 'tasks.sort', 'tasks.manage_board',
    'attachments.upload', 'attachments.delete', 'attachments.view',
    'messages.pin', 'messages.edit', 'messages.delete', 'messages.reply', 'messages.mark_followup',
    'departments.assign_manager', 'departments.add_users', 'departments.remove_users', 'departments.manage_users',
    'reports.contributions', 'reports.pdf', 'reports.workload',
    'profile.change_password',
    'database.repair', 'database.backup', 'database.restore',
    'admin.test_permissions', 'admin.debug'
)
ORDER BY [permission_group], [name]
