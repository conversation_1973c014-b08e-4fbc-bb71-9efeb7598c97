-- =====================================================
-- سكريبت إضافة الصلاحيات الجديدة (محدث لبنية الجدول الصحيحة)
-- تاريخ الإنشاء: 2025-01-06
-- الوصف: إضافة 12 صلاحية جديدة تم تطبيقها في المشروع
-- =====================================================

-- التحقق من وجود جدول الصلاحيات
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'permissions')
BEGIN
    PRINT 'خطأ: جدول permissions غير موجود!'
    RETURN
END

-- بدء المعاملة
BEGIN TRANSACTION;

BEGIN TRY
    PRINT 'بدء إضافة الصلاحيات الجديدة...'
    
    -- =====================================================
    -- 💬 صلاحيات الرسائل الإضافية (5 صلاحيات)
    -- =====================================================
    
    -- 1. تحديد الرسائل للمتابعة
    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'messages.mark_followup')
    BEGIN
        INSERT INTO permissions (name, description, permission_group, created_at, category)
        VALUES ('messages.mark_followup', 'تحديد الرسائل للمتابعة', 'messages', DATEDIFF(SECOND, '1970-01-01', GETDATE()), 'رسائل');
        PRINT '✅ تم إضافة صلاحية: messages.mark_followup'
    END
    ELSE
        PRINT '⚠️ صلاحية messages.mark_followup موجودة مسبقاً'

    -- 2. تثبيت الرسائل
    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'messages.pin')
    BEGIN
        INSERT INTO permissions (name, description, permission_group, created_at, category)
        VALUES ('messages.pin', 'تثبيت الرسائل', 'messages', DATEDIFF(SECOND, '1970-01-01', GETDATE()), 'رسائل');
        PRINT '✅ تم إضافة صلاحية: messages.pin'
    END
    ELSE
        PRINT '⚠️ صلاحية messages.pin موجودة مسبقاً'

    -- 3. تعديل الرسائل
    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'messages.edit')
    BEGIN
        INSERT INTO permissions (name, description, permission_group, created_at, category)
        VALUES ('messages.edit', 'تعديل الرسائل', 'messages', DATEDIFF(SECOND, '1970-01-01', GETDATE()), 'رسائل');
        PRINT '✅ تم إضافة صلاحية: messages.edit'
    END
    ELSE
        PRINT '⚠️ صلاحية messages.edit موجودة مسبقاً'

    -- 4. حذف الرسائل
    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'messages.delete')
    BEGIN
        INSERT INTO permissions (name, description, permission_group, created_at, category)
        VALUES ('messages.delete', 'حذف الرسائل', 'messages', DATEDIFF(SECOND, '1970-01-01', GETDATE()), 'رسائل');
        PRINT '✅ تم إضافة صلاحية: messages.delete'
    END
    ELSE
        PRINT '⚠️ صلاحية messages.delete موجودة مسبقاً'

    -- 5. الرد على الرسائل
    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'messages.reply')
    BEGIN
        INSERT INTO permissions (name, description, permission_group, created_at, category)
        VALUES ('messages.reply', 'الرد على الرسائل', 'messages', DATEDIFF(SECOND, '1970-01-01', GETDATE()), 'رسائل');
        PRINT '✅ تم إضافة صلاحية: messages.reply'
    END
    ELSE
        PRINT '⚠️ صلاحية messages.reply موجودة مسبقاً'

    -- =====================================================
    -- 🔧 صلاحيات النظام والإدارة (7 صلاحيات)
    -- =====================================================
    
    -- 6. اختبار الصلاحيات
    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'admin.test_permissions')
    BEGIN
        INSERT INTO permissions (name, description, permission_group, created_at, category)
        VALUES ('admin.test_permissions', 'اختبار الصلاحيات', 'admin', DATEDIFF(SECOND, '1970-01-01', GETDATE()), 'إدارة');
        PRINT '✅ تم إضافة صلاحية: admin.test_permissions'
    END
    ELSE
        PRINT '⚠️ صلاحية admin.test_permissions موجودة مسبقاً'

    -- 7. الوصول لأدوات التشخيص
    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'admin.debug')
    BEGIN
        INSERT INTO permissions (name, description, permission_group, created_at, category)
        VALUES ('admin.debug', 'الوصول لأدوات التشخيص والتطوير', 'admin', DATEDIFF(SECOND, '1970-01-01', GETDATE()), 'إدارة');
        PRINT '✅ تم إضافة صلاحية: admin.debug'
    END
    ELSE
        PRINT '⚠️ صلاحية admin.debug موجودة مسبقاً'

    -- 8. تكوين إعدادات الإشعارات
    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'settings.notifications')
    BEGIN
        INSERT INTO permissions (name, description, permission_group, created_at, category)
        VALUES ('settings.notifications', 'تكوين إعدادات الإشعارات', 'settings', DATEDIFF(SECOND, '1970-01-01', GETDATE()), 'إعدادات');
        PRINT '✅ تم إضافة صلاحية: settings.notifications'
    END
    ELSE
        PRINT '⚠️ صلاحية settings.notifications موجودة مسبقاً'

    -- 9. الوصول للتقارير الديناميكية
    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'reports.dynamic_access')
    BEGIN
        INSERT INTO permissions (name, description, permission_group, created_at, category)
        VALUES ('reports.dynamic_access', 'الوصول للتقارير الديناميكية المتقدمة', 'reports', DATEDIFF(SECOND, '1970-01-01', GETDATE()), 'تقارير');
        PRINT '✅ تم إضافة صلاحية: reports.dynamic_access'
    END
    ELSE
        PRINT '⚠️ صلاحية reports.dynamic_access موجودة مسبقاً'

    -- 10. إصلاح قاعدة البيانات
    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'admin.database_repair')
    BEGIN
        INSERT INTO permissions (name, description, permission_group, created_at, category)
        VALUES ('admin.database_repair', 'الوصول لأدوات إصلاح قاعدة البيانات', 'admin', DATEDIFF(SECOND, '1970-01-01', GETDATE()), 'إدارة');
        PRINT '✅ تم إضافة صلاحية: admin.database_repair'
    END
    ELSE
        PRINT '⚠️ صلاحية admin.database_repair موجودة مسبقاً'

    -- 11. عرض مستندات الأرشيف
    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'archive.view_documents')
    BEGIN
        INSERT INTO permissions (name, description, permission_group, created_at, category)
        VALUES ('archive.view_documents', 'عرض وتصفح مستندات الأرشيف', 'archive', DATEDIFF(SECOND, '1970-01-01', GETDATE()), 'أرشيف');
        PRINT '✅ تم إضافة صلاحية: archive.view_documents'
    END
    ELSE
        PRINT '⚠️ صلاحية archive.view_documents موجودة مسبقاً'

    -- 12. إدارة سجل البحث
    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'search.manage_history')
    BEGIN
        INSERT INTO permissions (name, description, permission_group, created_at, category)
        VALUES ('search.manage_history', 'إدارة ومسح سجل البحث', 'search', DATEDIFF(SECOND, '1970-01-01', GETDATE()), 'بحث');
        PRINT '✅ تم إضافة صلاحية: search.manage_history'
    END
    ELSE
        PRINT '⚠️ صلاحية search.manage_history موجودة مسبقاً'

    -- =====================================================
    -- التحقق من النتائج
    -- =====================================================
    
    PRINT ''
    PRINT '📊 ملخص الصلاحيات المضافة:'
    
    -- عدد صلاحيات الرسائل
    DECLARE @MessagesCount INT = (
        SELECT COUNT(*) FROM permissions 
        WHERE permission_group = 'messages' AND name IN ('messages.mark_followup', 'messages.pin', 'messages.edit', 'messages.delete', 'messages.reply')
    )
    PRINT '💬 صلاحيات الرسائل: ' + CAST(@MessagesCount AS VARCHAR(10)) + ' من 5'
    
    -- عدد صلاحيات النظام
    DECLARE @AdminCount INT = (
        SELECT COUNT(*) FROM permissions 
        WHERE name IN ('admin.test_permissions', 'admin.debug', 'settings.notifications', 'reports.dynamic_access', 'admin.database_repair', 'archive.view_documents', 'search.manage_history')
    )
    PRINT '🔧 صلاحيات النظام: ' + CAST(@AdminCount AS VARCHAR(10)) + ' من 7'
    
    -- المجموع الكلي
    DECLARE @TotalCount INT = @MessagesCount + @AdminCount
    PRINT '📈 المجموع الكلي: ' + CAST(@TotalCount AS VARCHAR(10)) + ' من 12'
    
    -- تأكيد المعاملة
    COMMIT TRANSACTION;
    PRINT ''
    PRINT '🎉 تم إكمال إضافة الصلاحيات بنجاح!'
    PRINT '✅ جميع الصلاحيات الجديدة متاحة الآن في النظام'

END TRY
BEGIN CATCH
    -- في حالة حدوث خطأ
    ROLLBACK TRANSACTION;
    
    PRINT ''
    PRINT '❌ حدث خطأ أثناء إضافة الصلاحيات:'
    PRINT 'رقم الخطأ: ' + CAST(ERROR_NUMBER() AS VARCHAR(10))
    PRINT 'رسالة الخطأ: ' + ERROR_MESSAGE()
    PRINT 'السطر: ' + CAST(ERROR_LINE() AS VARCHAR(10))
    
END CATCH

-- =====================================================
-- استعلام للتحقق من الصلاحيات المضافة
-- =====================================================

PRINT ''
PRINT '🔍 استعلام التحقق من الصلاحيات الجديدة:'
PRINT '================================================'

SELECT 
    name AS permission_key,
    description,
    permission_group,
    category,
    created_at,
    CASE 
        WHEN permission_group = 'messages' THEN '💬 رسائل'
        WHEN permission_group = 'admin' THEN '🔧 إدارة'
        WHEN permission_group = 'settings' THEN '⚙️ إعدادات'
        WHEN permission_group = 'reports' THEN '📊 تقارير'
        WHEN permission_group = 'archive' THEN '📁 أرشيف'
        WHEN permission_group = 'search' THEN '🔍 بحث'
        ELSE '❓ أخرى'
    END AS category_icon
FROM permissions 
WHERE name IN (
    'messages.mark_followup', 'messages.pin', 'messages.edit', 'messages.delete', 'messages.reply',
    'admin.test_permissions', 'admin.debug', 'settings.notifications', 'reports.dynamic_access', 
    'admin.database_repair', 'archive.view_documents', 'search.manage_history'
)
ORDER BY 
    CASE permission_group 
        WHEN 'messages' THEN 1
        WHEN 'admin' THEN 2
        WHEN 'settings' THEN 3
        WHEN 'reports' THEN 4
        WHEN 'archive' THEN 5
        WHEN 'search' THEN 6
        ELSE 7
    END,
    name;

PRINT ''
PRINT '📝 ملاحظة: يمكنك الآن استخدام هذه الصلاحيات في التطبيق!'
PRINT '🔗 تأكد من تحديث أدوار المستخدمين لتشمل الصلاحيات الجديدة حسب الحاجة.'

-- نهاية السكريبت
