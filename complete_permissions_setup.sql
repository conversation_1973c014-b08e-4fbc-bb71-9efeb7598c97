-- =====================================================
-- سكريبت شامل لإعداد الصلاحيات الجديدة
-- تاريخ الإنشاء: 2025-01-06
-- الوصف: إضافة الصلاحيات الجديدة ومنحها للأدوار المناسبة
-- =====================================================

-- إعدادات السكريبت
SET NOCOUNT ON;
PRINT '🚀 بدء إعداد الصلاحيات الجديدة الشامل...'
PRINT '=============================================='
PRINT ''

-- التحقق من وجود الجداول المطلوبة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'permissions')
   OR NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'roles')
   OR NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'role_permissions')
BEGIN
    PRINT '❌ خطأ: أحد الجداول المطلوبة غير موجود!'
    PRINT 'الجداول المطلوبة: permissions, roles, role_permissions'
    RETURN
END

-- بدء المعاملة الرئيسية
BEGIN TRANSACTION MainTransaction;

BEGIN TRY

    -- =====================================================
    -- المرحلة الأولى: إضافة الصلاحيات الجديدة
    -- =====================================================
    
    PRINT '📝 المرحلة 1: إضافة الصلاحيات الجديدة...'
    PRINT ''
    
    -- متغيرات لحساب الصلاحيات المضافة
    DECLARE @AddedPermissions INT = 0;
    
    -- جدول مؤقت للصلاحيات الجديدة
    CREATE TABLE #NewPermissions (
        type NVARCHAR(50),
        scope NVARCHAR(50),
        description NVARCHAR(255),
        category NVARCHAR(50)
    );
    
    -- إدراج الصلاحيات الجديدة في الجدول المؤقت
    INSERT INTO #NewPermissions VALUES
    -- صلاحيات الرسائل
    ('messages', 'mark_followup', 'تحديد الرسائل للمتابعة', 'رسائل'),
    ('messages', 'pin', 'تثبيت الرسائل', 'رسائل'),
    ('messages', 'edit', 'تعديل الرسائل', 'رسائل'),
    ('messages', 'delete', 'حذف الرسائل', 'رسائل'),
    ('messages', 'reply', 'الرد على الرسائل', 'رسائل'),
    -- صلاحيات النظام
    ('admin', 'test_permissions', 'اختبار الصلاحيات', 'إدارة'),
    ('admin', 'debug', 'الوصول لأدوات التشخيص والتطوير', 'إدارة'),
    ('settings', 'notifications', 'تكوين إعدادات الإشعارات', 'إعدادات'),
    ('reports', 'dynamic_access', 'الوصول للتقارير الديناميكية المتقدمة', 'تقارير'),
    ('admin', 'database_repair', 'الوصول لأدوات إصلاح قاعدة البيانات', 'إدارة'),
    ('archive', 'view_documents', 'عرض وتصفح مستندات الأرشيف', 'أرشيف'),
    ('search', 'manage_history', 'إدارة ومسح سجل البحث', 'بحث');
    
    -- إضافة الصلاحيات الجديدة
    INSERT INTO permissions (type, scope, description, created_at)
    SELECT np.type, np.scope, np.description, GETDATE()
    FROM #NewPermissions np
    WHERE NOT EXISTS (
        SELECT 1 FROM permissions p 
        WHERE p.type = np.type AND p.scope = np.scope
    );
    
    SET @AddedPermissions = @@ROWCOUNT;
    
    PRINT '✅ تم إضافة ' + CAST(@AddedPermissions AS VARCHAR(10)) + ' صلاحية جديدة'
    
    -- عرض الصلاحيات المضافة
    IF @AddedPermissions > 0
    BEGIN
        PRINT ''
        PRINT '📋 الصلاحيات المضافة:'
        SELECT 
            '✅ ' + np.category + ': ' + np.type + '.' + np.scope + ' - ' + np.description AS added_permission
        FROM #NewPermissions np
        WHERE NOT EXISTS (
            SELECT 1 FROM permissions p 
            WHERE p.type = np.type AND p.scope = np.scope 
            AND p.created_at < DATEADD(MINUTE, -1, GETDATE())
        )
    END

    -- =====================================================
    -- المرحلة الثانية: منح الصلاحيات للأدوار
    -- =====================================================
    
    PRINT ''
    PRINT '🔐 المرحلة 2: منح الصلاحيات للأدوار...'
    PRINT ''
    
    -- جدول مؤقت لتتبع منح الصلاحيات
    CREATE TABLE #RolePermissionGrants (
        role_name NVARCHAR(100),
        permission_key NVARCHAR(100),
        granted BIT DEFAULT 0
    );
    
    -- تعريف قواعد منح الصلاحيات للأدوار
    DECLARE @SuperAdminRoleId INT, @AdminRoleId INT, @ModeratorRoleId INT, @UserRoleId INT;
    
    -- الحصول على معرفات الأدوار
    SELECT @SuperAdminRoleId = id FROM roles WHERE name IN ('Super Admin', 'مدير عام', 'super_admin');
    SELECT @AdminRoleId = id FROM roles WHERE name IN ('Admin', 'مدير', 'admin');
    SELECT @ModeratorRoleId = id FROM roles WHERE name IN ('Moderator', 'مشرف', 'moderator');
    SELECT @UserRoleId = id FROM roles WHERE name IN ('User', 'مستخدم', 'user');
    
    -- منح الصلاحيات للمدير العام (جميع الصلاحيات)
    IF @SuperAdminRoleId IS NOT NULL
    BEGIN
        INSERT INTO role_permissions (role_id, permission_id, granted_at)
        SELECT DISTINCT @SuperAdminRoleId, p.id, GETDATE()
        FROM permissions p
        INNER JOIN #NewPermissions np ON p.type = np.type AND p.scope = np.scope
        WHERE NOT EXISTS (
            SELECT 1 FROM role_permissions rp 
            WHERE rp.role_id = @SuperAdminRoleId AND rp.permission_id = p.id
        );
        
        PRINT '👑 تم منح جميع الصلاحيات الجديدة للمدير العام'
    END
    
    -- منح الصلاحيات للمدير (صلاحيات الإدارة والنظام)
    IF @AdminRoleId IS NOT NULL
    BEGIN
        INSERT INTO role_permissions (role_id, permission_id, granted_at)
        SELECT DISTINCT @AdminRoleId, p.id, GETDATE()
        FROM permissions p
        INNER JOIN #NewPermissions np ON p.type = np.type AND p.scope = np.scope
        WHERE np.category IN ('إدارة', 'إعدادات', 'تقارير', 'أرشيف', 'بحث')
        AND NOT EXISTS (
            SELECT 1 FROM role_permissions rp 
            WHERE rp.role_id = @AdminRoleId AND rp.permission_id = p.id
        );
        
        PRINT '🔧 تم منح صلاحيات الإدارة والنظام للمدير'
    END
    
    -- منح الصلاحيات للمشرف (صلاحيات الرسائل والبحث)
    IF @ModeratorRoleId IS NOT NULL
    BEGIN
        INSERT INTO role_permissions (role_id, permission_id, granted_at)
        SELECT DISTINCT @ModeratorRoleId, p.id, GETDATE()
        FROM permissions p
        INNER JOIN #NewPermissions np ON p.type = np.type AND p.scope = np.scope
        WHERE (np.category = 'رسائل' AND np.scope IN ('pin', 'edit', 'delete', 'reply'))
           OR np.category IN ('بحث', 'أرشيف')
        AND NOT EXISTS (
            SELECT 1 FROM role_permissions rp 
            WHERE rp.role_id = @ModeratorRoleId AND rp.permission_id = p.id
        );
        
        PRINT '👥 تم منح صلاحيات الرسائل والبحث للمشرف'
    END
    
    -- منح الصلاحيات للمستخدم العادي (صلاحيات أساسية)
    IF @UserRoleId IS NOT NULL
    BEGIN
        INSERT INTO role_permissions (role_id, permission_id, granted_at)
        SELECT DISTINCT @UserRoleId, p.id, GETDATE()
        FROM permissions p
        INNER JOIN #NewPermissions np ON p.type = np.type AND p.scope = np.scope
        WHERE (np.category = 'رسائل' AND np.scope IN ('reply', 'mark_followup'))
           OR np.category = 'أرشيف'
        AND NOT EXISTS (
            SELECT 1 FROM role_permissions rp 
            WHERE rp.role_id = @UserRoleId AND rp.permission_id = p.id
        );
        
        PRINT '👤 تم منح الصلاحيات الأساسية للمستخدم العادي'
    END

    -- =====================================================
    -- المرحلة الثالثة: التحقق والتقرير النهائي
    -- =====================================================
    
    PRINT ''
    PRINT '📊 المرحلة 3: التحقق من النتائج...'
    PRINT ''
    
    -- إحصائيات الصلاحيات الجديدة
    DECLARE @TotalNewPermissions INT = (
        SELECT COUNT(*) FROM permissions p
        INNER JOIN #NewPermissions np ON p.type = np.type AND p.scope = np.scope
    );
    
    -- إحصائيات منح الصلاحيات
    DECLARE @TotalGrants INT = (
        SELECT COUNT(*) FROM role_permissions rp
        INNER JOIN permissions p ON rp.permission_id = p.id
        INNER JOIN #NewPermissions np ON p.type = np.type AND p.scope = np.scope
        WHERE rp.granted_at >= DATEADD(MINUTE, -5, GETDATE())
    );
    
    PRINT '📈 تقرير الإحصائيات النهائية:'
    PRINT '=============================='
    PRINT '• إجمالي الصلاحيات الجديدة: ' + CAST(@TotalNewPermissions AS VARCHAR(10)) + ' من 12'
    PRINT '• إجمالي منح الصلاحيات: ' + CAST(@TotalGrants AS VARCHAR(10))
    PRINT '• الأدوار المحدثة: ' + CAST((
        SELECT COUNT(DISTINCT r.name) 
        FROM roles r
        INNER JOIN role_permissions rp ON r.id = rp.role_id
        INNER JOIN permissions p ON rp.permission_id = p.id
        INNER JOIN #NewPermissions np ON p.type = np.type AND p.scope = np.scope
        WHERE rp.granted_at >= DATEADD(MINUTE, -5, GETDATE())
    ) AS VARCHAR(10))
    
    -- تنظيف الجداول المؤقتة
    DROP TABLE #NewPermissions;
    DROP TABLE #RolePermissionGrants;
    
    -- تأكيد المعاملة
    COMMIT TRANSACTION MainTransaction;
    
    PRINT ''
    PRINT '🎉 تم إكمال إعداد الصلاحيات الجديدة بنجاح!'
    PRINT '✅ النظام جاهز الآن مع جميع الصلاحيات الجديدة'
    PRINT ''
    PRINT '📝 الخطوات التالية الموصى بها:'
    PRINT '• اختبار الصلاحيات الجديدة في التطبيق'
    PRINT '• مراجعة منح الصلاحيات للأدوار حسب احتياجاتك'
    PRINT '• تحديث وثائق النظام لتشمل الصلاحيات الجديدة'
    PRINT '• إعلام المستخدمين بالميزات الجديدة المتاحة'

END TRY
BEGIN CATCH
    -- في حالة حدوث خطأ
    ROLLBACK TRANSACTION MainTransaction;
    
    PRINT ''
    PRINT '❌ حدث خطأ أثناء إعداد الصلاحيات:'
    PRINT '=================================='
    PRINT 'رقم الخطأ: ' + CAST(ERROR_NUMBER() AS VARCHAR(10))
    PRINT 'رسالة الخطأ: ' + ERROR_MESSAGE()
    PRINT 'الإجراء: ' + ERROR_PROCEDURE()
    PRINT 'السطر: ' + CAST(ERROR_LINE() AS VARCHAR(10))
    PRINT ''
    PRINT '🔄 يرجى مراجعة الخطأ وإعادة تشغيل السكريبت'
    
END CATCH

-- إعادة تعيين الإعدادات
SET NOCOUNT OFF;

PRINT ''
PRINT '📋 انتهى تنفيذ السكريبت'
PRINT '======================'

-- نهاية السكريبت
