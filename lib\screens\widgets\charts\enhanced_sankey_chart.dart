import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/advanced_filter_options.dart';
import 'package:flutter_application_2/models/chart_enums.dart';



/// نموذج بيانات الرابط في مخطط سانكي
class SankeyLink {
  /// المصدر
  final String source;

  /// الوجهة
  final String target;

  /// القيمة
  final double value;

  /// اللون (اختياري)
  final Color? color;

  /// إنشاء رابط سانكي
  const SankeyLink({
    required this.source,
    required this.target,
    required this.value,
    this.color,
  });
}

/// نموذج بيانات العقدة في مخطط سانكي
class SankeyNode {
  /// معرف العقدة
  final String id;

  /// اسم العقدة
  final String name;

  /// لون العقدة (اختياري)
  final Color? color;

  /// إنشاء عقدة سانكي
  const SankeyNode({
    required this.id,
    required this.name,
    this.color,
  });
}

/// مكون مخطط سانكي محسن
///
/// يوفر هذا المكون مخططًا سانكي لعرض تدفق البيانات بين العقد
class EnhancedSankeyChart extends StatefulWidget {
  /// روابط المخطط
  final List<SankeyLink> links;

  /// عقد المخطط (اختياري، يمكن استنتاجها من الروابط)
  final List<SankeyNode>? nodes;

  /// عنوان المخطط
  final String? title;

  /// دالة استدعاء عند تغيير التصفية
  final Function(DateTime?, DateTime?, TimeFilterType)? onFilterChanged;

  /// دالة استدعاء عند التصدير
  final Function(String)? onExport;

  /// دالة استدعاء عند تغيير نوع المخطط
  final Function(ChartType, String chartKey)? onChartTypeChanged;

  /// هل يتم عرض خيارات التصفية
  final bool showFilterOptions;

  /// هل يتم عرض خيارات التصدير
  final bool showExportOptions;

  /// هل يتم عرض خيارات نوع المخطط
  final bool showChartTypeOptions;

  /// تنسيقات التصدير المدعومة
  final List<String> supportedExportFormats;

  /// أنواع المخططات المدعومة
  final List<ChartType> supportedChartTypes;

  /// نوع المخطط
  final ChartType chartType;

  /// خيارات الفلتر المتقدمة
  final AdvancedFilterOptions advancedFilterOptions;

  /// ألوان العقد المخصصة
  final Map<String, Color>? nodeColors;

  /// عرض قيم الروابط
  final bool showLinkValues;

  /// عرض أسماء العقد
  final bool showNodeNames;

  /// إنشاء مكون مخطط سانكي محسن
  const EnhancedSankeyChart({
    super.key,
    required this.links,
    this.nodes,
    this.title,
    this.onFilterChanged,
    this.onExport,
    this.onChartTypeChanged,
    this.showFilterOptions = false,
    this.showExportOptions = false,
    this.showChartTypeOptions = false,
    this.supportedExportFormats = const ['pdf', 'excel', 'csv', 'png'],
    this.supportedChartTypes = const [
      ChartType.funnel,
      ChartType.pie,
      ChartType.bar,
    ],
    required this.chartType,
    required this.advancedFilterOptions,
    this.nodeColors,
    this.showLinkValues = false,
    this.showNodeNames = true,
  });

  @override
  State<EnhancedSankeyChart> createState() => _EnhancedSankeyChartState();
}

class _EnhancedSankeyChartState extends State<EnhancedSankeyChart> {
  /// قائمة العقد المستنتجة
  late List<SankeyNode> _nodes;

  /// العقدة المحددة
  String? _selectedNodeId;

  @override
  void initState() {
    super.initState();
    _initializeNodes();
  }

  /// تهيئة العقد من الروابط إذا لم يتم توفيرها
  void _initializeNodes() {
    if (widget.nodes != null) {
      _nodes = List.from(widget.nodes!);

      // تطبيق الألوان المخصصة إذا كانت متوفرة
      if (widget.nodeColors != null) {
        for (int i = 0; i < _nodes.length; i++) {
          final nodeId = _nodes[i].id;
          if (widget.nodeColors!.containsKey(nodeId)) {
            _nodes[i] = SankeyNode(
              id: nodeId,
              name: _nodes[i].name,
              color: widget.nodeColors![nodeId],
            );
          }
        }
      }
      return;
    }

    // استخراج العقد الفريدة من الروابط
    final Set<String> nodeIds = {};
    for (final link in widget.links) {
      nodeIds.add(link.source);
      nodeIds.add(link.target);
    }

    // إنشاء قائمة العقد مع الألوان المخصصة
    _nodes = nodeIds.map((id) {
      // استخدام الألوان المخصصة إذا كانت متوفرة
      Color? nodeColor;
      if (widget.nodeColors != null && widget.nodeColors!.containsKey(id)) {
        nodeColor = widget.nodeColors![id];
      } else {
        // استخدام لون عشوائي من الألوان الأساسية
        nodeColor = Colors
            .primaries[nodeIds.toList().indexOf(id) % Colors.primaries.length];
      }

      return SankeyNode(id: id, name: id, color: nodeColor);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.links.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات للعرض'),
      );
    }

    return Column(
      children: [
        // عنوان المخطط
        if (widget.title != null && widget.title!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              widget.title!,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

        // المخطط سانكي
        Expanded(
          child: _buildSankeyChart(),
        ),
      ],
    );
  }

  /// بناء مخطط سانكي
  Widget _buildSankeyChart() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          children: [
            // إضافة مفتاح الألوان (legend)
            if (_nodes.isNotEmpty && widget.showNodeNames)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Wrap(
                  spacing: 16,
                  runSpacing: 8,
                  alignment: WrapAlignment.center,
                  children: _nodes.map((node) {
                    // حساب إجمالي القيمة للعقدة
                    double totalValue = 0;
                    for (final link in widget.links) {
                      if (link.source == node.id) {
                        totalValue += link.value;
                      }
                    }

                    return Tooltip(
                      message: '${node.name}: ${totalValue.toStringAsFixed(0)}',
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: node.color ?? Colors.blue,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            node.name,
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),

            // مخطط سانكي
            Expanded(
              child: _SankeyLayout(
                links: widget.links,
                nodes: _nodes,
                width: constraints.maxWidth,
                height: constraints.maxHeight -
                    (_nodes.isEmpty || !widget.showNodeNames ? 0 : 40),
                showLinkValues: widget.showLinkValues,
                showNodeNames: widget.showNodeNames,
                onNodeSelected: (nodeId) {
                  setState(() {
                    _selectedNodeId =
                        (_selectedNodeId == nodeId) ? null : nodeId;
                  });

                  // عرض معلومات إضافية عن العقدة
                  if (_selectedNodeId != null) {
                    final selectedNode = _nodes.firstWhere(
                      (node) => node.id == _selectedNodeId,
                      orElse: () => _nodes.first,
                    );

                    // حساب الروابط الداخلة والخارجة
                    final incomingLinks = widget.links
                        .where((link) => link.target == _selectedNodeId)
                        .toList();
                    final outgoingLinks = widget.links
                        .where((link) => link.source == _selectedNodeId)
                        .toList();

                    // حساب إجمالي القيم
                    final incomingTotal = incomingLinks.fold<double>(
                        0, (sum, link) => sum + link.value);
                    final outgoingTotal = outgoingLinks.fold<double>(
                        0, (sum, link) => sum + link.value);

                    // عرض معلومات العقدة
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: Text(selectedNode.name),
                        content: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                                'إجمالي الوارد: ${incomingTotal.toStringAsFixed(0)}'),
                            const SizedBox(height: 8),
                            Text(
                                'إجمالي الصادر: ${outgoingTotal.toStringAsFixed(0)}'),
                            if (incomingLinks.isNotEmpty) ...[
                              const SizedBox(height: 16),
                              const Text('الروابط الواردة:',
                                  style:
                                      TextStyle(fontWeight: FontWeight.bold)),
                              ...incomingLinks.map((link) {
                                final sourceNode = _nodes.firstWhere(
                                  (node) => node.id == link.source,
                                  orElse: () => SankeyNode(
                                      id: link.source, name: link.source),
                                );
                                return Text(
                                    '${sourceNode.name}: ${link.value.toStringAsFixed(0)}');
                              }),
                            ],
                            if (outgoingLinks.isNotEmpty) ...[
                              const SizedBox(height: 16),
                              const Text('الروابط الصادرة:',
                                  style:
                                      TextStyle(fontWeight: FontWeight.bold)),
                              ...outgoingLinks.map((link) {
                                final targetNode = _nodes.firstWhere(
                                  (node) => node.id == link.target,
                                  orElse: () => SankeyNode(
                                      id: link.target, name: link.target),
                                );
                                return Text(
                                    '${targetNode.name}: ${link.value.toStringAsFixed(0)}');
                              }),
                            ],
                          ],
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('إغلاق'),
                          ),
                        ],
                      ),
                    );
                  }
                },
                selectedNodeId: _selectedNodeId,
              ),
            ),
          ],
        );
      },
    );
  }
}

/// مكون تخطيط سانكي
class _SankeyLayout extends StatelessWidget {
  final List<SankeyLink> links;
  final List<SankeyNode> nodes;
  final double width;
  final double height;
  final Function(String)? onNodeSelected;
  final String? selectedNodeId;
  final bool showLinkValues;
  final bool showNodeNames;

  const _SankeyLayout({
    required this.links,
    required this.nodes,
    required this.width,
    required this.height,
    this.onNodeSelected,
    this.selectedNodeId,
    this.showLinkValues = false,
    this.showNodeNames = true,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(width, height),
      painter: _SankeyPainter(
        links: links,
        nodes: nodes,
        selectedNodeId: selectedNodeId,
        showLinkValues: showLinkValues,
        showNodeNames: showNodeNames,
      ),
      child: GestureDetector(
        onTapUp: (details) {
          _handleTap(details.localPosition);
        },
      ),
    );
  }

  /// معالجة النقر على المخطط
  void _handleTap(Offset position) {
    // تنفيذ منطق تحديد العقدة عند النقر
    // (سيتم تنفيذه في الإصدار النهائي)
    if (onNodeSelected != null) {
      // تحديد العقدة المنقورة
      // onNodeSelected!(nodeId);
    }
  }
}

/// رسام مخطط سانكي
class _SankeyPainter extends CustomPainter {
  final List<SankeyLink> links;
  final List<SankeyNode> nodes;
  final String? selectedNodeId;
  final bool showLinkValues;
  final bool showNodeNames;

  // مواقع العقد
  final Map<String, Rect> _nodePositions = {};

  _SankeyPainter({
    required this.links,
    required this.nodes,
    this.selectedNodeId,
    this.showLinkValues = false,
    this.showNodeNames = true,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // حساب القيم الإجمالية والمواقع
    _calculateNodePositions(size);

    // رسم الروابط
    _drawLinks(canvas, size);

    // رسم العقد
    _drawNodes(canvas, size);
  }

  // حساب مواقع العقد
  void _calculateNodePositions(Size size) {
    // تحديد العقد المصدر والوجهة
    final sourceNodes = <String>{};
    final targetNodes = <String>{};

    for (final link in links) {
      sourceNodes.add(link.source);
      targetNodes.add(link.target);
    }

    // حساب قيم العقد
    final nodeValues = <String, double>{};

    for (final link in links) {
      nodeValues[link.source] = (nodeValues[link.source] ?? 0) + link.value;
      nodeValues[link.target] = (nodeValues[link.target] ?? 0) + link.value;
    }

    // تحديد مستويات العقد (الأعمدة)
    final nodeLevels = <String, int>{};

    // العقد المصدر فقط في المستوى 0
    for (final node in sourceNodes) {
      if (!targetNodes.contains(node)) {
        nodeLevels[node] = 0;
      }
    }

    // العقد الوجهة فقط في المستوى الأخير
    for (final node in targetNodes) {
      if (!sourceNodes.contains(node)) {
        nodeLevels[node] = 2;
      }
    }

    // العقد المشتركة في المستوى 1
    for (final node in sourceNodes.intersection(targetNodes)) {
      nodeLevels[node] = 1;
    }

    // حساب مواقع العقد
    final levelCounts = <int, int>{};
    final levelCurrentPos = <int, double>{};

    // حساب عدد العقد في كل مستوى
    for (final level in nodeLevels.values) {
      levelCounts[level] = (levelCounts[level] ?? 0) + 1;
      levelCurrentPos[level] = 0;
    }

    // حساب الارتفاع الكلي للعقد
    final totalHeight = size.height * 0.8;
    final nodeHeight = totalHeight / 10; // ارتفاع العقدة

    // حساب عرض المخطط
    final chartWidth = size.width * 0.8;
    final levelWidth = chartWidth / 3; // عرض المستوى

    // تعيين مواقع العقد
    for (final node in nodes) {
      final level = nodeLevels[node.id] ?? 0;
      final count = levelCounts[level] ?? 1;
      final pos = levelCurrentPos[level] ?? 0;

      // حساب موقع العقدة
      final x = size.width * 0.1 + level * levelWidth;
      final y = size.height * 0.1 + pos * (totalHeight / count);

      // تخزين موقع العقدة
      _nodePositions[node.id] = Rect.fromLTWH(
        x,
        y,
        levelWidth * 0.5,
        nodeHeight *
            (nodeValues[node.id] ?? 1) /
            (nodeValues.values.reduce((a, b) => a > b ? a : b)),
      );

      // تحديث الموقع الحالي للمستوى
      levelCurrentPos[level] = pos + 1;
    }
  }

  // رسم الروابط
  void _drawLinks(Canvas canvas, Size size) {
    for (final link in links) {
      // الحصول على مواقع العقد
      final sourceRect = _nodePositions[link.source];
      final targetRect = _nodePositions[link.target];

      if (sourceRect == null || targetRect == null) continue;

      // حساب نقاط التحكم
      final sourcePoint = Offset(
        sourceRect.right,
        sourceRect.center.dy,
      );

      final targetPoint = Offset(
        targetRect.left,
        targetRect.center.dy,
      );

      // إنشاء مسار الرابط
      final path = Path();
      path.moveTo(sourcePoint.dx, sourcePoint.dy);

      // نقاط التحكم للمنحنى
      final controlPoint1 = Offset(
        sourcePoint.dx + (targetPoint.dx - sourcePoint.dx) / 3,
        sourcePoint.dy,
      );

      final controlPoint2 = Offset(
        sourcePoint.dx + (targetPoint.dx - sourcePoint.dx) * 2 / 3,
        targetPoint.dy,
      );

      // رسم المنحنى
      path.cubicTo(
        controlPoint1.dx,
        controlPoint1.dy,
        controlPoint2.dx,
        controlPoint2.dy,
        targetPoint.dx,
        targetPoint.dy,
      );

      // تحديد سمك الرابط بناءً على القيمة
      final maxValue =
          links.map((l) => l.value).reduce((a, b) => a > b ? a : b);
      final strokeWidth = (link.value / maxValue) * 20.0;

      // رسم الرابط
      final paint = Paint()
        ..color = link.color ?? Colors.blue.withAlpha(128)
        ..style = PaintingStyle.stroke
        ..strokeWidth = strokeWidth;

      canvas.drawPath(path, paint);

      // عرض قيمة الرابط إذا كان مطلوبًا
      if (showLinkValues) {
        // حساب نقطة منتصف المنحنى
        final midPoint = Offset(
          (sourcePoint.dx + targetPoint.dx) / 2,
          (sourcePoint.dy + targetPoint.dy) / 2,
        );

        // إنشاء نص القيمة
        final textSpan = TextSpan(
          text: link.value.toStringAsFixed(0),
          style: const TextStyle(
            color: Colors.black,
            fontSize: 10,
            fontWeight: FontWeight.bold,
            backgroundColor: Color(0xCCFFFFFF), // خلفية بيضاء شبه شفافة
          ),
        );

        // رسم النص
        final textPainter = TextPainter(
          text: textSpan,
          textDirection: TextDirection.ltr,
          textAlign: TextAlign.center,
        );

        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(
            midPoint.dx - textPainter.width / 2,
            midPoint.dy - textPainter.height / 2,
          ),
        );
      }
    }
  }

  // رسم العقد
  void _drawNodes(Canvas canvas, Size size) {
    for (final node in nodes) {
      final rect = _nodePositions[node.id];
      if (rect == null) continue;

      // تحديد لون العقدة
      final color = node.color ??
          (node.id == selectedNodeId ? Colors.orange : Colors.blue);

      // رسم العقدة
      final paint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;

      // رسم مستطيل العقدة
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(8)),
        paint,
      );

      // رسم اسم العقدة إذا كان مطلوبًا
      if (showNodeNames) {
        final textPainter = TextPainter(
          text: TextSpan(
            text: node.name,
            style: TextStyle(
              color:
                  color.computeLuminance() > 0.5 ? Colors.black : Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          textDirection: TextDirection.rtl,
          textAlign: TextAlign.center,
        );

        textPainter.layout(maxWidth: rect.width);

        // وضع النص في وسط العقدة
        textPainter.paint(
          canvas,
          Offset(
            rect.left + (rect.width - textPainter.width) / 2,
            rect.top + (rect.height - textPainter.height) / 2,
          ),
        );
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
