-- سكريبت إضافة الصلاحيات المفقودة للنظام
-- يجب تشغيل هذا السكريبت على قاعدة البيانات databasetasks

USE [databasetasks]
GO

PRINT '🚀 بدء إضافة الصلاحيات المفقودة للنظام...'

-- التحقق من وجود الجداول المطلوبة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'permissions')
BEGIN
    PRINT '❌ جدول permissions غير موجود. يرجى إنشاء الجداول أولاً.'
    RETURN
END

-- عرض إحصائيات قبل التحديث
PRINT ''
PRINT '📊 إحصائيات قبل التحديث:'
SELECT
    COUNT(*) as [إجمالي الصلاحيات الحالية],
    COUNT(DISTINCT permission_group) as [عدد المجموعات]
FROM permissions

-- إضافة الصلاحيات المفقودة
DECLARE @CurrentTime BIGINT = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
DECLARE @AddedCount INT = 0

-- ===== الصلاحيات المفقودة من الكود =====

-- إدارة الصلاحيات العامة
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'permissions.manage')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('permissions.manage', N'إدارة صلاحيات النظام العامة', N'Permissions', N'إدارة متقدمة', 5, 'security', '#E91E63', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية permissions.manage'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية permissions.manage موجودة بالفعل'
END

-- إدارة أدوار المستخدمين
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'users.manage_roles')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('users.manage_roles', N'إدارة أدوار المستخدمين', N'Users', N'إدارة متقدمة', 4, 'admin_panel_settings', '#3F51B5', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية users.manage_roles'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية users.manage_roles موجودة بالفعل'
END

-- عرض جميع المستخدمين
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'users.view_all')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('users.view_all', N'عرض جميع المستخدمين في النظام', N'Users', N'عرض متقدم', 3, 'people', '#3F51B5', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية users.view_all'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية users.view_all موجودة بالفعل'
END

-- تعديل التقارير
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'reports.edit')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('reports.edit', N'تعديل التقارير الموجودة', N'Reports', N'تعديل', 2, 'edit', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية reports.edit'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية reports.edit موجودة بالفعل'
END

-- حذف التقارير
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'reports.delete')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('reports.delete', N'حذف التقارير', N'Reports', N'حذف', 3, 'delete', '#F44336', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية reports.delete'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية reports.delete موجودة بالفعل'
END

-- جدولة التقارير
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'reports.schedule')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('reports.schedule', N'جدولة التقارير التلقائية', N'Reports', N'جدولة', 3, 'schedule', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية reports.schedule'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية reports.schedule موجودة بالفعل'
END

-- ===== صلاحيات إضافية للتغطية الكاملة =====

-- إدارة المرفقات
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'attachments.manage')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('attachments.manage', N'إدارة المرفقات (رفع، حذف، تعديل)', N'Attachments', N'إدارة', 2, 'attach_file', '#607D8B', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية attachments.manage'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية attachments.manage موجودة بالفعل'
END

-- عرض المرفقات
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'attachments.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('attachments.view', N'عرض وتحميل المرفقات', N'Attachments', N'عرض', 1, 'attachment', '#607D8B', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية attachments.view'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية attachments.view موجودة بالفعل'
END

-- إدارة التعليقات
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'comments.manage')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('comments.manage', N'إدارة التعليقات (إضافة، تعديل، حذف)', N'Comments', N'إدارة', 2, 'comment', '#FF9800', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية comments.manage'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية comments.manage موجودة بالفعل'
END

-- عرض التعليقات
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'comments.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('comments.view', N'عرض التعليقات', N'Comments', N'عرض', 1, 'chat_bubble', '#FF9800', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية comments.view'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية comments.view موجودة بالفعل'
END

-- إدارة الإعدادات
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'settings.manage')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('settings.manage', N'إدارة إعدادات التطبيق', N'Settings', N'إدارة', 4, 'settings', '#9E9E9E', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية settings.manage'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية settings.manage موجودة بالفعل'
END

-- عرض الإعدادات
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'settings.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('settings.view', N'عرض إعدادات التطبيق', N'Settings', N'عرض', 2, 'visibility', '#9E9E9E', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية settings.view'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية settings.view موجودة بالفعل'
END

-- إدارة الأنشطة والسجلات
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'activities.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('activities.view', N'عرض سجل الأنشطة', N'Activities', N'عرض', 2, 'history', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية activities.view'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية activities.view موجودة بالفعل'
END

-- إدارة الإحصائيات
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'statistics.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('statistics.view', N'عرض الإحصائيات والتحليلات', N'Statistics', N'عرض', 2, 'analytics', '#2196F3', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية statistics.view'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية statistics.view موجودة بالفعل'
END

-- إدارة التصدير والاستيراد
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'data.export')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('data.export', N'تصدير البيانات', N'Data', N'تصدير', 3, 'file_download', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية data.export'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية data.export موجودة بالفعل'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'data.import')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('data.import', N'استيراد البيانات', N'Data', N'استيراد', 4, 'file_upload', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية data.import'
END
ELSE
BEGIN
    PRINT '⚠️ صلاحية data.import موجودة بالفعل'
END

-- عرض ملخص العملية
PRINT ''
PRINT '📊 ملخص عملية إضافة الصلاحيات:'

-- حساب عدد الصلاحيات المضافة
DECLARE @NewPermissionsCount INT
SELECT @NewPermissionsCount = COUNT(*) FROM permissions WHERE created_at >= @CurrentTime

IF @NewPermissionsCount > 0
BEGIN
    PRINT '✅ تم إضافة ' + CAST(@NewPermissionsCount AS NVARCHAR(10)) + ' صلاحية جديدة بنجاح!'

    -- عرض إحصائيات الصلاحيات المضافة الجديدة
    PRINT ''
    PRINT '📊 إحصائيات الصلاحيات الجديدة:'
    SELECT
        permission_group as [المجموعة],
        COUNT(*) as [عدد الصلاحيات الجديدة]
    FROM permissions
    WHERE created_at >= @CurrentTime
    GROUP BY permission_group
    ORDER BY permission_group
END
ELSE
BEGIN
    PRINT '⚠️ لم يتم إضافة أي صلاحيات جديدة - جميع الصلاحيات موجودة بالفعل!'
END

PRINT ''
PRINT '📋 إجمالي الصلاحيات في النظام بعد التحديث:'
SELECT
    COUNT(*) as [إجمالي الصلاحيات],
    SUM(CASE WHEN is_default = 1 THEN 1 ELSE 0 END) as [الصلاحيات الافتراضية],
    SUM(CASE WHEN is_default = 0 THEN 1 ELSE 0 END) as [الصلاحيات المتقدمة],
    COUNT(DISTINCT permission_group) as [عدد المجموعات]
FROM permissions

PRINT ''
PRINT '🎉 تم تحديث نظام الصلاحيات بنجاح!'
PRINT 'يمكنك الآن استخدام الصلاحيات الجديدة في التطبيق.'
PRINT ''
PRINT '💡 ملاحظة: تأكد من تحديث ملف UnifiedPermissionService لاستخدام الصلاحيات الجديدة.'

GO
