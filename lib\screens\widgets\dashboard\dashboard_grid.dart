import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/dashboard_model.dart' as dashboard_model;
import '../../../models/dashboard_widget_model.dart' as widget_model;
import '../../../utils/dashboard_widget_adapter.dart';

/// مكون شبكة لوحة المعلومات
///
/// يوفر شبكة مرنة لعرض وتنظيم عناصر لوحة المعلومات
/// مع دعم السحب والإفلات وإعادة تحجيم العناصر
class DashboardGrid extends StatefulWidget {
  /// لوحة المعلومات
  final dashboard_model.Dashboard dashboard;

  /// دالة يتم استدعاؤها عند تحديث لوحة المعلومات
  final Function(dashboard_model.Dashboard)? onDashboardUpdated;

  /// دالة يتم استدعاؤها عند النقر على عنصر
  final Function(widget_model.DashboardWidget)? onWidgetTap;

  /// دالة يتم استدعاؤها عند النقر المزدوج على عنصر
  final Function(widget_model.DashboardWidget)? onWidgetDoubleTap;

  /// وضع التحرير
  final bool editMode;

  const DashboardGrid({
    super.key,
    required this.dashboard,
    this.onDashboardUpdated,
    this.onWidgetTap,
    this.onWidgetDoubleTap,
    this.editMode = false,
  });

  @override
  State<DashboardGrid> createState() => _DashboardGridState();
}

class _DashboardGridState extends State<DashboardGrid> {
  // حجم الخلية
  double _cellWidth = 0;
  final double _cellHeight = 100.0;

  // ألوان حسب السمة - مستوحاة من Monday.com
  final Color _deleteColor = const Color(0xFFE2445C); // أحمر Monday.com
  final Color _expandColor = const Color(0xFFFFCB00); // أصفر Monday.com

  @override
  Widget build(BuildContext context) {
    // حساب عرض الخلية بناءً على عرض الشاشة وعدد الأعمدة
    _cellWidth =
        MediaQuery.of(context).size.width / widget.dashboard.gridColumns;

    // تحديد لون الخلفية حسب السمة
    final Color bgColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF181B34) // لون خلفية داكن مستوحى من Monday.com
        : const Color(0xFFF6F7FB); // لون خلفية فاتح مستوحى من Monday.com

    return Container(
      color: bgColor,
      child: Stack(
        children: [
          // شبكة الخلايا
          _buildGridLines(),

          // عناصر لوحة المعلومات
          ...widget.dashboard.widgets.map((dashboardWidget) =>
              _buildWidgetContent(DashboardWidgetAdapter.convertToWidgetModel(
                  dashboardWidget, widget.dashboard.id))),
        ],
      ),
    );
  }

  /// بناء خطوط الشبكة
  Widget _buildGridLines() {
    return CustomPaint(
      size: Size(
        MediaQuery.of(context).size.width,
        widget.dashboard.gridRows * _cellHeight,
      ),
      painter: GridPainter(
        columns: widget.dashboard.gridColumns,
        rows: widget.dashboard.gridRows,
        cellWidth: _cellWidth,
        cellHeight: _cellHeight,
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.white.withAlpha(13) // 0.05 * 255 = 13
            : Colors.black.withAlpha(13), // 0.05 * 255 = 13
      ),
    );
  }

  /// بناء محتوى العنصر
  Widget _buildWidgetContent(widget_model.DashboardWidget widget) {
    return Stack(
      children: [
        // المخطط
        // تم حذف جميع الأكواد المتعلقة بتحريك وتحجيم عناصر الداشبورد (السحب، الإفلات، تغيير الحجم، Draggable، onMove، onResize، Positioned، Offset، Size)

        // أزرار التحكم (تظهر فقط في وضع التحرير)
        if (this.widget.editMode) ...[
          // زر الحذف
          Positioned(
            top: 6,
            left: 6,
            child: _buildControlButton(
              icon: Icons.delete_outline_rounded,
              color: _deleteColor,
              bgColor: const Color(0x1AE2445C), // 0.1 opacity
              tooltip: 'حذف العنصر',
              onPressed: () => _deleteWidget(widget),
            ),
          ),

          // زر التوسيع (always show for now since widget_model doesn't have isExpandable)
          Positioned(
            top: 6,
            left: 42,
            child: _buildControlButton(
              icon: Icons.fullscreen,
              color: _expandColor,
              bgColor: const Color(0x1AFFCB00), // 0.1 opacity
              tooltip: 'توسيع العنصر',
              onPressed: () => _toggleWidgetExpansion(widget),
            ),
          ),
        ],
      ],
    );
  }

  /// بناء زر التحكم
  Widget _buildControlButton({
    required IconData icon,
    required Color color,
    required Color bgColor,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(4),
          child: Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: bgColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
        ),
      ),
    );
  }

  /// حذف العنصر
  void _deleteWidget(widget_model.DashboardWidget widget) {
    // عرض مربع حوار للتأكيد
    Get.defaultDialog(
      title: 'تأكيد الحذف',
      middleText: 'هل أنت متأكد من حذف هذا العنصر؟',
      textConfirm: 'حذف',
      textCancel: 'إلغاء',
      confirmTextColor: Colors.white,
      cancelTextColor: Colors.black,
      buttonColor: _deleteColor,
      onConfirm: () {
        // For now, just show a message since deleteWidget method doesn't exist
        Get.snackbar('تنبيه', 'وظيفة الحذف غير متاحة حاليًا');
        Get.back();
      },
    );
  }

  /// تبديل حالة توسيع العنصر
  void _toggleWidgetExpansion(widget_model.DashboardWidget widget) {
    // For now, just show a message since toggleWidgetExpansion method doesn't exist
    Get.snackbar('تنبيه', 'وظيفة التوسيع غير متاحة حاليًا');
  }
}

/// رسام الشبكة
class GridPainter extends CustomPainter {
  final int columns;
  final int rows;
  final double cellWidth;
  final double cellHeight;
  final Color color;

  GridPainter({
    required this.columns,
    required this.rows,
    required this.cellWidth,
    required this.cellHeight,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1;

    // رسم الخطوط الأفقية
    for (int i = 0; i <= rows; i++) {
      final y = i * cellHeight;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }

    // رسم الخطوط الرأسية
    for (int i = 0; i <= columns; i++) {
      final x = i * cellWidth;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
