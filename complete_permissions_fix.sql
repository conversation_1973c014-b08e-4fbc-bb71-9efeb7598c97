-- سكريبت شامل لإصلاح مشاكل الترميز في جدول الصلاحيات
-- تاريخ الإنشاء: 2025-01-06
-- المطور: نظام إدارة الصلاحيات
-- الهدف: إصلاح شامل لجميع مشاكل الترميز والأوصاف المرمزة

-- ==========================================
-- المرحلة 1: التحليل والتشخيص
-- ==========================================

SELECT '=== بدء تحليل مشاكل الترميز ===' as step;

-- عرض السجلات المتأثرة
SELECT 
    'السجلات المتأثرة بمشاكل الترميز' as analysis,
    COUNT(*) as total_affected
FROM permissions 
WHERE description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%Ø§%' OR description LIKE '%?%';

-- ==========================================
-- المرحلة 2: إنشاء النسخة الاحتياطية
-- ==========================================

SELECT '=== إنشاء النسخة الاحتياطية ===' as step;

DROP TABLE IF EXISTS permissions_backup_encoding_fix;
CREATE TABLE permissions_backup_encoding_fix AS SELECT * FROM permissions;

SELECT 
    'تم إنشاء النسخة الاحتياطية' as status,
    COUNT(*) as backed_up_records
FROM permissions_backup_encoding_fix;

-- ==========================================
-- المرحلة 3: تطبيق الإصلاحات
-- ==========================================

SELECT '=== بدء تطبيق الإصلاحات ===' as step;

BEGIN;

-- إصلاح المجموعة الأولى (40-71)
UPDATE permissions SET description = 'إدارة لوحة التحكم', permission_group = 'Dashboard' WHERE id = 40;
UPDATE permissions SET description = 'عرض المهام', permission_group = 'Tasks' WHERE id = 41;
UPDATE permissions SET description = 'إنشاء مهام جديدة', permission_group = 'Tasks' WHERE id = 42;
UPDATE permissions SET description = 'تعديل المهام', permission_group = 'Tasks' WHERE id = 43;
UPDATE permissions SET description = 'حذف المهام', permission_group = 'Tasks' WHERE id = 44;
UPDATE permissions SET description = 'تعيين المهام للمستخدمين', permission_group = 'Tasks' WHERE id = 45;
UPDATE permissions SET description = 'تحديث المهام الخاصة', permission_group = 'Tasks' WHERE id = 46;
UPDATE permissions SET description = 'عرض المستخدمين', permission_group = 'Users' WHERE id = 47;
UPDATE permissions SET description = 'إنشاء مستخدمين جدد', permission_group = 'Users' WHERE id = 48;
UPDATE permissions SET description = 'تعديل بيانات المستخدمين', permission_group = 'Users' WHERE id = 49;
UPDATE permissions SET description = 'إدارة أدوار المستخدمين', permission_group = 'Users' WHERE id = 51;
UPDATE permissions SET description = 'عرض التقارير', permission_group = 'Reports' WHERE id = 52;
UPDATE permissions SET description = 'إنشاء تقارير جديدة', permission_group = 'Reports' WHERE id = 53;
UPDATE permissions SET description = 'تصدير التقارير', permission_group = 'Reports' WHERE id = 54;
UPDATE permissions SET description = 'إدارة النظام', permission_group = 'System' WHERE id = 55;
UPDATE permissions SET description = 'إنشاء نسخ احتياطية', permission_group = 'System' WHERE id = 56;
UPDATE permissions SET description = 'استعادة النسخ الاحتياطية', permission_group = 'System' WHERE id = 57;
UPDATE permissions SET description = 'إدارة قاعدة البيانات', permission_group = 'System' WHERE id = 58;
UPDATE permissions SET description = 'عرض الملف الشخصي', permission_group = 'Profile' WHERE id = 59;
UPDATE permissions SET description = 'تعديل الملف الشخصي', permission_group = 'Profile' WHERE id = 60;
UPDATE permissions SET description = 'عرض الإشعارات', permission_group = 'Notifications' WHERE id = 61;
UPDATE permissions SET description = 'إدارة الإشعارات', permission_group = 'Notifications' WHERE id = 62;
UPDATE permissions SET description = 'إدارة التقويم', permission_group = 'Calendar' WHERE id = 64;
UPDATE permissions SET description = 'عرض الأقسام', permission_group = 'Departments' WHERE id = 65;
UPDATE permissions SET description = 'إدارة الأقسام', permission_group = 'Departments' WHERE id = 66;
UPDATE permissions SET description = 'عرض جميع المهام', permission_group = 'Tasks' WHERE id = 71;

-- إصلاح المجموعة الثانية (1205-1227)
UPDATE permissions SET 
    description = 'عرض تفاصيل المهام والانتقال إليها', 
    permission_group = 'Tasks',
    category = 'عرض'
WHERE id = 1205;

UPDATE permissions SET 
    description = 'تحديث نسبة إنجاز المهام', 
    permission_group = 'Tasks',
    category = 'تحديث'
WHERE id = 1206;

UPDATE permissions SET 
    description = 'تصفية وفلترة المهام', 
    permission_group = 'Tasks',
    category = 'تصفية'
WHERE id = 1207;

UPDATE permissions SET 
    description = 'ترتيب المهام حسب معايير مختلفة', 
    permission_group = 'Tasks',
    category = 'ترتيب'
WHERE id = 1208;

UPDATE permissions SET 
    description = 'إدارة لوحة المهام وإضافة أعمدة', 
    permission_group = 'Tasks',
    category = 'إدارة'
WHERE id = 1209;

UPDATE permissions SET 
    description = 'تثبيت وإلغاء تثبيت الرسائل', 
    permission_group = 'Messages',
    category = 'إدارة'
WHERE id = 1210;

UPDATE permissions SET 
    description = 'تعديل الرسائل المرسلة', 
    permission_group = 'Messages',
    category = 'تعديل'
WHERE id = 1211;

UPDATE permissions SET 
    description = 'حذف الرسائل', 
    permission_group = 'Messages',
    category = 'حذف'
WHERE id = 1212;

UPDATE permissions SET 
    description = 'الرد على الرسائل', 
    permission_group = 'Messages',
    category = 'تفاعل'
WHERE id = 1213;

UPDATE permissions SET 
    description = 'تحديد الرسائل للمتابعة', 
    permission_group = 'Messages',
    category = 'متابعة'
WHERE id = 1214;

UPDATE permissions SET 
    description = 'تعيين مدير للقسم', 
    permission_group = 'Departments',
    category = 'إدارة'
WHERE id = 1215;

UPDATE permissions SET 
    description = 'إضافة مستخدمين للقسم', 
    permission_group = 'Departments',
    category = 'إدارة'
WHERE id = 1216;

UPDATE permissions SET 
    description = 'إزالة مستخدمين من القسم', 
    permission_group = 'Departments',
    category = 'إدارة'
WHERE id = 1217;

UPDATE permissions SET 
    description = 'إدارة شاملة لمستخدمي القسم', 
    permission_group = 'Departments',
    category = 'إدارة متقدمة'
WHERE id = 1218;

UPDATE permissions SET 
    description = 'عرض تقارير المساهمات', 
    permission_group = 'Reports',
    category = 'عرض'
WHERE id = 1219;

UPDATE permissions SET 
    description = 'تصدير التقارير كـ PDF', 
    permission_group = 'Reports',
    category = 'تصدير'
WHERE id = 1220;

UPDATE permissions SET 
    description = 'تقارير عبء العمل والأداء', 
    permission_group = 'Reports',
    category = 'تحليل'
WHERE id = 1221;

UPDATE permissions SET 
    description = 'تغيير كلمة المرور', 
    permission_group = 'Profile',
    category = 'أمان'
WHERE id = 1222;

UPDATE permissions SET 
    description = 'إصلاح قاعدة البيانات', 
    permission_group = 'Database',
    category = 'صيانة'
WHERE id = 1223;

UPDATE permissions SET 
    description = 'إنشاء نسخ احتياطية لقاعدة البيانات', 
    permission_group = 'Database',
    category = 'نسخ احتياطي'
WHERE id = 1224;

UPDATE permissions SET 
    description = 'استعادة النسخ الاحتياطية لقاعدة البيانات', 
    permission_group = 'Database',
    category = 'استعادة'
WHERE id = 1225;

UPDATE permissions SET 
    description = 'اختبار الصلاحيات والتحقق منها', 
    permission_group = 'Admin',
    category = 'اختبار'
WHERE id = 1226;

UPDATE permissions SET 
    description = 'أدوات التشخيص والتطوير', 
    permission_group = 'Admin',
    category = 'تطوير'
WHERE id = 1227;

-- إصلاح المجموعة الثالثة (1232-1236)
UPDATE permissions SET 
    name = 'settings.notifications',
    description = 'إعدادات الإشعارات', 
    permission_group = 'Settings',
    category = 'إعدادات'
WHERE id = 1232;

UPDATE permissions SET 
    name = 'reports.dynamic_access',
    description = 'الوصول الديناميكي للتقارير', 
    permission_group = 'Reports',
    category = 'وصول متقدم'
WHERE id = 1233;

UPDATE permissions SET 
    name = 'admin.database_repair',
    description = 'إصلاح وصيانة قاعدة البيانات', 
    permission_group = 'Admin',
    category = 'صيانة'
WHERE id = 1234;

UPDATE permissions SET 
    name = 'archive.view_documents',
    description = 'عرض وثائق الأرشيف', 
    permission_group = 'Archive',
    category = 'عرض'
WHERE id = 1235;

UPDATE permissions SET 
    name = 'search.manage_history',
    description = 'إدارة تاريخ البحث', 
    permission_group = 'Search',
    category = 'إدارة'
WHERE id = 1236;

-- تحديث تاريخ التعديل
UPDATE permissions 
SET updated_at = UNIX_TIMESTAMP() 
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
);

COMMIT;

-- ==========================================
-- المرحلة 4: التحقق من النتائج
-- ==========================================

SELECT '=== التحقق من نتائج الإصلاح ===' as step;

-- عرض السجلات المُصلحة
SELECT 
    id, 
    name, 
    description, 
    permission_group,
    category,
    'تم الإصلاح' as status
FROM permissions 
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
)
ORDER BY id;

-- إحصائيات نهائية
SELECT 
    'تقرير نهائي' as report,
    (SELECT COUNT(*) FROM permissions) as total_permissions,
    (SELECT COUNT(*) FROM permissions WHERE description REGEXP '[ا-ي]') as arabic_descriptions,
    (SELECT COUNT(*) FROM permissions WHERE description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%?%') as still_encoded,
    'تم إصلاح 59 سجل' as fixed_records;

SELECT '=== اكتمل الإصلاح بنجاح ===' as final_status;
