-- إكمال إصلاح جميع الصلاحيات بالإنجليزية
USE databasetasks;

BEGIN TRANSACTION;

-- إصلاح باقي المجموعة الأولى
UPDATE permissions SET description = 'Create Backups' WHERE id = 56;
UPDATE permissions SET description = 'Restore Backups' WHERE id = 57;
UPDATE permissions SET description = 'Database Management' WHERE id = 58;
UPDATE permissions SET description = 'View Profile' WHERE id = 59;
UPDATE permissions SET description = 'Edit Profile' WHERE id = 60;
UPDATE permissions SET description = 'View Notifications' WHERE id = 61;
UPDATE permissions SET description = 'Manage Notifications' WHERE id = 62;
UPDATE permissions SET description = 'Calendar Management' WHERE id = 64;
UPDATE permissions SET description = 'View Departments' WHERE id = 65;
UPDATE permissions SET description = 'Manage Departments' WHERE id = 66;
UPDATE permissions SET description = 'View All Tasks' WHERE id = 71;

-- إ<PERSON>لاح المجموعة الثانية: السجلات 1205-1227
UPDATE permissions SET description = 'View Task Details and Navigate' WHERE id = 1205;
UPDATE permissions SET description = 'Update Task Progress' WHERE id = 1206;
UPDATE permissions SET description = 'Filter and Sort Tasks' WHERE id = 1207;
UPDATE permissions SET description = 'Sort Tasks by Different Criteria' WHERE id = 1208;
UPDATE permissions SET description = 'Manage Task Board and Add Columns' WHERE id = 1209;
UPDATE permissions SET description = 'Pin and Unpin Messages' WHERE id = 1210;
UPDATE permissions SET description = 'Edit Sent Messages' WHERE id = 1211;
UPDATE permissions SET description = 'Delete Messages' WHERE id = 1212;
UPDATE permissions SET description = 'Reply to Messages' WHERE id = 1213;
UPDATE permissions SET description = 'Mark Messages for Follow-up' WHERE id = 1214;
UPDATE permissions SET description = 'Assign Department Manager' WHERE id = 1215;
UPDATE permissions SET description = 'Add Users to Department' WHERE id = 1216;
UPDATE permissions SET description = 'Remove Users from Department' WHERE id = 1217;
UPDATE permissions SET description = 'Comprehensive Department User Management' WHERE id = 1218;
UPDATE permissions SET description = 'View Contribution Reports' WHERE id = 1219;
UPDATE permissions SET description = 'Export Reports as PDF' WHERE id = 1220;
UPDATE permissions SET description = 'Workload and Performance Reports' WHERE id = 1221;
UPDATE permissions SET description = 'Change Password' WHERE id = 1222;
UPDATE permissions SET description = 'Database Repair' WHERE id = 1223;
UPDATE permissions SET description = 'Create Database Backups' WHERE id = 1224;
UPDATE permissions SET description = 'Restore Database Backups' WHERE id = 1225;
UPDATE permissions SET description = 'Test and Verify Permissions' WHERE id = 1226;
UPDATE permissions SET description = 'Diagnostic and Development Tools' WHERE id = 1227;

-- إصلاح المجموعة الثالثة: السجلات 1232-1236
UPDATE permissions SET description = 'Notification Settings' WHERE id = 1232;
UPDATE permissions SET description = 'Dynamic Report Access' WHERE id = 1233;
UPDATE permissions SET description = 'Database Repair and Maintenance' WHERE id = 1234;
UPDATE permissions SET description = 'View Archive Documents' WHERE id = 1235;
UPDATE permissions SET description = 'Manage Search History' WHERE id = 1236;

-- إصلاح أي سجلات أخرى قد تحتوي على رموز
UPDATE permissions 
SET description = CASE 
    WHEN name = 'dashboard.admin' THEN 'Dashboard Management'
    WHEN name = 'tasks.view' THEN 'View Tasks'
    WHEN name = 'tasks.create' THEN 'Create New Tasks'
    WHEN name = 'tasks.edit' THEN 'Edit Tasks'
    WHEN name = 'tasks.delete' THEN 'Delete Tasks'
    WHEN name = 'tasks.assign' THEN 'Assign Tasks to Users'
    WHEN name = 'tasks.update_own' THEN 'Update Own Tasks'
    WHEN name = 'users.view' THEN 'View Users'
    WHEN name = 'users.create' THEN 'Create New Users'
    WHEN name = 'users.edit' THEN 'Edit User Data'
    WHEN name = 'users.manage_roles' THEN 'Manage User Roles'
    WHEN name = 'reports.view' THEN 'View Reports'
    WHEN name = 'reports.create' THEN 'Create New Reports'
    WHEN name = 'reports.export' THEN 'Export Reports'
    WHEN name = 'system.manage' THEN 'System Management'
    WHEN name = 'system.backup' THEN 'Create Backups'
    WHEN name = 'system.restore' THEN 'Restore Backups'
    WHEN name = 'database.manage' THEN 'Database Management'
    WHEN name = 'profile.view' THEN 'View Profile'
    WHEN name = 'profile.edit' THEN 'Edit Profile'
    WHEN name = 'notifications.view' THEN 'View Notifications'
    WHEN name = 'notifications.manage' THEN 'Manage Notifications'
    WHEN name = 'calendar.manage' THEN 'Calendar Management'
    WHEN name = 'departments.view' THEN 'View Departments'
    WHEN name = 'departments.manage' THEN 'Manage Departments'
    WHEN name = 'tasks.view_all' THEN 'View All Tasks'
    ELSE description
END
WHERE description LIKE '%O%' OR description LIKE '%U%' OR description LIKE '%?%';

COMMIT TRANSACTION;

-- التحقق النهائي
SELECT 
    'Final Check' as check_type,
    COUNT(*) as total_records,
    SUM(CASE WHEN description LIKE '%O%' OR description LIKE '%U%' OR description LIKE '%?%' THEN 1 ELSE 0 END) as still_encoded,
    SUM(CASE WHEN description NOT LIKE '%O%' AND description NOT LIKE '%U%' AND description NOT LIKE '%?%' THEN 1 ELSE 0 END) as clean_records
FROM permissions;

PRINT 'All permissions have been fixed with English descriptions!';
GO
