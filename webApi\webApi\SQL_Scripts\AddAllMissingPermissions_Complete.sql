-- سكريبت شامل لإضافة جميع الصلاحيات المفقودة للمشروع
-- تم تحليل جميع الشاشات والأزرار والعمليات في المشروع
-- يجب تشغيل هذا السكريبت على قاعدة البيانات databasetasks

USE [databasetasks]
GO

PRINT '🚀 بدء إضافة جميع الصلاحيات المفقودة للمشروع...'

-- التحقق من وجود الجداول المطلوبة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'permissions')
BEGIN
    PRINT '❌ جدول permissions غير موجود. يرجى إنشاء الجداول أولاً.'
    RETURN
END

-- إضافة الصلاحيات المفقودة
DECLARE @CurrentTime BIGINT = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())

-- ===== 1. صلاحيات البحث والاستعلام =====
PRINT '📍 إضافة صلاحيات البحث والاستعلام...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'search.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('search.view', N'عرض شاشة البحث الموحد', N'Search', N'عرض', 1, 'search', '#2196F3', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية search.view'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'search.advanced')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('search.advanced', N'البحث المتقدم', N'Search', N'بحث متقدم', 2, 'manage_search', '#2196F3', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية search.advanced'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'search.export')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('search.export', N'تصدير نتائج البحث', N'Search', N'تصدير', 2, 'file_download', '#2196F3', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية search.export'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.search')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.search', N'البحث في المحادثات', N'Chat', N'بحث', 1, 'search', '#00BCD4', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.search'
END

-- ===== 2. صلاحيات إدارة الملفات والمرفقات =====
PRINT '📍 إضافة صلاحيات إدارة الملفات والمرفقات...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'attachments.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('attachments.view', N'عرض المرفقات', N'Attachments', N'عرض', 1, 'attachment', '#607D8B', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية attachments.view'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'attachments.upload')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('attachments.upload', N'رفع المرفقات', N'Attachments', N'رفع', 2, 'cloud_upload', '#607D8B', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية attachments.upload'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'attachments.download')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('attachments.download', N'تحميل المرفقات', N'Attachments', N'تحميل', 1, 'cloud_download', '#607D8B', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية attachments.download'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'attachments.delete')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('attachments.delete', N'حذف المرفقات', N'Attachments', N'حذف', 3, 'delete', '#F44336', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية attachments.delete'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'attachments.share')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('attachments.share', N'مشاركة المرفقات', N'Attachments', N'مشاركة', 2, 'share', '#607D8B', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية attachments.share'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'files.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('files.view', N'عرض الملفات', N'Files', N'عرض', 1, 'folder', '#FF9800', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية files.view'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'files.upload')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('files.upload', N'رفع الملفات', N'Files', N'رفع', 2, 'file_upload', '#FF9800', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية files.upload'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'files.download')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('files.download', N'تحميل الملفات', N'Files', N'تحميل', 1, 'file_download', '#FF9800', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية files.download'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'files.delete')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('files.delete', N'حذف الملفات', N'Files', N'حذف', 3, 'delete_forever', '#F44336', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية files.delete'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'files.share')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('files.share', N'مشاركة الملفات', N'Files', N'مشاركة', 2, 'share', '#FF9800', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية files.share'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'files.preview')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('files.preview', N'معاينة الملفات', N'Files', N'معاينة', 1, 'preview', '#FF9800', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية files.preview'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'files.edit')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('files.edit', N'تحرير الملفات', N'Files', N'تحرير', 2, 'edit', '#FF9800', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية files.edit'
END

-- ===== 3. صلاحيات المستندات النصية =====
PRINT '📍 إضافة صلاحيات المستندات النصية...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'documents.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('documents.view', N'عرض المستندات النصية', N'Documents', N'عرض', 1, 'description', '#795548', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية documents.view'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'documents.create')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('documents.create', N'إنشاء مستندات نصية', N'Documents', N'إنشاء', 2, 'note_add', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية documents.create'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'documents.edit')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('documents.edit', N'تحرير المستندات النصية', N'Documents', N'تحرير', 2, 'edit_note', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية documents.edit'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'documents.delete')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('documents.delete', N'حذف المستندات النصية', N'Documents', N'حذف', 3, 'delete', '#F44336', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية documents.delete'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'documents.share')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('documents.share', N'مشاركة المستندات النصية', N'Documents', N'مشاركة', 2, 'share', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية documents.share'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'documents.export')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('documents.export', N'تصدير المستندات (PDF, Markdown)', N'Documents', N'تصدير', 2, 'file_download', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية documents.export'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'documents.version_history')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('documents.version_history', N'عرض تاريخ إصدارات المستندات', N'Documents', N'تاريخ', 2, 'history', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية documents.version_history'
END

-- ===== 4. صلاحيات التقارير المتقدمة =====
PRINT '📍 إضافة صلاحيات التقارير المتقدمة...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'reports.edit')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('reports.edit', N'تعديل التقارير', N'Reports', N'تعديل', 2, 'edit', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية reports.edit'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'reports.delete')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('reports.delete', N'حذف التقارير', N'Reports', N'حذف', 3, 'delete', '#F44336', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية reports.delete'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'reports.schedule')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('reports.schedule', N'جدولة التقارير', N'Reports', N'جدولة', 3, 'schedule', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية reports.schedule'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'reports.share')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('reports.share', N'مشاركة التقارير', N'Reports', N'مشاركة', 2, 'share', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية reports.share'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'reports.print')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('reports.print', N'طباعة التقارير', N'Reports', N'طباعة', 2, 'print', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية reports.print'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'reports.advanced')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('reports.advanced', N'التقارير المتقدمة', N'Reports', N'متقدم', 3, 'analytics', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية reports.advanced'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'reports.custom')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('reports.custom', N'التقارير المخصصة', N'Reports', N'مخصص', 3, 'tune', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية reports.custom'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'reports.builder')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('reports.builder', N'منشئ التقارير', N'Reports', N'منشئ', 3, 'build', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية reports.builder'
END

-- ===== 5. صلاحيات المحادثات المتقدمة =====
PRINT '📍 إضافة صلاحيات المحادثات المتقدمة...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.create_group')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.create_group', N'إنشاء مجموعات محادثة', N'Chat', N'إنشاء', 2, 'group_add', '#00BCD4', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.create_group'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.edit_group')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.edit_group', N'تعديل مجموعات المحادثة', N'Chat', N'تعديل', 2, 'edit', '#00BCD4', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.edit_group'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.delete_group')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.delete_group', N'حذف مجموعات المحادثة', N'Chat', N'حذف', 3, 'delete', '#F44336', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.delete_group'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.add_members')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.add_members', N'إضافة أعضاء للمجموعة', N'Chat', N'إدارة', 2, 'person_add', '#00BCD4', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.add_members'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.remove_members')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.remove_members', N'إزالة أعضاء من المجموعة', N'Chat', N'إدارة', 3, 'person_remove', '#00BCD4', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.remove_members'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.mute')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.mute', N'كتم الإشعارات', N'Chat', N'إعدادات', 1, 'volume_off', '#00BCD4', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.mute'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.unmute')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.unmute', N'إلغاء كتم الإشعارات', N'Chat', N'إعدادات', 1, 'volume_up', '#00BCD4', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.unmute'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.pin_messages')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.pin_messages', N'تثبيت الرسائل', N'Chat', N'إدارة', 2, 'push_pin', '#00BCD4', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.pin_messages'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.unpin_messages')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.unpin_messages', N'إلغاء تثبيت الرسائل', N'Chat', N'إدارة', 2, 'clear', '#00BCD4', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.unpin_messages'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.forward')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.forward', N'إعادة توجيه الرسائل', N'Chat', N'مشاركة', 1, 'forward', '#00BCD4', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.forward'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.reply')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.reply', N'الرد على الرسائل', N'Chat', N'تفاعل', 1, 'reply', '#00BCD4', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.reply'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.edit_messages')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.edit_messages', N'تعديل الرسائل', N'Chat', N'تعديل', 2, 'edit', '#00BCD4', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.edit_messages'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'chat.react')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('chat.react', N'التفاعل مع الرسائل', N'Chat', N'تفاعل', 1, 'thumb_up', '#00BCD4', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية chat.react'
END

-- ===== 6. صلاحيات التقويم المتقدمة =====
PRINT '📍 إضافة صلاحيات التقويم المتقدمة...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'calendar.edit_events')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('calendar.edit_events', N'تعديل الأحداث', N'Calendar', N'تعديل', 2, 'edit_calendar', '#FF5722', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية calendar.edit_events'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'calendar.delete_events')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('calendar.delete_events', N'حذف الأحداث', N'Calendar', N'حذف', 3, 'event_busy', '#F44336', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية calendar.delete_events'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'calendar.share_events')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('calendar.share_events', N'مشاركة الأحداث', N'Calendar', N'مشاركة', 2, 'share', '#FF5722', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية calendar.share_events'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'calendar.invite_users')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('calendar.invite_users', N'دعوة المستخدمين للأحداث', N'Calendar', N'دعوة', 2, 'person_add', '#FF5722', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية calendar.invite_users'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'calendar.set_reminders')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('calendar.set_reminders', N'تعيين التذكيرات', N'Calendar', N'تذكير', 1, 'alarm', '#FF5722', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية calendar.set_reminders'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'calendar.view_all')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('calendar.view_all', N'عرض جميع الأحداث', N'Calendar', N'عرض متقدم', 3, 'calendar_view_month', '#FF5722', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية calendar.view_all'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'calendar.export')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('calendar.export', N'تصدير التقويم', N'Calendar', N'تصدير', 2, 'file_download', '#FF5722', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية calendar.export'
END

-- ===== 7. صلاحيات لوحة المعلومات =====
PRINT '📍 إضافة صلاحيات لوحة المعلومات...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'dashboard.edit')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('dashboard.edit', N'تعديل لوحة المعلومات', N'Dashboard', N'تعديل', 3, 'edit', '#3F51B5', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية dashboard.edit'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'dashboard.customize')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('dashboard.customize', N'تخصيص لوحة المعلومات', N'Dashboard', N'تخصيص', 2, 'tune', '#3F51B5', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية dashboard.customize'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'dashboard.add_widgets')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('dashboard.add_widgets', N'إضافة عناصر للوحة', N'Dashboard', N'إضافة', 2, 'add_box', '#3F51B5', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية dashboard.add_widgets'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'dashboard.remove_widgets')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('dashboard.remove_widgets', N'إزالة عناصر من اللوحة', N'Dashboard', N'إزالة', 2, 'remove', '#3F51B5', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية dashboard.remove_widgets'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'dashboard.export')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('dashboard.export', N'تصدير لوحة المعلومات', N'Dashboard', N'تصدير', 2, 'file_download', '#3F51B5', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية dashboard.export'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'dashboard.share')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('dashboard.share', N'مشاركة لوحة المعلومات', N'Dashboard', N'مشاركة', 2, 'share', '#3F51B5', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية dashboard.share'
END

PRINT '✅ تم الانتهاء من إضافة الجزء الثاني من الصلاحيات المفقودة!'
PRINT '📊 تم إضافة 20 صلاحية إضافية في هذا الجزء'

GO
