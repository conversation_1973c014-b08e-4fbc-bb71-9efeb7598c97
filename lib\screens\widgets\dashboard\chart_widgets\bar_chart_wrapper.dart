import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../models/chart_enums.dart';
import '../../../../models/dashboard_models.dart';
import '../../../../models/advanced_filter_options.dart';
import '../../../../controllers/task_controller.dart';
import '../../../../controllers/user_controller.dart';
import '../../../../controllers/department_controller.dart';
import '../../../../models/task_status_enum.dart';
import '../../charts/enhanced_bar_chart.dart' as enhanced;


/// مغلف مخطط شريطي
///
/// يعرض مخططًا شريطيًا لأداء المستخدمين أو الأقسام
///
/// ملاحظة: يوصى باستخدام EnhancedBarChart من مجلد widgets/charts بدلاً من هذا المكون
/// لتوحيد واجهة المستخدم وتقليل التكرار في الكود.
/// راجع ملف DASHBOARD_REFACTORING.md للمزيد من المعلومات.
class BarChartWrapper extends StatefulWidget {
  /// عنصر لوحة المعلومات
  final DashboardWidget widget;

  /// إعدادات المخطط
  final Map<String, dynamic> settings;

  /// دالة يتم استدعاؤها عند تحديث الإعدادات
  final Function(DashboardWidget, Map<String, dynamic>)? onSettingsUpdated;

  /// ما إذا كان عرض تفاصيل
  final bool isDetailView;

  const BarChartWrapper({
    super.key,
    required this.widget,
    required this.settings,
    this.onSettingsUpdated,
    this.isDetailView = false,
  });

  @override
  State<BarChartWrapper> createState() => _BarChartWrapperState();
}

class _BarChartWrapperState extends State<BarChartWrapper> {
  // تحكم المهام
  final TaskController _taskController = Get.find<TaskController>();

  // تحكم المستخدمين
  final UserController _userController = Get.find<UserController>();

  // تحكم الأقسام
  final DepartmentController _departmentController =
      Get.find<DepartmentController>();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (widget.widget.type == 'userPerformanceChart') {
        return _buildUserPerformanceChart();
      } else {
        return _buildDepartmentPerformanceChart();
      }
    });
  }

  /// بناء مخطط أداء المستخدمين
  Widget _buildUserPerformanceChart() {
    final tasks = _taskController.allTasks;
    final users = _userController.users;

    if (tasks.isEmpty || users.isEmpty) {
      return Center(
        child: Text(
          'لا توجد بيانات كافية',
          style: TextStyle(
            color: Colors.grey,
          ),
        ),
      );
    }

    // تجميع المهام حسب المستخدم
    final Map<int, int> completedTasksByUser = {};
    final Map<int, int> totalTasksByUser = {};

    for (final task in tasks) {
      final assigneeId = task.assigneeId;
      if (assigneeId != null) {
        totalTasksByUser[assigneeId] = (totalTasksByUser[assigneeId] ?? 0) + 1;
        if (task.status == TaskStatus.completed.stringValue) {
          completedTasksByUser[assigneeId] =
              (completedTasksByUser[assigneeId] ?? 0) + 1;
        }
      }
    }

    // ترتيب المستخدمين حسب عدد المهام المكتملة
    final sortedUserIds = completedTasksByUser.keys.toList()
      ..sort((a, b) =>
          completedTasksByUser[b]!.compareTo(completedTasksByUser[a]!));

    // تحديد عدد المستخدمين المراد عرضهم
    final maxUsers = widget.settings['maxUsers'] ?? 5;
    final userIds = sortedUserIds.take(maxUsers).toList();

    if (userIds.isEmpty) {
      return Center(
        child: Text(
          'لا توجد مهام مكتملة',
          style: TextStyle(
            color: Colors.grey,
          ),
        ),
      );
    }

    // تحويل البيانات إلى التنسيق المطلوب للمخطط المحسن
    final Map<String, double> chartData = {};
    final Map<String, Color> barColors = {};

    for (final userId in userIds) {
      final user = users.firstWhereOrNull((u) => u.id == userId);
      if (user == null) continue;

      final completed = completedTasksByUser[userId] ?? 0;
      chartData[user.name] = completed.toDouble();
      barColors[user.name] = Colors.green;
    }

    // استخدام EnhancedBarChart بدلاً من التنفيذ المباشر مع تصميم Monday.com
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(25),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان المخطط بأسلوب Monday.com
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'أداء المستخدمين',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
                Row(
                  children: [
                    // زر تصفية
                    IconButton(
                      icon: const Icon(Icons.filter_list, size: 18),
                      onPressed: () {
                        // عرض خيارات التصفية
                      },
                      tooltip: 'تصفية',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      splashRadius: 20,
                    ),
                    const SizedBox(width: 8),
                    // زر تصدير
                    IconButton(
                      icon: const Icon(Icons.more_vert, size: 18),
                      onPressed: () {
                        // عرض خيارات التصدير
                      },
                      tooltip: 'المزيد من الخيارات',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      splashRadius: 20,
                    ),
                  ],
                ),
              ],
            ),
          ),

          // المخطط نفسه
          Expanded(
            child: LayoutBuilder(
              builder: (context, constraints) {
                // التحقق من أن المخطط له أبعاد محددة قبل رسمه
                if (constraints.maxWidth == 0 || constraints.maxHeight == 0) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                return enhanced.EnhancedBarChart(
                  data: chartData,
                  barColors: barColors,
                  title: '', // إزالة العنوان لأننا أضفناه بالفعل
                  xAxisTitle: 'المستخدم',
                  yAxisTitle: 'عدد المهام المكتملة',
                  showGrid: widget.settings['showGrid'] == true,
                  showValues: widget.settings['showValues'] == true,
                  maxY: _getMaxYFromIntMap(completedTasksByUser, userIds),
                  showFilterOptions: false,
                  showExportOptions: false,
                  chartType: ChartType.bar,
                  advancedFilterOptions: const AdvancedFilterOptions(),
                );
              },
            ),
          ),

          // وسيلة الإيضاح المحسنة بأسلوب Monday.com
          if (widget.settings['showValues'] == true) ...[
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: _buildMondayStyleUserLegend(
                  userIds, completedTasksByUser, totalTasksByUser),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء مخطط أداء الأقسام
  Widget _buildDepartmentPerformanceChart() {
    final tasks = _taskController.allTasks;
    final departments = _departmentController.allDepartments;

    if (tasks.isEmpty || departments.isEmpty) {
      return Center(
        child: Text(
          'لا توجد بيانات كافية',
          style: TextStyle(
            color: Colors.grey,
          ),
        ),
      );
    }

    // تجميع المهام حسب القسم
    final Map<int, int> completedTasksByDepartment = {};
    final Map<int, int> totalTasksByDepartment = {};

    for (final task in tasks) {
      final departmentId = task.departmentId;
      if (departmentId != null) {
        totalTasksByDepartment[departmentId] =
            (totalTasksByDepartment[departmentId] ?? 0) + 1;
        if (task.status == TaskStatus.completed.stringValue) {
          completedTasksByDepartment[departmentId] =
              (completedTasksByDepartment[departmentId] ?? 0) + 1;
        }
      }
    }

    // ترتيب الأقسام حسب عدد المهام المكتملة
    final sortedDepartmentIds = completedTasksByDepartment.keys.toList()
      ..sort((a, b) => completedTasksByDepartment[b]!
          .compareTo(completedTasksByDepartment[a]!));

    if (sortedDepartmentIds.isEmpty) {
      return Center(
        child: Text(
          'لا توجد مهام مكتملة',
          style: TextStyle(
            color: Colors.grey,
          ),
        ),
      );
    }

    // تحويل البيانات إلى التنسيق المطلوب للمخطط المحسن
    final Map<String, double> chartData = {};
    final Map<String, Color> barColors = {};

    for (final departmentId in sortedDepartmentIds) {
      final department =
          departments.firstWhereOrNull((d) => d.id == departmentId);
      if (department == null) continue;

      final completed = completedTasksByDepartment[departmentId] ?? 0;
      chartData[department.name] = completed.toDouble();
      barColors[department.name] = Colors.green;
    }

    // استخدام EnhancedBarChart بدلاً من التنفيذ المباشر مع تصميم Monday.com
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(25),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان المخطط بأسلوب Monday.com
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'أداء الأقسام',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
                Row(
                  children: [
                    // زر تصفية
                    IconButton(
                      icon: const Icon(Icons.filter_list, size: 18),
                      onPressed: () {
                        // عرض خيارات التصفية
                      },
                      tooltip: 'تصفية',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      splashRadius: 20,
                    ),
                    const SizedBox(width: 8),
                    // زر تصدير
                    IconButton(
                      icon: const Icon(Icons.more_vert, size: 18),
                      onPressed: () {
                        // عرض خيارات التصدير
                      },
                      tooltip: 'المزيد من الخيارات',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      splashRadius: 20,
                    ),
                  ],
                ),
              ],
            ),
          ),

          // المخطط نفسه
          Expanded(
            child: LayoutBuilder(
              builder: (context, constraints) {
                // التحقق من أن المخطط له أبعاد محددة قبل رسمه
                if (constraints.maxWidth == 0 || constraints.maxHeight == 0) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                return enhanced.EnhancedBarChart(
                  data: chartData,
                  barColors: barColors,
                  title: '', // إزالة العنوان لأننا أضفناه بالفعل
                  xAxisTitle: 'القسم',
                  yAxisTitle: 'عدد المهام المكتملة',
                  showGrid: widget.settings['showGrid'] == true,
                  showValues: widget.settings['showValues'] == true,
                  maxY:
                      _getMaxYFromIntMap(completedTasksByDepartment, sortedDepartmentIds),
                  showFilterOptions: false,
                  showExportOptions: false,
                  chartType: ChartType.bar,
                  advancedFilterOptions: const AdvancedFilterOptions(),
                );
              },
            ),
          ),

          // وسيلة الإيضاح المحسنة بأسلوب Monday.com
          if (widget.settings['showValues'] == true) ...[
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: _buildMondayStyleDepartmentLegend(sortedDepartmentIds,
                  completedTasksByDepartment, totalTasksByDepartment),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء وسيلة إيضاح المستخدمين بأسلوب Monday.com
  Widget _buildMondayStyleUserLegend(
    List<int> userIds,
    Map<int, int> completedTasksByUser,
    Map<int, int> totalTasksByUser,
  ) {
    final users = _userController.users;
    final List<Widget> legendItems = [];

    for (final userId in userIds) {
      final user = users.firstWhereOrNull((u) => u.id == userId);
      if (user == null) continue;

      final completed = completedTasksByUser[userId] ?? 0;
      final total = totalTasksByUser[userId] ?? 0;
      final completionRate =
          total > 0 ? (completed / total * 100).toStringAsFixed(1) : '0';

      legendItems.add(
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              // مربع اللون
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),

              // اسم المستخدم
              Expanded(
                child: Text(
                  user.name,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              // العدد والنسبة المئوية
              Text(
                '$completed/$total ($completionRate%)',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // عنوان وسيلة الإيضاح
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Text(
            'أداء المستخدمين',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
        ),

        // عناصر وسيلة الإيضاح
        ...legendItems,
      ],
    );
  }

  /// بناء وسيلة إيضاح الأقسام بأسلوب Monday.com
  Widget _buildMondayStyleDepartmentLegend(
    List<int> departmentIds,
    Map<int, int> completedTasksByDepartment,
    Map<int, int> totalTasksByDepartment,
  ) {
    final departments = _departmentController.allDepartments;
    final List<Widget> legendItems = [];

    for (final departmentId in departmentIds) {
      final department =
          departments.firstWhereOrNull((d) => d.id == departmentId);
      if (department == null) continue;

      final completed = completedTasksByDepartment[departmentId] ?? 0;
      final total = totalTasksByDepartment[departmentId] ?? 0;
      final completionRate =
          total > 0 ? (completed / total * 100).toStringAsFixed(1) : '0';

      legendItems.add(
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              // مربع اللون
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),

              // اسم القسم
              Expanded(
                child: Text(
                  department.name,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              // العدد والنسبة المئوية
              Text(
                '$completed/$total ($completionRate%)',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // عنوان وسيلة الإيضاح
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Text(
            'أداء الأقسام',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
        ),

        // عناصر وسيلة الإيضاح
        ...legendItems,
      ],
    );
  }

  /// الحصول على القيمة القصوى للمحور Y
  double _getMaxYFromIntMap(Map<int, int> data, List<int> ids) {
    double maxY = 0;
    for (final id in ids) {
      final value = data[id]?.toDouble() ?? 0;
      if (value > maxY) {
        maxY = value;
      }
    }
    return maxY * 1.2; // إضافة هامش
  }
}
