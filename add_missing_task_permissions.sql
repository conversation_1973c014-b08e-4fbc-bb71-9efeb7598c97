-- ===================================================================
-- إضافة الصلاحيات المفقودة الجديدة لتغيير حالة وأولوية المهام
-- تاريخ الإنشاء: 2025-01-07
-- الوصف: إضافة الصلاحيات الجديدة التي تم تطويرها للسحب والإفلات
-- ===================================================================

USE [databasetasks]
GO

PRINT '🚀 بدء إضافة الصلاحيات الجديدة للمهام...'

-- ===================================================================
-- الصلاحيات الجديدة للمهام
-- ===================================================================

PRINT '📋 إضافة صلاحيات تغيير حالة وأولوية المهام...'

-- تغيير حالة المهام (للسحب والإفلات)
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.change_status')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.change_status', N'تغيير حالة المهام عبر السحب والإفلات', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: tasks.change_status'
END
ELSE
BEGIN
    PRINT '⚠️ الصلاحية tasks.change_status موجودة مسبقاً'
END

-- تغيير أولوية المهام (للأزرار والسحب والإفلات)
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.change_priority')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.change_priority', N'تغيير أولوية المهام عبر الأزرار والسحب والإفلات', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: tasks.change_priority'
END
ELSE
BEGIN
    PRINT '⚠️ الصلاحية tasks.change_priority موجودة مسبقاً'
END

-- ===================================================================
-- منح الصلاحيات الجديدة للأدوار الموجودة
-- ===================================================================

PRINT '🔐 منح الصلاحيات الجديدة للأدوار...'

-- الحصول على معرفات الصلاحيات الجديدة
DECLARE @changeStatusPermissionId INT
DECLARE @changePriorityPermissionId INT

SELECT @changeStatusPermissionId = id FROM [dbo].[permissions] WHERE [name] = 'tasks.change_status'
SELECT @changePriorityPermissionId = id FROM [dbo].[permissions] WHERE [name] = 'tasks.change_priority'

-- منح الصلاحيات لدور المدير (admin)
IF EXISTS (SELECT 1 FROM [dbo].[roles] WHERE [name] = 'admin')
BEGIN
    DECLARE @adminRoleId INT
    SELECT @adminRoleId = id FROM [dbo].[roles] WHERE [name] = 'admin'
    
    -- منح صلاحية تغيير الحالة
    IF NOT EXISTS (SELECT 1 FROM [dbo].[role_permissions] WHERE [role_id] = @adminRoleId AND [permission_id] = @changeStatusPermissionId)
    BEGIN
        INSERT INTO [dbo].[role_permissions] ([role_id], [permission_id], [granted_at])
        VALUES (@adminRoleId, @changeStatusPermissionId, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
        PRINT '✅ تم منح صلاحية تغيير الحالة لدور المدير'
    END
    
    -- منح صلاحية تغيير الأولوية
    IF NOT EXISTS (SELECT 1 FROM [dbo].[role_permissions] WHERE [role_id] = @adminRoleId AND [permission_id] = @changePriorityPermissionId)
    BEGIN
        INSERT INTO [dbo].[role_permissions] ([role_id], [permission_id], [granted_at])
        VALUES (@adminRoleId, @changePriorityPermissionId, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
        PRINT '✅ تم منح صلاحية تغيير الأولوية لدور المدير'
    END
END

-- منح الصلاحيات لدور المدير العام (super_admin)
IF EXISTS (SELECT 1 FROM [dbo].[roles] WHERE [name] = 'super_admin')
BEGIN
    DECLARE @superAdminRoleId INT
    SELECT @superAdminRoleId = id FROM [dbo].[roles] WHERE [name] = 'super_admin'
    
    -- منح صلاحية تغيير الحالة
    IF NOT EXISTS (SELECT 1 FROM [dbo].[role_permissions] WHERE [role_id] = @superAdminRoleId AND [permission_id] = @changeStatusPermissionId)
    BEGIN
        INSERT INTO [dbo].[role_permissions] ([role_id], [permission_id], [granted_at])
        VALUES (@superAdminRoleId, @changeStatusPermissionId, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
        PRINT '✅ تم منح صلاحية تغيير الحالة لدور المدير العام'
    END
    
    -- منح صلاحية تغيير الأولوية
    IF NOT EXISTS (SELECT 1 FROM [dbo].[role_permissions] WHERE [role_id] = @superAdminRoleId AND [permission_id] = @changePriorityPermissionId)
    BEGIN
        INSERT INTO [dbo].[role_permissions] ([role_id], [permission_id], [granted_at])
        VALUES (@superAdminRoleId, @changePriorityPermissionId, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
        PRINT '✅ تم منح صلاحية تغيير الأولوية لدور المدير العام'
    END
END

-- منح الصلاحيات لدور المشرف (manager)
IF EXISTS (SELECT 1 FROM [dbo].[roles] WHERE [name] = 'manager')
BEGIN
    DECLARE @managerRoleId INT
    SELECT @managerRoleId = id FROM [dbo].[roles] WHERE [name] = 'manager'
    
    -- منح صلاحية تغيير الحالة
    IF NOT EXISTS (SELECT 1 FROM [dbo].[role_permissions] WHERE [role_id] = @managerRoleId AND [permission_id] = @changeStatusPermissionId)
    BEGIN
        INSERT INTO [dbo].[role_permissions] ([role_id], [permission_id], [granted_at])
        VALUES (@managerRoleId, @changeStatusPermissionId, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
        PRINT '✅ تم منح صلاحية تغيير الحالة لدور المشرف'
    END
    
    -- منح صلاحية تغيير الأولوية
    IF NOT EXISTS (SELECT 1 FROM [dbo].[role_permissions] WHERE [role_id] = @managerRoleId AND [permission_id] = @changePriorityPermissionId)
    BEGIN
        INSERT INTO [dbo].[role_permissions] ([role_id], [permission_id], [granted_at])
        VALUES (@managerRoleId, @changePriorityPermissionId, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
        PRINT '✅ تم منح صلاحية تغيير الأولوية لدور المشرف'
    END
END

-- ===================================================================
-- التحقق من النتائج
-- ===================================================================

PRINT '🔍 التحقق من الصلاحيات المضافة...'

-- عرض الصلاحيات الجديدة
SELECT 
    [name] as 'اسم الصلاحية',
    [description] as 'الوصف',
    [permission_group] as 'المجموعة',
    [is_active] as 'نشطة'
FROM [dbo].[permissions] 
WHERE [name] IN ('tasks.change_status', 'tasks.change_priority')
ORDER BY [name]

-- عرض الأدوار التي تم منحها الصلاحيات الجديدة
SELECT 
    r.[name] as 'اسم الدور',
    p.[name] as 'اسم الصلاحية',
    p.[description] as 'وصف الصلاحية'
FROM [dbo].[role_permissions] rp
INNER JOIN [dbo].[roles] r ON rp.[role_id] = r.[id]
INNER JOIN [dbo].[permissions] p ON rp.[permission_id] = p.[id]
WHERE p.[name] IN ('tasks.change_status', 'tasks.change_priority')
ORDER BY r.[name], p.[name]

PRINT '✅ تم الانتهاء من إضافة الصلاحيات الجديدة بنجاح!'
PRINT '📊 عدد الصلاحيات المضافة: 2 صلاحية'
PRINT '🎯 الصلاحيات الجديدة:'
PRINT '   - tasks.change_status: تغيير حالة المهام عبر السحب والإفلات'
PRINT '   - tasks.change_priority: تغيير أولوية المهام عبر الأزرار والسحب والإفلات'

PRINT '🎉 تم الانتهاء بنجاح من إضافة جميع الصلاحيات المطلوبة!'
