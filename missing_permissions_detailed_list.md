# 📋 قائمة مفصلة بالصلاحيات المفقودة

## 🔥 الصلاحيات الأساسية المفقودة (أولوية عالية)

### 1. صلاحيات عرض تفاصيل المهام
```
Permission: tasks.view_details
Description: عرض تفاصيل المهام والانتقال إليها
Group: tasks
Priority: HIGH
Affected Files: 7+ ملفات
```

**الأماكن المتأثرة:**
- `tasks_tab.dart` - النقر على المهمة
- `user_dashboard_screen.dart` - قائمة المهام
- `department_detail_screen.dart` - مهام القسم
- `unified_search_screen.dart` - نتائج البحث
- `task_list_widget.dart` - ويدجت قائمة المهام
- `notifications_tab.dart` - الإشعارات المرتبطة بالمهام

### 2. صلاحيات تحديث تقدم المهام
```
Permission: tasks.update_progress
Description: تحديث نسبة إنجاز المهام
Group: tasks
Priority: HIGH
Affected Files: 2+ ملفات
```

### 3. صلاحيات إدارة المرفقات
```
Permission: attachments.upload
Description: رفع المرفقات للمهام
Group: attachments
Priority: HIGH

Permission: attachments.delete
Description: حذف المرفقات من المهام
Group: attachments
Priority: HIGH

Permission: attachments.view
Description: عرض وتحميل المرفقات
Group: attachments
Priority: MEDIUM
```

## 🔧 صلاحيات إدارة الرسائل والمحادثات

### 4. صلاحيات الرسائل
```
Permission: messages.pin
Description: تثبيت وإلغاء تثبيت الرسائل
Group: messages
Priority: MEDIUM

Permission: messages.edit
Description: تعديل الرسائل المرسلة
Group: messages
Priority: MEDIUM

Permission: messages.delete
Description: حذف الرسائل
Group: messages
Priority: MEDIUM

Permission: messages.reply
Description: الرد على الرسائل
Group: messages
Priority: LOW

Permission: messages.mark_followup
Description: تحديد الرسائل للمتابعة
Group: messages
Priority: LOW
```

## 🏢 صلاحيات إدارة الأقسام المتقدمة

### 5. صلاحيات الأقسام
```
Permission: departments.assign_manager
Description: تعيين مدير للقسم
Group: departments
Priority: HIGH

Permission: departments.add_users
Description: إضافة مستخدمين للقسم
Group: departments
Priority: HIGH

Permission: departments.remove_users
Description: إزالة مستخدمين من القسم
Group: departments
Priority: HIGH

Permission: departments.manage_users
Description: إدارة شاملة لمستخدمي القسم
Group: departments
Priority: MEDIUM
```

## 📊 صلاحيات التقارير المتخصصة

### 6. صلاحيات التقارير
```
Permission: reports.contributions
Description: عرض تقارير المساهمات
Group: reports
Priority: MEDIUM

Permission: reports.pdf
Description: تصدير التقارير كـ PDF
Group: reports
Priority: MEDIUM

Permission: reports.workload
Description: تقارير عبء العمل
Group: reports
Priority: LOW
```

## 👤 صلاحيات الملف الشخصي والإعدادات

### 7. صلاحيات الملف الشخصي
```
Permission: profile.change_password
Description: تغيير كلمة المرور
Group: profile
Priority: HIGH

Permission: profile.edit
Description: تعديل بيانات الملف الشخصي
Group: profile
Priority: HIGH

Permission: profile.view
Description: عرض الملف الشخصي
Group: profile
Priority: MEDIUM
```

### 8. صلاحيات الإعدادات
```
Permission: settings.language
Description: تغيير إعدادات اللغة
Group: settings
Priority: LOW

Permission: settings.theme
Description: تغيير السمة والمظهر
Group: settings
Priority: LOW

Permission: settings.notifications
Description: إعدادات الإشعارات
Group: settings
Priority: MEDIUM
```

## 🗄️ صلاحيات قاعدة البيانات والنظام

### 9. صلاحيات قاعدة البيانات
```
Permission: database.repair
Description: إصلاح قاعدة البيانات
Group: database
Priority: HIGH

Permission: database.backup
Description: إنشاء نسخ احتياطية
Group: database
Priority: HIGH

Permission: database.restore
Description: استعادة النسخ الاحتياطية
Group: database
Priority: HIGH
```

## 🎮 صلاحيات لوحة المهام والتفاعل

### 10. صلاحيات لوحة المهام
```
Permission: tasks.manage_board
Description: إدارة لوحة المهام وإضافة أعمدة
Group: tasks
Priority: MEDIUM

Permission: tasks.filter
Description: تصفية المهام
Group: tasks
Priority: LOW

Permission: tasks.sort
Description: ترتيب المهام
Group: tasks
Priority: LOW
```

## 🧪 صلاحيات الاختبار والتشخيص

### 11. صلاحيات الاختبار
```
Permission: admin.test_permissions
Description: اختبار الصلاحيات
Group: admin
Priority: LOW

Permission: admin.debug
Description: أدوات التشخيص والتطوير
Group: admin
Priority: LOW
```

## 📊 ملخص الأولويات

### أولوية عالية (HIGH): 9 صلاحيات
- `tasks.view_details`
- `tasks.update_progress`
- `attachments.upload`
- `attachments.delete`
- `departments.assign_manager`
- `departments.add_users`
- `departments.remove_users`
- `profile.change_password`
- `profile.edit`
- `database.repair`
- `database.backup`
- `database.restore`

### أولوية متوسطة (MEDIUM): 8 صلاحيات
- `attachments.view`
- `messages.pin`
- `messages.edit`
- `messages.delete`
- `departments.manage_users`
- `reports.contributions`
- `reports.pdf`
- `profile.view`
- `settings.notifications`
- `tasks.manage_board`

### أولوية منخفضة (LOW): 8 صلاحيات
- `messages.reply`
- `messages.mark_followup`
- `reports.workload`
- `settings.language`
- `settings.theme`
- `tasks.filter`
- `tasks.sort`
- `admin.test_permissions`
- `admin.debug`

## 🎯 التوصية

**ابدأ بتطبيق الصلاحيات ذات الأولوية العالية أولاً**، خاصة `tasks.view_details` لأنها تؤثر على أكبر عدد من الأماكن في المشروع.
