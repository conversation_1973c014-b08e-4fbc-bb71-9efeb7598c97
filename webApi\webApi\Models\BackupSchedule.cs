using System.ComponentModel.DataAnnotations;

namespace webApi.Models
{
    /// <summary>
    /// نموذج جدولة النسخ الاحتياطية
    /// </summary>
    public class BackupSchedule
    {
        /// <summary>
        /// معرف الجدولة
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// اسم الجدولة
        /// </summary>
        [Required]
        [MaxLength(255)]
        public string Name { get; set; } = null!;

        /// <summary>
        /// وصف الجدولة
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// نوع الجدولة (daily, weekly, monthly)
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string ScheduleType { get; set; } = null!;

        /// <summary>
        /// تعبير Cron للجدولة
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string CronExpression { get; set; } = null!;

        /// <summary>
        /// هل الجدولة نشطة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// آخر تشغيل للجدولة
        /// </summary>
        public long? LastRunAt { get; set; }

        /// <summary>
        /// التشغيل التالي المتوقع
        /// </summary>
        public long? NextRunAt { get; set; }

        /// <summary>
        /// عدد النسخ الاحتياطية المحتفظ بها
        /// </summary>
        public int RetentionCount { get; set; } = 7;

        /// <summary>
        /// معرف المستخدم الذي أنشأ الجدولة
        /// </summary>
        public int CreatedBy { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public long CreatedAt { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public long? UpdatedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي قام بآخر تحديث
        /// </summary>
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// عدد مرات التشغيل الناجحة
        /// </summary>
        public int SuccessfulRuns { get; set; } = 0;

        /// <summary>
        /// عدد مرات الفشل
        /// </summary>
        public int FailedRuns { get; set; } = 0;

        /// <summary>
        /// آخر رسالة خطأ
        /// </summary>
        public string? LastErrorMessage { get; set; }

        // Navigation properties
        public virtual User CreatedByNavigation { get; set; } = null!;
        public virtual User? UpdatedByNavigation { get; set; }
    }

    /// <summary>
    /// نموذج طلب إنشاء جدولة نسخ احتياطية
    /// </summary>
    public class CreateBackupScheduleRequest
    {
        /// <summary>
        /// اسم الجدولة
        /// </summary>
        [Required]
        public string Name { get; set; } = null!;

        /// <summary>
        /// وصف الجدولة
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// نوع الجدولة
        /// </summary>
        [Required]
        public string ScheduleType { get; set; } = null!;

        /// <summary>
        /// تعبير Cron
        /// </summary>
        [Required]
        public string CronExpression { get; set; } = null!;

        /// <summary>
        /// عدد النسخ المحتفظ بها
        /// </summary>
        public int RetentionCount { get; set; } = 7;

        /// <summary>
        /// معرف المستخدم المنشئ
        /// </summary>
        [Required]
        public int CreatedBy { get; set; }
    }

    /// <summary>
    /// نموذج تحديث جدولة النسخ الاحتياطية
    /// </summary>
    public class UpdateBackupScheduleRequest
    {
        /// <summary>
        /// اسم الجدولة
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// وصف الجدولة
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// تعبير Cron
        /// </summary>
        public string? CronExpression { get; set; }

        /// <summary>
        /// هل الجدولة نشطة
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// عدد النسخ المحتفظ بها
        /// </summary>
        public int? RetentionCount { get; set; }

        /// <summary>
        /// معرف المستخدم المحدث
        /// </summary>
        [Required]
        public int UpdatedBy { get; set; }
    }
}
