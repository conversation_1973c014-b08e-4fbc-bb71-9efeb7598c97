# 🔍 تحليل شامل للأماكن المفقودة للصلاحيات

## 📊 ملخص التحليل

بعد فحص شامل لجميع ملفات المشروع، تم العثور على **عدة أماكن** تحتاج لتطبيق الصلاحيات أو تحتاج لصلاحيات غير موجودة في النظام.

## ❌ الأماكن التي تحتاج لتطبيق الصلاحيات

### 1. 📋 شاشات المهام

#### `lib/screens/tasks/create_task_screen.dart`
**الأزرار غير المحمية:**
- ✅ زر "إنشاء المهمة" (السطر 1096) - **محمي بالفعل عبر التنقل**
- ❌ زر "اختيار المستخدمين" (السطر 827) - يحتاج `users.view`
- ❌ زر "إضافة مرفقات" (السطر 898) - يحتاج `attachments.upload`
- ❌ زر "حذف مرفق" (السطر 952) - يحتاج `attachments.delete`

#### `lib/screens/tasks/task_detail_screen.dart`
**الأزرار غير المحمية:**
- ❌ زر "تعديل المهمة" (السطر 335) - يحتاج `tasks.edit`
- ❌ زر "تقرير PDF" (السطر 348) - يحتاج `reports.export`
- ❌ زر "تحديث التقدم" (السطر 772) - يحتاج `tasks.update_progress`
- ❌ زر "إرسال رسالة" (السطر 2491) - يحتاج `chat.send`
- ❌ زر "حذف المهمة" (السطر 2559) - يحتاج `tasks.delete`

#### `lib/screens/tasks/task_board_screen.dart`
**الأزرار غير المحمية:**
- ❌ زر "عرض القائمة" (السطر 79) - يحتاج `tasks.view`
- ❌ زر "تصفية المهام" (السطر 95) - يحتاج `tasks.filter`
- ❌ زر "ترتيب المهام" (السطر 121) - يحتاج `tasks.sort`
- ❌ زر "إنشاء مهمة" العائم (السطر 158) - يحتاج `tasks.create`
- ❌ زر "إضافة عمود" (السطر 356) - يحتاج `tasks.manage_board`

#### `lib/screens/tasks/task_progress_tab.dart`
**الأزرار غير المحمية:**
- ❌ زر "تقارير المساهمات" (السطر 1265) - يحتاج `reports.contributions`
- ❌ زر "إرفاق ملف" (السطر 2426) - يحتاج `attachments.upload`

### 2. 🏢 شاشات الأقسام

#### `lib/screens/departments/department_detail_screen.dart`
**الأزرار غير المحمية:**
- ❌ زر "تحديث البيانات" (السطر 858) - يحتاج `departments.view`
- ❌ زر "إدارة المستخدمين" (السطر 867) - يحتاج `departments.manage_users`
- ❌ زر "تعديل القسم" (السطر 878) - يحتاج `departments.edit`
- ❌ زر "إضافة عضو" (السطر 1292) - يحتاج `departments.add_users`
- ❌ زر "تعيين كمدير" (السطر 1328) - يحتاج `departments.assign_manager`
- ❌ زر "إزالة من القسم" (السطر 1333) - يحتاج `departments.remove_users`
- ❌ زر "فلترة المهام" (السطر 1458) - يحتاج `tasks.filter`
- ❌ زر "نقل المهمة" (السطر 1669) - يحتاج `tasks.transfer`

### 3. 🏠 الشاشة الرئيسية

#### `lib/screens/home/<USER>
**الأزرار غير المحمية:**
- ❌ زر "إضافة عنصر جديد" العائم (السطر 4388) - يحتاج `dashboard.customize`

#### `lib/screens/home/<USER>
**الأزرار غير المحمية:**
- ❌ جميع أزرار الإعدادات - تحتاج صلاحيات متنوعة

### 4. ⚙️ شاشات الإعدادات

#### `lib/screens/settings/database_repair_screen.dart`
**الأزرار غير المحمية:**
- ❌ زر "إصلاح قاعدة البيانات" (السطر 138) - يحتاج `database.repair`

#### شاشات الإعدادات الأخرى:
- ❌ `change_password_screen.dart` - يحتاج `profile.change_password`
- ❌ `edit_profile_screen.dart` - يحتاج `profile.edit`
- ❌ `language_settings_screen.dart` - يحتاج `settings.language`
- ❌ `theme_settings_screen.dart` - يحتاج `settings.theme`

### 5. 🧪 شاشات الاختبار

#### `lib/screens/test/permissions_test_screen.dart`
**الأزرار غير المحمية:**
- ❌ زر "اختبار صلاحية" العائم (السطر 57) - يحتاج `admin.test_permissions`

## 🆕 الصلاحيات المفقودة في النظام

### 🔍 الصلاحية الأهم المفقودة: عرض تفاصيل المهام
**المشكلة الرئيسية:** لا توجد صلاحية محددة للتحكم في **عرض تفاصيل المهام**

**الأماكن المتأثرة:**
- `lib/screens/home/<USER>
- `lib/screens/home/<USER>"فتح في نافذة جديدة" (السطر 791)
- `lib/screens/user/user_dashboard_screen.dart` - النقر على المهمة (السطر 476, 621)
- `lib/screens/departments/department_detail_screen.dart` - النقر على المهمة (السطر 1566)
- `lib/screens/search/unified_search_screen.dart` - النقر على نتيجة البحث (السطر 376)
- `lib/screens/widgets/dashboard/chart_widgets/task_list_widget.dart` - النقر على المهمة (السطر 158)
- `lib/screens/home/<USER>

**الصلاحية المطلوبة:**
- `tasks.view_details` - عرض تفاصيل المهام

### صلاحيات المرفقات:
- `attachments.upload` - رفع المرفقات
- `attachments.delete` - حذف المرفقات
- `attachments.download` - تحميل المرفقات
- `attachments.view` - عرض المرفقات

### صلاحيات التقدم:
- `tasks.update_progress` - تحديث تقدم المهام
- `tasks.view_progress` - عرض تقدم المهام

### صلاحيات لوحة المهام:
- `tasks.manage_board` - إدارة لوحة المهام
- `tasks.filter` - تصفية المهام
- `tasks.sort` - ترتيب المهام

### صلاحيات الأقسام المتقدمة:
- `departments.assign_manager` - تعيين مدير القسم
- `departments.remove_users` - إزالة المستخدمين من القسم
- `departments.add_users` - إضافة المستخدمين للقسم
- `departments.manage_users` - إدارة مستخدمي القسم

### صلاحيات التقارير المتخصصة:
- `reports.contributions` - تقارير المساهمات
- `reports.export` - تصدير التقارير
- `reports.pdf` - تقارير PDF

### صلاحيات الملف الشخصي:
- `profile.change_password` - تغيير كلمة المرور
- `profile.edit` - تعديل الملف الشخصي
- `profile.view` - عرض الملف الشخصي

### صلاحيات الإعدادات:
- `settings.language` - إعدادات اللغة
- `settings.theme` - إعدادات السمة
- `settings.notifications` - إعدادات الإشعارات

### صلاحيات قاعدة البيانات:
- `database.repair` - إصلاح قاعدة البيانات
- `database.backup` - نسخ احتياطي
- `database.restore` - استعادة النسخ الاحتياطية

### صلاحيات الاختبار:
- `admin.test_permissions` - اختبار الصلاحيات
- `admin.debug` - أدوات التشخيص

### صلاحيات الرسائل والمحادثات:
- `messages.pin` - تثبيت الرسائل
- `messages.edit` - تعديل الرسائل
- `messages.delete` - حذف الرسائل
- `messages.reply` - الرد على الرسائل
- `messages.mark_followup` - تحديد للمتابعة

## 🎯 أمثلة محددة للأماكن التي تحتاج صلاحيات

### 1. النقر على المهام لعرض التفاصيل
```dart
// في lib/screens/home/<USER>
InkWell(
  onTap: () {
    // يحتاج: if (_permissionService.canViewTaskDetails())
    setState(() {
      _selectedTaskId = task.id;
      _showSidePanel = true;
    });
    taskController.loadTaskDetails(task.id.toString());
  },
  child: taskContent,
)
```

### 2. زر فتح المهمة في نافذة جديدة
```dart
// في lib/screens/home/<USER>
IconButton(
  icon: const Icon(Icons.open_in_new),
  tooltip: 'فتح في نافذة جديدة',
  onPressed: () {
    // يحتاج: if (_permissionService.canViewTaskDetails())
    Get.toNamed(AppRoutes.taskDetail, arguments: {'taskId': _selectedTaskId!});
  },
),
```

### 3. أزرار إدارة الرسائل في تفاصيل المهمة
```dart
// في lib/screens/tasks/task_detail_screen.dart - السطر 2310
ListTile(
  leading: Icon(message.isPinned ? Icons.push_pin_outlined : Icons.push_pin),
  title: Text(message.isPinned ? 'إلغاء التثبيت' : 'تثبيت الرسالة'),
  onTap: () {
    // يحتاج: if (_permissionService.canPinMessages())
    messagesController.pinMessage(message.id, !message.isPinned);
  },
),
```

### 4. زر تحديث التقدم
```dart
// في lib/screens/tasks/task_detail_screen.dart - السطر 772
ElevatedButton.icon(
  icon: const Icon(Icons.update),
  label: const Text('تحديث التقدم'),
  onPressed: () {
    // يحتاج: if (_permissionService.canUpdateTaskProgress())
    _showProgressUpdateDialog();
  },
),
```

## 📈 إحصائيات التحليل

### الملفات المفحوصة: **50+ ملف**
### الأزرار المكتشفة: **100+ زر**
### الأزرار غير المحمية: **45+ زر**
### الصلاحيات المفقودة: **25+ صلاحية**
### أهم صلاحية مفقودة: **`tasks.view_details`** (تؤثر على 7+ أماكن)

## 🎯 التوصيات

### 1. إضافة الصلاحيات المفقودة
- تحديث `unified_permission_service.dart`
- إضافة الصلاحيات الجديدة لقاعدة البيانات
- تحديث جدول الصلاحيات

### 2. تطبيق الحماية على الأزرار
- استخدام نفس النمط المطبق سابقاً
- التحقق من الصلاحية قبل عرض الزر
- إخفاء الزر إذا لم تكن الصلاحية متاحة

### 3. اختبار شامل
- اختبار جميع الصلاحيات الجديدة
- التأكد من عمل جميع الأزرار
- فحص تجربة المستخدم

## 🚀 الخطوات التالية

1. **إضافة الصلاحيات المفقودة** للنظام
2. **تطبيق الحماية** على الأزرار المكتشفة
3. **اختبار شامل** للنظام
4. **توثيق** التغييرات المطبقة
