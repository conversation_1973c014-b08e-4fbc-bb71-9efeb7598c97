-- تغيير ترميز قاعدة البيانات لدعم العربية
-- الحل الأول: تغيير ترميز قاعدة البيانات

-- 1. إنشاء نسخة احتياطية أولاً
BACKUP DATABASE databasetasks 
TO DISK = 'C:\temp\databasetasks_backup_before_collation_change.bak'
WITH FORMAT, INIT;

-- 2. قطع جميع الاتصالات بقاعدة البيانات
USE master;
GO

ALTER DATABASE databasetasks SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
GO

-- 3. تغيير ترميز قاعدة البيانات إلى Arabic_CI_AS (يدعم العربية)
ALTER DATABASE databasetasks COLLATE Arabic_CI_AS;
GO

-- 4. إعادة تعيين قاعدة البيانات للوضع متعدد المستخدمين
ALTER DATABASE databasetasks SET MULTI_USER;
GO

-- 5. التحقق من التغيير
SELECT 
    name,
    collation_name
FROM sys.databases 
WHERE name = 'databasetasks';

PRINT N'تم تغيير ترميز قاعدة البيانات بنجاح!';
GO
