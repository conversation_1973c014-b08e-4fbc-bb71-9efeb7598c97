import 'package:flutter/material.dart';

class MondayStyleDashboardGrid extends StatefulWidget {
  final String dashboardId;
  final bool isEditing;

  const MondayStyleDashboardGrid({
    Key? key,
    required this.dashboardId,
    required this.isEditing,
  }) : super(key: key);

  @override
  State<MondayStyleDashboardGrid> createState() =>
      _MondayStyleDashboardGridState();
}

class _MondayStyleDashboardGridState extends State<MondayStyleDashboardGrid> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Center(
        child: Text('Monday Style Dashboard Grid'),
      ),
    );
  }
}
