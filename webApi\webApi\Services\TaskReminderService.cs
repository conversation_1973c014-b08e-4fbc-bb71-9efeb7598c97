using Microsoft.EntityFrameworkCore;
using webApi.Models;
using TaskModel = webApi.Models.Task;

namespace webApi.Services
{
    /// <summary>
    /// خدمة تذكيرات المهام - تدير إرسال التذكيرات للمهام المقتربة من الموعد النهائي
    /// </summary>
    public class TaskReminderService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TaskReminderService> _logger;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(15); // فحص كل 15 دقيقة لتحسين الاستجابة
        private DateTime _lastCheckTime = DateTime.MinValue;

        public TaskReminderService(IServiceProvider serviceProvider, ILogger<TaskReminderService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async System.Threading.Tasks.Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("بدء خدمة تذكيرات المهام");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CheckTaskDeadlines();
                    await System.Threading.Tasks.Task.Delay(_checkInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("تم إيقاف خدمة تذكيرات المهام");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "خطأ في خدمة تذكيرات المهام");
                    await System.Threading.Tasks.Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // انتظار 5 دقائق قبل المحاولة مرة أخرى
                }
            }
        }

        /// <summary>
        /// فحص المهام المقتربة من الموعد النهائي وإرسال التذكيرات
        /// </summary>
        private async System.Threading.Tasks.Task CheckTaskDeadlines()
        {
            // تجنب الفحص المتكرر خلال فترة قصيرة (تقليل إلى 10 دقائق)
            var now = DateTime.UtcNow;
            if ((now - _lastCheckTime).TotalMinutes < 10)
            {
                _logger.LogDebug("تم تخطي الفحص - تم الفحص مؤخراً");
                return;
            }
            _lastCheckTime = now;

            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<TasksDbContext>();
            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();

            try
            {
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                var fortyEightHoursFromNow = currentTime + (48 * 60 * 60); // 48 ساعة
                var twentyFourHoursFromNow = currentTime + (24 * 60 * 60); // 24 ساعة
                var sixHoursFromNow = currentTime + (6 * 60 * 60); // 6 ساعات

                // البحث عن المهام المقتربة من الموعد النهائي (محسن مع فهرسة أفضل)
                var upcomingTasks = await context.Tasks
                    .Where(t => t.DueDate.HasValue &&
                               t.Status != "مكتملة" &&
                               t.Status != "ملغية" &&
                               !t.IsDeleted &&
                               t.DueDate.Value <= fortyEightHoursFromNow &&
                               t.DueDate.Value > currentTime)
                    .OrderBy(t => t.DueDate) // ترتيب حسب الموعد النهائي
                    .Take(15) // تقليل العدد لتحسين الأداء
                    .Select(t => new { t.Id, t.Title, t.DueDate, t.CreatorId, t.AssigneeId }) // تحديد الحقول المطلوبة فقط
                    .ToListAsync();

                _logger.LogInformation("تم العثور على {Count} مهمة مقتربة من الموعد النهائي", upcomingTasks.Count);

                // معالجة المهام بدون استعلامات إضافية مفرطة
                foreach (var taskData in upcomingTasks)
                {
                    try
                    {
                        // إنشاء كائن مهمة مبسط للمعالجة
                        var task = new TaskModel
                        {
                            Id = taskData.Id,
                            Title = taskData.Title,
                            DueDate = taskData.DueDate,
                            CreatorId = taskData.CreatorId,
                            AssigneeId = taskData.AssigneeId
                        };
                        await SendTaskReminders(task, currentTime, twentyFourHoursFromNow, sixHoursFromNow, notificationService, context);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في معالجة تذكير المهمة {TaskId}", taskData.Id);
                    }
                }

                // فحص المهام المتأخرة
                await CheckOverdueTasks(context, notificationService, currentTime);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص المواعيد النهائية للمهام");
            }
        }

        /// <summary>
        /// إرسال تذكيرات للمهمة حسب الوقت المتبقي
        /// </summary>
        private async System.Threading.Tasks.Task SendTaskReminders(
            TaskModel task,
            long currentTime,
            long twentyFourHoursFromNow,
            long sixHoursFromNow,
            INotificationService notificationService,
            TasksDbContext context)
        {
            try
            {
                var timeRemaining = task.DueDate!.Value - currentTime;
                var hoursRemaining = timeRemaining / 3600;

                string reminderType;
                string title;
                string message;

                if (hoursRemaining <= 6)
                {
                    reminderType = "reminder_6_hours";
                    title = "تذكير عاجل - 6 ساعات متبقية";
                    message = $"المهمة رقم #{task.Id}: '{task.Title}' تنتهي خلال {hoursRemaining:F0} ساعة";
                }
                else if (hoursRemaining <= 24)
                {
                    reminderType = "reminder_24_hours";
                    title = "تذكير - يوم واحد متبقي";
                    message = $"المهمة رقم #{task.Id}: '{task.Title}' تنتهي خلال {hoursRemaining:F0} ساعة";
                }
                else
                {
                    reminderType = "reminder_48_hours";
                    title = "تذكير - يومان متبقيان";
                    message = $"المهمة رقم #{task.Id}: '{task.Title}' تنتهي خلال {hoursRemaining:F0} ساعة";
                }

                // التحقق من عدم إرسال نفس التذكير مسبقاً
                var existingReminder = await context.Notifications
                    .AnyAsync(n => n.RelatedId == task.Id && 
                                  n.Type == reminderType &&
                                  n.CreatedAt > (currentTime - 86400)); // خلال آخر 24 ساعة

                if (existingReminder)
                {
                    return; // تم إرسال هذا التذكير مسبقاً
                }

                // الحصول على المستخدمين المعنيين
                var userIds = await GetTaskRelatedUsers(task, context);

                if (userIds.Count > 0)
                {
                    await notificationService.CreateAndSendNotificationsAsync(
                        userIds,
                        title,
                        message,
                        reminderType,
                        task.Id
                    );

                    _logger.LogInformation("تم إرسال تذكير {ReminderType} للمهمة {TaskId} لـ {UserCount} مستخدم", 
                        reminderType, task.Id, userIds.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إرسال تذكير للمهمة {TaskId}", task.Id);
            }
        }

        /// <summary>
        /// فحص المهام المتأخرة وإرسال إشعارات
        /// </summary>
        private async System.Threading.Tasks.Task CheckOverdueTasks(TasksDbContext context, INotificationService notificationService, long currentTime)
        {
            try
            {
                var twentyFourHoursAgo = currentTime - 86400; // 24 ساعة

                // البحث عن المهام المتأخرة (محسن مع استعلام منفصل للإشعارات)
                var overdueTaskIds = await context.Tasks
                    .Where(t => t.DueDate.HasValue &&
                               t.Status != "مكتملة" &&
                               t.Status != "ملغية" &&
                               !t.IsDeleted &&
                               t.DueDate.Value < currentTime)
                    .OrderBy(t => t.DueDate) // الأقدم أولاً
                    .Take(30) // تقليل العدد لتحسين الأداء
                    .Select(t => t.Id)
                    .ToListAsync();

                // فحص الإشعارات المرسلة مسبقاً
                var recentNotificationTaskIds = await context.Notifications
                    .Where(n => overdueTaskIds.Contains(n.RelatedId.Value) &&
                               n.Type == "task_overdue" &&
                               n.CreatedAt > twentyFourHoursAgo)
                    .Select(n => n.RelatedId.Value)
                    .ToListAsync();

                // الحصول على المهام التي تحتاج إشعارات
                var tasksNeedingNotifications = overdueTaskIds.Except(recentNotificationTaskIds).ToList();

                var overdueTasks = await context.Tasks
                    .Where(t => tasksNeedingNotifications.Contains(t.Id))
                    .Select(t => new { t.Id, t.Title, t.DueDate, t.CreatorId, t.AssigneeId })
                    .ToListAsync();

                _logger.LogInformation("تم العثور على {Count} مهمة متأخرة تحتاج إشعارات", overdueTasks.Count);

                // معالجة المهام المتأخرة بدون استعلامات إضافية
                foreach (var taskData in overdueTasks)
                {
                    try
                    {
                        // إنشاء كائن مهمة مبسط
                        var task = new TaskModel
                        {
                            Id = taskData.Id,
                            Title = taskData.Title,
                            DueDate = taskData.DueDate,
                            CreatorId = taskData.CreatorId,
                            AssigneeId = taskData.AssigneeId
                        };

                        var userIds = await GetTaskRelatedUsers(task, context);
                        var hoursOverdue = (currentTime - task.DueDate!.Value) / 3600;

                        if (userIds.Count > 0)
                        {
                            await notificationService.CreateAndSendNotificationsAsync(
                                userIds,
                                "مهمة متأخرة",
                                $"المهمة رقم #{task.Id}: '{task.Title}' متأخرة بـ {hoursOverdue:F0} ساعة",
                                "task_overdue",
                                task.Id
                            );

                            _logger.LogInformation("تم إرسال إشعار تأخير للمهمة {TaskId}", task.Id);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في معالجة المهمة المتأخرة {TaskId}", taskData.Id);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص المهام المتأخرة");
            }
        }

        /// <summary>
        /// الحصول على المستخدمين المرتبطين بالمهمة
        /// </summary>
        private async System.Threading.Tasks.Task<List<int>> GetTaskRelatedUsers(TaskModel task, TasksDbContext context)
        {
            var userIds = new List<int>();

            // إضافة منشئ المهمة
            userIds.Add(task.CreatorId);

            // إضافة المسند له
            if (task.AssigneeId.HasValue && !userIds.Contains(task.AssigneeId.Value))
            {
                userIds.Add(task.AssigneeId.Value);
            }

            // إضافة المستخدمين الذين لهم وصول للمهمة
            var accessUsers = await context.TaskAccessUsers
                .Where(au => au.TaskId == task.Id && !userIds.Contains(au.UserId))
                .Select(au => au.UserId)
                .ToListAsync();

            userIds.AddRange(accessUsers);

            return userIds;
        }
    }
}
