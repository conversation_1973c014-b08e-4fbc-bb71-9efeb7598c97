-- فحص إعدادات الترميز في قاعدة البيانات
USE databasetasks;

-- 1. فحص إعدادات قاعدة البيانات
SELECT 
    name,
    collation_name,
    is_ansi_null_default_on,
    is_ansi_nulls_on,
    is_ansi_padding_on,
    is_ansi_warnings_on
FROM sys.databases 
WHERE name = 'databasetasks';

-- 2. فحص إعدادات جدول الصلاحيات
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    COLLATION_NAME
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'permissions' 
AND COLUMN_NAME IN ('name', 'description', 'permission_group', 'category');

-- 3. فحص عينة من البيانات الحالية
SELECT TOP 5
    id,
    name,
    description,
    LEN(description) as desc_length,
    ASCII(LEFT(description, 1)) as first_char_ascii
FROM permissions 
WHERE id IN (40, 41, 42, 43, 44)
ORDER BY id;
