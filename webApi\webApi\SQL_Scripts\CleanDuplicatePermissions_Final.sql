-- سكريبت التنظيف النهائي للتكرارات - حل جذري
-- يحذ<PERSON> التكرارات بطريقة آمنة ومضمونة

USE [databasetasks]
GO

PRINT '🧹 بدء التنظيف النهائي للتكرارات...'

-- إنشاء نسخة احتياطية شاملة
PRINT ''
PRINT '💾 إنشاء نسخة احتياطية شاملة...'

-- نسخ احتياطي لجدول الصلاحيات
SELECT * INTO permissions_backup_final FROM permissions
PRINT '✅ تم إنشاء نسخة احتياطية: permissions_backup_final'

-- نسخ احتياطي لجدول role_default_permissions
SELECT * INTO role_default_permissions_backup FROM role_default_permissions
PRINT '✅ تم إنشاء نسخة احتياطية: role_default_permissions_backup'

-- نسخ احتياطي لجدول user_permissions إذا كان موجوداً
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'user_permissions')
BEGIN
    SELECT * INTO user_permissions_backup FROM user_permissions
    PRINT '✅ تم إنشاء نسخة احتياطية: user_permissions_backup'
END

-- عرض التكرارات قبل الحذف
PRINT ''
PRINT '🔍 التكرارات المكتشفة:'
SELECT 
    name as [اسم الصلاحية],
    COUNT(*) as [عدد التكرارات],
    MIN(id) as [أقل ID],
    MAX(id) as [أعلى ID]
FROM permissions 
GROUP BY name 
HAVING COUNT(*) > 1
ORDER BY COUNT(*) DESC, name

-- الخطوة 1: حذف جميع المراجع للصلاحيات المكررة
PRINT ''
PRINT '🗑️ حذف المراجع للصلاحيات المكررة...'

-- حذف من role_default_permissions للصلاحيات المكررة (الاحتفاظ بأحدث ID فقط)
DELETE rdp
FROM role_default_permissions rdp
INNER JOIN permissions p ON rdp.permission_id = p.id
WHERE p.id IN (
    SELECT p1.id
    FROM permissions p1
    INNER JOIN (
        SELECT name, MAX(id) as max_id
        FROM permissions 
        GROUP BY name
        HAVING COUNT(*) > 1
    ) p2 ON p1.name = p2.name
    WHERE p1.id != p2.max_id  -- حذف المراجع للنسخ القديمة فقط
)

DECLARE @DeletedRolePermissions INT = @@ROWCOUNT
PRINT '✅ تم حذف ' + CAST(@DeletedRolePermissions AS NVARCHAR(10)) + ' مرجع من role_default_permissions'

-- حذف من user_permissions للصلاحيات المكررة (إذا كان الجدول موجوداً)
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'user_permissions')
BEGIN
    DELETE up
    FROM user_permissions up
    INNER JOIN permissions p ON up.permission_id = p.id
    WHERE p.id IN (
        SELECT p1.id
        FROM permissions p1
        INNER JOIN (
            SELECT name, MAX(id) as max_id
            FROM permissions 
            GROUP BY name
            HAVING COUNT(*) > 1
        ) p2 ON p1.name = p2.name
        WHERE p1.id != p2.max_id
    )

    DECLARE @DeletedUserPermissions INT = @@ROWCOUNT
    PRINT '✅ تم حذف ' + CAST(@DeletedUserPermissions AS NVARCHAR(10)) + ' مرجع من user_permissions'
END

-- الخطوة 2: حذف الصلاحيات المكررة (الاحتفاظ بأحدث ID)
PRINT ''
PRINT '🗑️ حذف الصلاحيات المكررة...'

;WITH DuplicatePermissions AS (
    SELECT 
        id,
        name,
        ROW_NUMBER() OVER (PARTITION BY name ORDER BY id DESC) as rn
    FROM permissions
)
DELETE FROM permissions 
WHERE id IN (
    SELECT id 
    FROM DuplicatePermissions 
    WHERE rn > 1
);

DECLARE @DeletedPermissions INT = @@ROWCOUNT
PRINT '✅ تم حذف ' + CAST(@DeletedPermissions AS NVARCHAR(10)) + ' صلاحية مكررة'

-- الخطوة 3: تنظيف البيانات الغريبة
PRINT ''
PRINT '🧽 تنظيف البيانات الغريبة...'

-- حذف المراجع للصلاحيات الغريبة أولاً
DELETE rdp
FROM role_default_permissions rdp
INNER JOIN permissions p ON rdp.permission_id = p.id
WHERE p.description = 'q' 
   OR p.description IS NULL 
   OR p.description = ''
   OR p.permission_group = 'CustomRoles'

-- حذف من user_permissions إذا كان موجوداً
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'user_permissions')
BEGIN
    DELETE up
    FROM user_permissions up
    INNER JOIN permissions p ON up.permission_id = p.id
    WHERE p.description = 'q' 
       OR p.description IS NULL 
       OR p.description = ''
       OR p.permission_group = 'CustomRoles'
END

-- الآن حذف الصلاحيات الغريبة
DELETE FROM permissions 
WHERE description = 'q' 
   OR description IS NULL 
   OR description = ''
   OR permission_group = 'CustomRoles';

DECLARE @DeletedWeirdData INT = @@ROWCOUNT
IF @DeletedWeirdData > 0
BEGIN
    PRINT '✅ تم حذف ' + CAST(@DeletedWeirdData AS NVARCHAR(10)) + ' صلاحية ذات بيانات غريبة'
END
ELSE
BEGIN
    PRINT '✅ لا توجد بيانات غريبة للحذف'
END

-- الخطوة 4: إنشاء الفهرس الفريد
PRINT ''
PRINT '🔧 إنشاء الفهرس الفريد...'

-- التحقق من عدم وجود تكرارات
DECLARE @RemainingDuplicates INT
SELECT @RemainingDuplicates = COUNT(*) 
FROM (
    SELECT name 
    FROM permissions 
    GROUP BY name 
    HAVING COUNT(*) > 1
) as duplicates

IF @RemainingDuplicates = 0
BEGIN
    -- إنشاء فهرس فريد
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_permissions_name_unique')
    BEGIN
        CREATE UNIQUE INDEX IX_permissions_name_unique ON permissions (name)
        PRINT '✅ تم إنشاء فهرس فريد لمنع التكرار المستقبلي'
    END
    ELSE
    BEGIN
        PRINT '✅ الفهرس الفريد موجود بالفعل'
    END
END
ELSE
BEGIN
    PRINT '⚠️ لا يمكن إنشاء الفهرس الفريد - لا تزال هناك ' + CAST(@RemainingDuplicates AS NVARCHAR(10)) + ' تكرارات'
END

-- عرض النتائج النهائية
PRINT ''
PRINT '📊 النتائج النهائية:'
SELECT 
    COUNT(*) as [إجمالي الصلاحيات],
    COUNT(DISTINCT name) as [الصلاحيات الفريدة],
    COUNT(*) - COUNT(DISTINCT name) as [التكرارات المتبقية],
    COUNT(DISTINCT permission_group) as [عدد المجموعات]
FROM permissions

-- فحص نهائي للتكرارات
PRINT ''
PRINT '🔍 فحص نهائي للتكرارات:'
IF @RemainingDuplicates = 0
BEGIN
    PRINT '🎉 ممتاز! تم حل جميع التكرارات بنجاح'
END
ELSE
BEGIN
    PRINT '⚠️ لا تزال هناك تكرارات:'
    SELECT 
        name as [الصلاحية المكررة],
        COUNT(*) as [عدد التكرارات]
    FROM permissions 
    GROUP BY name 
    HAVING COUNT(*) > 1
END

-- تقرير نهائي شامل
PRINT ''
PRINT '📄 تقرير التنظيف النهائي:'
PRINT '================================'
PRINT '• مراجع الأدوار المحذوفة: ' + CAST(@DeletedRolePermissions AS NVARCHAR(10))
PRINT '• الصلاحيات المحذوفة: ' + CAST(@DeletedPermissions AS NVARCHAR(10))
PRINT '• البيانات الغريبة المحذوفة: ' + CAST(@DeletedWeirdData AS NVARCHAR(10))
PRINT '• التكرارات المتبقية: ' + CAST(@RemainingDuplicates AS NVARCHAR(10))

PRINT ''
PRINT '💡 ملاحظات مهمة:'
PRINT '• تم إنشاء نسخ احتياطية لجميع الجداول المتأثرة'
PRINT '• تم الاحتفاظ بأحدث نسخة من كل صلاحية مكررة'
PRINT '• تم حذف جميع المراجع للصلاحيات المحذوفة'

IF @RemainingDuplicates = 0
BEGIN
    PRINT ''
    PRINT '🎉 تم تنظيف جدول الصلاحيات بنجاح!'
    PRINT 'يمكنك الآن إضافة صلاحيات جديدة بأمان.'
END
ELSE
BEGIN
    PRINT ''
    PRINT '⚠️ يرجى مراجعة التكرارات المتبقية يدوياً'
END

GO
