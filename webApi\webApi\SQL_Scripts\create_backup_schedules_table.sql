-- إنشاء جدول جدولة النسخ الاحتياطية
-- Create backup schedules table

-- التحقق من وجود الجدول وإنشاؤه إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='backup_schedules' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[backup_schedules] (
        [id] [int] IDENTITY(1,1) NOT NULL,
        [name] [nvarchar](255) NOT NULL,
        [description] [nvarchar](max) NULL,
        [schedule_type] [nvarchar](20) NOT NULL,
        [cron_expression] [nvarchar](100) NOT NULL,
        [is_active] [bit] NOT NULL DEFAULT 1,
        [last_run_at] [bigint] NULL,
        [next_run_at] [bigint] NULL,
        [retention_count] [int] NOT NULL DEFAULT 7,
        [created_by] [int] NOT NULL,
        [created_at] [bigint] NOT NULL,
        [updated_at] [bigint] NULL,
        [updated_by] [int] NULL,
        [successful_runs] [int] NOT NULL DEFAULT 0,
        [failed_runs] [int] NOT NULL DEFAULT 0,
        [last_error_message] [nvarchar](max) NULL,
        
        CONSTRAINT [PK_backup_schedules] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_backup_schedules_created_by_users] FOREIGN KEY ([created_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [FK_backup_schedules_updated_by_users] FOREIGN KEY ([updated_by]) REFERENCES [dbo].[users] ([id])
    ) ON [PRIMARY]

    -- إضافة فهارس لتحسين الأداء
    CREATE INDEX [IX_backup_schedules_is_active] ON [dbo].[backup_schedules] ([is_active])
    CREATE INDEX [IX_backup_schedules_schedule_type] ON [dbo].[backup_schedules] ([schedule_type])
    CREATE INDEX [IX_backup_schedules_next_run_at] ON [dbo].[backup_schedules] ([next_run_at])
    CREATE INDEX [IX_backup_schedules_created_by] ON [dbo].[backup_schedules] ([created_by])

    PRINT 'تم إنشاء جدول backup_schedules بنجاح'
END
ELSE
BEGIN
    PRINT 'جدول backup_schedules موجود بالفعل'
END

-- إدراج بعض الجدولات الافتراضية
IF NOT EXISTS (SELECT * FROM [dbo].[backup_schedules] WHERE [name] = 'نسخة احتياطية يومية')
BEGIN
    INSERT INTO [dbo].[backup_schedules] 
    ([name], [description], [schedule_type], [cron_expression], [is_active], [retention_count], [created_by], [created_at])
    VALUES 
    (N'نسخة احتياطية يومية', N'نسخة احتياطية تلقائية يومية في الساعة 2:00 صباحاً', 'daily', '0 2 * * *', 1, 7, 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))

    INSERT INTO [dbo].[backup_schedules] 
    ([name], [description], [schedule_type], [cron_expression], [is_active], [retention_count], [created_by], [created_at])
    VALUES 
    (N'نسخة احتياطية أسبوعية', N'نسخة احتياطية تلقائية أسبوعية يوم الأحد في الساعة 1:00 صباحاً', 'weekly', '0 1 * * 0', 0, 4, 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))

    INSERT INTO [dbo].[backup_schedules] 
    ([name], [description], [schedule_type], [cron_expression], [is_active], [retention_count], [created_by], [created_at])
    VALUES 
    (N'نسخة احتياطية شهرية', N'نسخة احتياطية تلقائية شهرية في اليوم الأول من كل شهر', 'monthly', '0 0 1 * *', 0, 12, 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))

    PRINT 'تم إدراج الجدولات الافتراضية بنجاح'
END

-- التحقق من النتائج
SELECT COUNT(*) as 'عدد الجدولات' FROM [dbo].[backup_schedules]
SELECT * FROM [dbo].[backup_schedules]
