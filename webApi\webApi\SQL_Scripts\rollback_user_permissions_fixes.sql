-- ===================================================================
-- سكريبت التراجع عن إصلاحات UserPermissions
-- تاريخ الإنشاء: 2025-01-05
-- الهدف: التراجع عن التغييرات في حالة وجود مشاكل
-- تحذير: استخدم هذا السكريبت بحذر!
-- ===================================================================

USE [TaskManagementDB]
GO

PRINT '⚠️ تحذير: سيتم التراجع عن إصلاحات UserPermissions'
PRINT '=================================================='
PRINT 'هذا السكريبت سيقوم بـ:'
PRINT '1. حذف الجداول الجديدة (custom_roles, user_custom_roles, custom_role_permissions)'
PRINT '2. حذف عمود created_at من user_permissions (إذا كان فارغاً)'
PRINT '3. حذف الفهارس الجديدة'
PRINT '=================================================='

-- إيقاف مؤقت للتأكيد (قم بإلغاء التعليق إذا كنت متأكداً)
-- PRINT 'قم بإلغاء التعليق عن السطر التالي إذا كنت متأكداً من التراجع'
-- RETURN

PRINT '🚀 بدء عملية التراجع...'

-- ===================================================================
-- 1. حذف الفهارس الجديدة
-- ===================================================================

PRINT '🗑️ حذف الفهارس الجديدة...'

-- حذف فهرس created_at من user_permissions
IF EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_user_permissions_created_at')
BEGIN
    DROP INDEX [IX_user_permissions_created_at] ON [dbo].[user_permissions]
    PRINT '✅ تم حذف فهرس IX_user_permissions_created_at'
END

-- حذف فهرس is_deleted من custom_roles
IF EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_custom_roles_is_deleted')
BEGIN
    DROP INDEX [IX_custom_roles_is_deleted] ON [dbo].[custom_roles]
    PRINT '✅ تم حذف فهرس IX_custom_roles_is_deleted'
END

-- حذف فهرس is_deleted من user_custom_roles
IF EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_user_custom_roles_is_deleted')
BEGIN
    DROP INDEX [IX_user_custom_roles_is_deleted] ON [dbo].[user_custom_roles]
    PRINT '✅ تم حذف فهرس IX_user_custom_roles_is_deleted'
END

-- ===================================================================
-- 2. حذف الجداول الجديدة (بالترتيب الصحيح للمفاتيح الخارجية)
-- ===================================================================

PRINT '🗑️ حذف الجداول الجديدة...'

-- حذف custom_role_permissions أولاً
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'custom_role_permissions')
BEGIN
    DROP TABLE [dbo].[custom_role_permissions]
    PRINT '✅ تم حذف جدول custom_role_permissions'
END

-- حذف user_custom_roles ثانياً
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'user_custom_roles')
BEGIN
    DROP TABLE [dbo].[user_custom_roles]
    PRINT '✅ تم حذف جدول user_custom_roles'
END

-- حذف custom_roles أخيراً
IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'custom_roles')
BEGIN
    DROP TABLE [dbo].[custom_roles]
    PRINT '✅ تم حذف جدول custom_roles'
END

-- ===================================================================
-- 3. التعامل مع عمود created_at في user_permissions
-- ===================================================================

PRINT '🔍 التحقق من عمود created_at في user_permissions...'

IF EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'user_permissions' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    -- التحقق من وجود بيانات في العمود
    DECLARE @RecordsWithCreatedAt INT
    SELECT @RecordsWithCreatedAt = COUNT(*) 
    FROM [dbo].[user_permissions] 
    WHERE created_at IS NOT NULL AND created_at > 0
    
    IF @RecordsWithCreatedAt = 0
    BEGIN
        -- إذا لم تكن هناك بيانات، احذف العمود
        ALTER TABLE [dbo].[user_permissions] DROP COLUMN [created_at]
        PRINT '✅ تم حذف عمود created_at من user_permissions (لم تكن هناك بيانات)'
    END
    ELSE
    BEGIN
        PRINT '⚠️ تم الاحتفاظ بعمود created_at لأنه يحتوي على بيانات (' + CAST(@RecordsWithCreatedAt AS NVARCHAR(10)) + ' سجل)'
        PRINT '   يمكنك حذفه يدوياً إذا كنت متأكداً:'
        PRINT '   ALTER TABLE [dbo].[user_permissions] DROP COLUMN [created_at]'
    END
END
ELSE
BEGIN
    PRINT '✅ عمود created_at غير موجود في user_permissions'
END

-- ===================================================================
-- 4. التحقق النهائي
-- ===================================================================

PRINT '🔍 التحقق النهائي...'

-- التحقق من الجداول المحذوفة
DECLARE @DeletedTables TABLE (TableName NVARCHAR(50), Status NVARCHAR(20))

INSERT INTO @DeletedTables
SELECT 'custom_roles', CASE WHEN TABLE_NAME IS NULL THEN '✅ محذوف' ELSE '❌ لا يزال موجود' END
FROM (SELECT 'custom_roles' as TN) t
LEFT JOIN INFORMATION_SCHEMA.TABLES ON TABLE_NAME = t.TN

INSERT INTO @DeletedTables
SELECT 'user_custom_roles', CASE WHEN TABLE_NAME IS NULL THEN '✅ محذوف' ELSE '❌ لا يزال موجود' END
FROM (SELECT 'user_custom_roles' as TN) t
LEFT JOIN INFORMATION_SCHEMA.TABLES ON TABLE_NAME = t.TN

INSERT INTO @DeletedTables
SELECT 'custom_role_permissions', CASE WHEN TABLE_NAME IS NULL THEN '✅ محذوف' ELSE '❌ لا يزال موجود' END
FROM (SELECT 'custom_role_permissions' as TN) t
LEFT JOIN INFORMATION_SCHEMA.TABLES ON TABLE_NAME = t.TN

SELECT TableName, Status FROM @DeletedTables

-- التحقق من الفهارس المحذوفة
SELECT 
    'IX_user_permissions_created_at' as IndexName,
    CASE WHEN COUNT(*) = 0 THEN '✅ محذوف' ELSE '❌ لا يزال موجود' END as Status
FROM sys.indexes 
WHERE name = 'IX_user_permissions_created_at'

UNION ALL

SELECT 
    'IX_custom_roles_is_deleted' as IndexName,
    CASE WHEN COUNT(*) = 0 THEN '✅ محذوف' ELSE '❌ لا يزال موجود' END as Status
FROM sys.indexes 
WHERE name = 'IX_custom_roles_is_deleted'

UNION ALL

SELECT 
    'IX_user_custom_roles_is_deleted' as IndexName,
    CASE WHEN COUNT(*) = 0 THEN '✅ محذوف' ELSE '❌ لا يزال موجود' END as Status
FROM sys.indexes 
WHERE name = 'IX_user_custom_roles_is_deleted'

-- ===================================================================
-- 5. تنظيف إضافي (اختياري)
-- ===================================================================

PRINT '🧹 تنظيف إضافي...'

-- حذف أي إجراءات مخزنة مؤقتة تم إنشاؤها
IF EXISTS (SELECT 1 FROM sys.procedures WHERE name = 'sp_AddRolePermission')
BEGIN
    DROP PROCEDURE sp_AddRolePermission
    PRINT '✅ تم حذف الإجراء المخزن sp_AddRolePermission'
END

PRINT '=================================================='
PRINT '🎉 تم الانتهاء من عملية التراجع'
PRINT '=================================================='
PRINT ''
PRINT '📋 ملخص ما تم:'
PRINT '✅ حذف الفهارس الجديدة'
PRINT '✅ حذف الجداول الجديدة (custom_roles, user_custom_roles, custom_role_permissions)'
PRINT '✅ التعامل مع عمود created_at في user_permissions'
PRINT '✅ تنظيف الإجراءات المخزنة المؤقتة'
PRINT ''
PRINT '⚠️ ملاحظات مهمة:'
PRINT '1. تأكد من إعادة تشغيل التطبيق'
PRINT '2. قد تحتاج لتحديث Entity Framework Models'
PRINT '3. تأكد من أن التطبيق يعمل بدون أخطاء'
PRINT ''
PRINT '🔄 إذا كنت تريد إعادة تطبيق الإصلاحات، شغل:'
PRINT '   fix_user_permissions_database.sql'

GO
