-- Script to fix encoded permission descriptions
-- Creation Date: 2025-01-06
-- Purpose: Fix encoded descriptions in permissions table
-- Encoding: UTF-8

-- Start transaction
BEGIN TRANSACTION;

-- Fix first group: Records 40-71 (first encoded descriptions)
UPDATE permissions SET description = 'ادارة لوحة التحكم' WHERE id = 40;
UPDATE permissions SET description = 'عرض المهام' WHERE id = 41;
UPDATE permissions SET description = 'انشاء مهام جديدة' WHERE id = 42;
UPDATE permissions SET description = 'تعديل المهام' WHERE id = 43;
UPDATE permissions SET description = 'حذف المهام' WHERE id = 44;
UPDATE permissions SET description = 'تعيين المهام للمستخدمين' WHERE id = 45;
UPDATE permissions SET description = 'تحديث المهام الخاصة' WHERE id = 46;
UPDATE permissions SET description = 'عرض المستخدمين' WHERE id = 47;
UPDATE permissions SET description = 'انشاء مستخدمين جدد' WHERE id = 48;
UPDATE permissions SET description = 'تعديل بيانات المستخدمين' WHERE id = 49;
UPDATE permissions SET description = 'ادارة ادوار المستخدمين' WHERE id = 51;
UPDATE permissions SET description = 'عرض التقارير' WHERE id = 52;
UPDATE permissions SET description = 'انشاء تقارير جديدة' WHERE id = 53;
UPDATE permissions SET description = 'تصدير التقارير' WHERE id = 54;
UPDATE permissions SET description = 'ادارة النظام' WHERE id = 55;
UPDATE permissions SET description = 'انشاء نسخ احتياطية' WHERE id = 56;
UPDATE permissions SET description = 'استعادة النسخ الاحتياطية' WHERE id = 57;
UPDATE permissions SET description = 'ادارة قاعدة البيانات' WHERE id = 58;
UPDATE permissions SET description = 'عرض الملف الشخصي' WHERE id = 59;
UPDATE permissions SET description = 'تعديل الملف الشخصي' WHERE id = 60;
UPDATE permissions SET description = 'عرض الاشعارات' WHERE id = 61;
UPDATE permissions SET description = 'ادارة الاشعارات' WHERE id = 62;
UPDATE permissions SET description = 'ادارة التقويم' WHERE id = 64;
UPDATE permissions SET description = 'عرض الاقسام' WHERE id = 65;
UPDATE permissions SET description = 'ادارة الاقسام' WHERE id = 66;
UPDATE permissions SET description = 'عرض جميع المهام' WHERE id = 71;

-- Fix second group: Records 1205-1227 (second encoded descriptions)
UPDATE permissions SET description = 'عرض تفاصيل المهام والانتقال اليها' WHERE id = 1205;
UPDATE permissions SET description = 'تحديث نسبة انجاز المهام' WHERE id = 1206;
UPDATE permissions SET description = 'تصفية وفلترة المهام' WHERE id = 1207;
UPDATE permissions SET description = 'ترتيب المهام حسب معايير مختلفة' WHERE id = 1208;
UPDATE permissions SET description = 'ادارة لوحة المهام واضافة اعمدة' WHERE id = 1209;
UPDATE permissions SET description = 'تثبيت والغاء تثبيت الرسائل' WHERE id = 1210;
UPDATE permissions SET description = 'تعديل الرسائل المرسلة' WHERE id = 1211;
UPDATE permissions SET description = 'حذف الرسائل' WHERE id = 1212;
UPDATE permissions SET description = 'الرد على الرسائل' WHERE id = 1213;
UPDATE permissions SET description = 'تحديد الرسائل للمتابعة' WHERE id = 1214;
UPDATE permissions SET description = 'تعيين مدير للقسم' WHERE id = 1215;
UPDATE permissions SET description = 'اضافة مستخدمين للقسم' WHERE id = 1216;
UPDATE permissions SET description = 'ازالة مستخدمين من القسم' WHERE id = 1217;
UPDATE permissions SET description = 'ادارة شاملة لمستخدمي القسم' WHERE id = 1218;
UPDATE permissions SET description = 'عرض تقارير المساهمات' WHERE id = 1219;
UPDATE permissions SET description = 'تصدير التقارير كـ PDF' WHERE id = 1220;
UPDATE permissions SET description = 'تقارير عبء العمل والاداء' WHERE id = 1221;
UPDATE permissions SET description = 'تغيير كلمة المرور' WHERE id = 1222;
UPDATE permissions SET description = 'اصلاح قاعدة البيانات' WHERE id = 1223;
UPDATE permissions SET description = 'انشاء نسخ احتياطية لقاعدة البيانات' WHERE id = 1224;
UPDATE permissions SET description = 'استعادة النسخ الاحتياطية لقاعدة البيانات' WHERE id = 1225;
UPDATE permissions SET description = 'اختبار الصلاحيات والتحقق منها' WHERE id = 1226;
UPDATE permissions SET description = 'ادوات التشخيص والتطوير' WHERE id = 1227;

-- Fix third group: Records with question marks
UPDATE permissions SET description = 'اعدادات الاشعارات' WHERE id = 1232;
UPDATE permissions SET description = 'الوصول الديناميكي للتقارير' WHERE id = 1233;
UPDATE permissions SET description = 'اصلاح وصيانة قاعدة البيانات' WHERE id = 1234;
UPDATE permissions SET description = 'عرض وثائق الارشيف' WHERE id = 1235;
UPDATE permissions SET description = 'ادارة تاريخ البحث' WHERE id = 1236;

-- Fix encoded permission names
UPDATE permissions SET name = 'settings.notifications' WHERE id = 1232;
UPDATE permissions SET name = 'reports.dynamic_access' WHERE id = 1233;
UPDATE permissions SET name = 'admin.database_repair' WHERE id = 1234;
UPDATE permissions SET name = 'archive.view_documents' WHERE id = 1235;
UPDATE permissions SET name = 'search.manage_history' WHERE id = 1236;

-- Fix encoded permission groups
UPDATE permissions SET permission_group = 'Settings' WHERE id = 1232;
UPDATE permissions SET permission_group = 'Reports' WHERE id = 1233;
UPDATE permissions SET permission_group = 'Admin' WHERE id = 1234;
UPDATE permissions SET permission_group = 'Archive' WHERE id = 1235;
UPDATE permissions SET permission_group = 'Search' WHERE id = 1236;

-- Fix encoded categories
UPDATE permissions SET category = 'اعدادات' WHERE id = 1232;
UPDATE permissions SET category = 'تقارير' WHERE id = 1233;
UPDATE permissions SET category = 'ادارة' WHERE id = 1234;
UPDATE permissions SET category = 'ارشيف' WHERE id = 1235;
UPDATE permissions SET category = 'بحث' WHERE id = 1236;

-- Update modification timestamp for all fixed records
UPDATE permissions 
SET updated_at = UNIX_TIMESTAMP() 
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
);

-- Verify results
SELECT 
    id, 
    name, 
    description, 
    permission_group,
    category
FROM permissions 
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
)
ORDER BY id;

-- End transaction
COMMIT;

-- Success message
SELECT 'تم اصلاح جميع الاوصاف المرمزة بنجاح!' as result;
