import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/dashboard_models.dart';
import 'package:get/get.dart';

import '../../../constants/app_styles.dart';

import '../common/loading_indicator.dart';
// تم إزالة database_helper - سيتم استخدام API
import '../../../controllers/task_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../controllers/department_controller.dart';
import '../../../routes/app_routes.dart';
import '../../../services/api/system_statistics_api_service.dart';
import '../../../services/api/reports_api_service.dart';
// Unused imports removed:
// import '../../models/task_model.dart';
// import '../../models/user_model.dart';
// import '../../models/department_model.dart';

/// مكون عنصر لوحة المعلومات الحديث
///
/// يعرض عنصر المخطط في لوحة المعلومات بتصميم حديث وبسيط
class ModernDashboardWidget extends StatefulWidget {
  /// عنصر لوحة المعلومات
  final DashboardWidget widget;

  /// هل في وضع التعديل
  final bool isEditing;

  /// حدث الحذف
  final VoidCallback? onDelete;

  /// حدث التعديل
  final VoidCallback? onEdit;

  const ModernDashboardWidget({
    super.key,
    required this.widget,
    this.isEditing = false,
    this.onDelete,
    this.onEdit,
  });

  @override
  State<ModernDashboardWidget> createState() => _ModernDashboardWidgetState();
}

class _ModernDashboardWidgetState extends State<ModernDashboardWidget> {
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  List<Map<String, dynamic>> _data = [];

  // تعريف المتحكمات والخدمات
  late final TaskController _taskController;
  late final UserController _userController;
  late final DepartmentController _departmentController;
  late final SystemStatisticsApiService _statisticsApiService;
  late final ReportsApiService _reportsApiService;
  // تم إزالة DatabaseHelper - سيتم استخدام API

  @override
  void initState() {
    super.initState();
    _initControllers();
    _loadData();
  }

  /// تهيئة المتحكمات والخدمات
  void _initControllers() {
    // التحقق من وجود المتحكمات أو تسجيلها
    if (!Get.isRegistered<TaskController>()) {
      Get.put(TaskController());
    }
    if (!Get.isRegistered<UserController>()) {
      Get.put(UserController());
    }
    if (!Get.isRegistered<DepartmentController>()) {
      Get.put(DepartmentController());
    }

    // الحصول على المتحكمات
    _taskController = Get.find<TaskController>();
    _userController = Get.find<UserController>();
    _departmentController = Get.find<DepartmentController>();

    // تهيئة الخدمات
    _statisticsApiService = SystemStatisticsApiService();
    _reportsApiService = ReportsApiService();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = '';
    });

    try {
      // تحميل البيانات الفعلية من قاعدة البيانات حسب نوع العنصر
      switch (widget.widget.type) {
        case 'taskStatusChart':
          await _loadTaskStatusData();
          break;
        case 'taskProgressChart':
          await _loadTaskProgressData();
          break;
        case 'userPerformanceChart':
          await _loadUserPerformanceData();
          break;
        case 'departmentPerformanceChart':
          await _loadDepartmentPerformanceData();
          break;
        case 'taskCounters':
          await _loadTaskCountersData();
          break;
        default:
          _data = [];
          break;
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
      });
    }
  }

  /// عرض المخطط بملء الشاشة
  void _showFullscreenChartView() {
    // تحويل البيانات إلى إعدادات
    final Map<String, dynamic> settings = {};

    // إضافة نوع المخطط
    switch (widget.widget.type) {
      case 'taskStatusChart':
        settings['chartType'] = 'pie';
        break;
      case 'taskProgressChart':
        settings['chartType'] = 'line';
        break;
      case 'userPerformanceChart':
      case 'departmentPerformanceChart':
        settings['chartType'] = 'bar';
        break;
      default:
        settings['chartType'] = 'bar';
    }

    // إضافة إعدادات أخرى
    settings['timeRange'] = 'month';
    settings['showLegend'] = true;
    settings['showValues'] = true;

    // فتح الشاشة بملء الشاشة باستخدام المسار المسمى
    Get.toNamed(
      AppRoutes.fullscreenChartView,
      arguments: {
        'widget': widget.widget,
        'settings': settings,
        'onSettingsUpdated': (Map<String, dynamic> updatedSettings) {
          // تحديث الإعدادات وإعادة تحميل البيانات
          setState(() {
            _loadData();
          });
        },
      },
    );
  }

  /// تحميل بيانات حالة المهام
  Future<void> _loadTaskStatusData() async {
    try {
      // استخدام API للحصول على إحصائيات المهام
      final stats = await _reportsApiService.getTasksStatistics();

      // استخراج بيانات المهام حسب الحالة
      final tasksByStatus = stats['tasksByStatus'] as List<dynamic>? ?? [];

      // تحويل البيانات إلى الصيغة المطلوبة للرسم البياني
      _data = [];

      // ألوان افتراضية للحالات
      final statusColors = {
        'قيد الانتظار': Colors.orange,
        'قيد التنفيذ': Colors.blue,
        'في انتظار معلومات': Colors.amber,
        'مكتملة': Colors.green,
        'ملغاة': Colors.grey,
        'جديدة': Colors.purple,
      };

      for (final statusData in tasksByStatus) {
        final status =
            statusData['status'] ?? statusData['Status'] ?? 'غير محدد';
        final count = statusData['count'] ?? statusData['Count'] ?? 0;

        _data.add({
          'label': status,
          'value': count,
          'color': statusColors[status] ?? Colors.grey,
        });
      }

      // إذا لم تكن هناك بيانات، أضف بيانات افتراضية
      if (_data.isEmpty) {
        _data = [
          {'label': 'لا توجد مهام', 'value': 0, 'color': Colors.grey},
        ];
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات حالة المهام: $e');
      // في حالة الخطأ، استخدم البيانات المحلية كبديل
      await _loadTaskStatusDataFallback();
    }
  }

  /// تحميل بيانات حالة المهام كبديل في حالة فشل الـ API
  Future<void> _loadTaskStatusDataFallback() async {
    try {
      await _taskController.loadAllTasks();
      final tasks = _taskController.allTasks;

      final Map<String, int> tasksByStatus = {
        'pending': 0,
        'in_progress': 0,
        'waiting_for_info': 0,
        'completed': 0,
        'cancelled': 0,
      };

      for (final task in tasks) {
        if (!task.isDeleted) {
          final statusValue = task.status;
          tasksByStatus[statusValue] = (tasksByStatus[statusValue] ?? 0) + 1;
        }
      }

      _data = [
        {
          'label': 'قيد الانتظار',
          'value': tasksByStatus['pending'] ?? 0,
          'color': Colors.orange
        },
        {
          'label': 'قيد التنفيذ',
          'value': tasksByStatus['in_progress'] ?? 0,
          'color': Colors.blue
        },
        {
          'label': 'في انتظار معلومات',
          'value': tasksByStatus['waiting_for_info'] ?? 0,
          'color': Colors.amber
        },
        {
          'label': 'مكتملة',
          'value': tasksByStatus['completed'] ?? 0,
          'color': Colors.green
        },
        {
          'label': 'ملغاة',
          'value': tasksByStatus['cancelled'] ?? 0,
          'color': Colors.grey
        },
      ];
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات البديلة: $e');
      _data = [
        {'label': 'خطأ في التحميل', 'value': 0, 'color': Colors.red}
      ];
    }
  }

  /// تحميل بيانات تقدم المهام على مدار الوقت
  Future<void> _loadTaskProgressData() async {
    try {
      // TODO: تنفيذ تحميل بيانات تقدم المهام عبر API
      // سيتم استبدال هذا بـ API call

      // بيانات وهمية مؤقتة
      _data = [
        {'month': 'يناير', 'completed': 10, 'inProgress': 5, 'delayed': 2},
        {'month': 'فبراير', 'completed': 15, 'inProgress': 8, 'delayed': 1},
        {'month': 'مارس', 'completed': 12, 'inProgress': 6, 'delayed': 3},
      ];

      // تم استبدال معالجة البيانات ببيانات وهمية مؤقتة
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات تقدم المهام: $e');
      rethrow;
    }
  }

  /// تحميل بيانات أداء المستخدمين
  Future<void> _loadUserPerformanceData() async {
    try {
      // استخدام API للحصول على إحصائيات المستخدمين
      final stats = await _reportsApiService.getUsersStatistics();

      // استخراج بيانات المستخدمين حسب القسم
      final usersByDepartment =
          stats['usersByDepartment'] as List<dynamic>? ?? [];

      // تحويل البيانات إلى الصيغة المطلوبة للرسم البياني
      _data = [];

      for (final deptData in usersByDepartment) {
        final department =
            deptData['department'] ?? deptData['Department'] ?? 'غير محدد';
        final count = deptData['count'] ?? deptData['Count'] ?? 0;

        _data.add({
          'name': department,
          'completed': count, // استخدام عدد المستخدمين كمؤشر للأداء
          'inProgress': 0,
          'delayed': 0,
        });
      }

      // إذا لم تكن هناك بيانات، استخدم البيانات المحلية كبديل
      if (_data.isEmpty) {
        await _loadUserPerformanceDataFallback();
      } else {
        // ترتيب البيانات تنازلياً حسب عدد المستخدمين
        _data.sort(
            (a, b) => (b['completed'] as int).compareTo(a['completed'] as int));

        // أخذ أفضل 5 أقسام فقط
        if (_data.length > 5) {
          _data = _data.sublist(0, 5);
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات أداء المستخدمين: $e');
      // في حالة الخطأ، استخدم البيانات المحلية كبديل
      await _loadUserPerformanceDataFallback();
    }
  }

  /// تحميل بيانات أداء المستخدمين كبديل في حالة فشل الـ API
  Future<void> _loadUserPerformanceDataFallback() async {
    try {
      await _userController.loadAllUsers();
      await _taskController.loadAllTasks();

      final users = _userController.users;
      final tasks = _taskController.allTasks;

      final Map<String, Map<String, dynamic>> userPerformance = {};

      for (final user in users) {
        userPerformance[user.id.toString()] = {
          'completed': 0,
          'inProgress': 0,
          'delayed': 0,
          'name': user.name,
        };
      }

      for (final task in tasks) {
        if (task.isDeleted || task.assigneeId == null) continue;

        final assigneeIdStr = task.assigneeId.toString();
        if (userPerformance.containsKey(assigneeIdStr)) {
          final statusValue = task.status;

          if (statusValue == 'completed') {
            userPerformance[assigneeIdStr]!['completed'] =
                (userPerformance[assigneeIdStr]!['completed'] as int) + 1;
          } else if (statusValue == 'in_progress') {
            userPerformance[assigneeIdStr]!['inProgress'] =
                (userPerformance[assigneeIdStr]!['inProgress'] as int) + 1;
          } else if (statusValue == 'waiting_for_info') {
            userPerformance[assigneeIdStr]!['delayed'] =
                (userPerformance[assigneeIdStr]!['delayed'] as int) + 1;
          }
        }
      }

      _data = [];
      for (final userId in userPerformance.keys) {
        final userData = userPerformance[userId]!;

        if ((userData['completed'] as int) > 0 ||
            (userData['inProgress'] as int) > 0 ||
            (userData['delayed'] as int) > 0) {
          _data.add({
            'name': userData['name'] as String? ?? 'مستخدم',
            'completed': userData['completed'] as int? ?? 0,
            'inProgress': userData['inProgress'] as int? ?? 0,
            'delayed': userData['delayed'] as int? ?? 0,
          });
        }
      }

      _data.sort(
          (a, b) => (b['completed'] as int).compareTo(a['completed'] as int));

      if (_data.length > 5) {
        _data = _data.sublist(0, 5);
      }
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات البديلة للمستخدمين: $e');
      _data = [
        {
          'name': 'خطأ في التحميل',
          'completed': 0,
          'inProgress': 0,
          'delayed': 0
        }
      ];
    }
  }

  /// تحميل بيانات أداء الأقسام
  Future<void> _loadDepartmentPerformanceData() async {
    try {
      // استخدام API للحصول على إحصائيات الأقسام
      final stats = await _statisticsApiService.getDepartmentsStatistics();

      // استخراج بيانات الأقسام
      final departmentStats = stats['departmentStats'] as List<dynamic>? ?? [];

      // تحويل البيانات إلى الصيغة المطلوبة للرسم البياني
      _data = [];

      for (final deptData in departmentStats) {
        final name = deptData['departmentName'] ??
            deptData['DepartmentName'] ??
            'غير محدد';
        final completed =
            deptData['completedTasks'] ?? deptData['CompletedTasks'] ?? 0;
        final pending =
            deptData['pendingTasks'] ?? deptData['PendingTasks'] ?? 0;
        final total = deptData['totalTasks'] ?? deptData['TotalTasks'] ?? 0;

        // إضافة الأقسام التي لديها مهام فقط
        if (total > 0) {
          _data.add({
            'name': name,
            'completed': completed,
            'inProgress': pending,
            'delayed': 0, // سيتم تحديثه لاحقاً إذا توفرت البيانات
          });
        }
      }

      // إذا لم تكن هناك بيانات، استخدم البيانات المحلية كبديل
      if (_data.isEmpty) {
        await _loadDepartmentPerformanceDataFallback();
      } else {
        // ترتيب البيانات تنازلياً حسب عدد المهام المكتملة
        _data.sort(
            (a, b) => (b['completed'] as int).compareTo(a['completed'] as int));

        // أخذ أفضل 5 أقسام فقط
        if (_data.length > 5) {
          _data = _data.sublist(0, 5);
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات أداء الأقسام: $e');
      // في حالة الخطأ، استخدم البيانات المحلية كبديل
      await _loadDepartmentPerformanceDataFallback();
    }
  }

  /// تحميل بيانات أداء الأقسام كبديل في حالة فشل الـ API
  Future<void> _loadDepartmentPerformanceDataFallback() async {
    try {
      await _departmentController.loadAllDepartments();
      await _taskController.loadAllTasks();

      final departments = _departmentController.allDepartments;
      final tasks = _taskController.allTasks;

      final Map<String, Map<String, dynamic>> departmentPerformance = {};

      for (final department in departments) {
        departmentPerformance[department.id.toString()] = {
          'name': department.name,
          'completed': 0,
          'inProgress': 0,
          'delayed': 0,
        };
      }

      for (final task in tasks) {
        if (task.isDeleted) continue;
        if (task.departmentId == null) continue;

        final departmentIdStr = task.departmentId.toString();
        if (departmentPerformance.containsKey(departmentIdStr)) {
          final statusValue = task.status;

          if (statusValue == 'completed') {
            departmentPerformance[departmentIdStr]!['completed'] =
                (departmentPerformance[departmentIdStr]!['completed'] as int) +
                    1;
          } else if (statusValue == 'in_progress') {
            departmentPerformance[departmentIdStr]!['inProgress'] =
                (departmentPerformance[departmentIdStr]!['inProgress'] as int) +
                    1;
          } else if (statusValue == 'waiting_for_info') {
            departmentPerformance[departmentIdStr]!['delayed'] =
                (departmentPerformance[departmentIdStr]!['delayed'] as int) + 1;
          }
        }
      }

      _data = [];
      for (final departmentId in departmentPerformance.keys) {
        final departmentData = departmentPerformance[departmentId]!;

        if ((departmentData['completed'] as int) > 0 ||
            (departmentData['inProgress'] as int) > 0 ||
            (departmentData['delayed'] as int) > 0) {
          _data.add({
            'name': departmentData['name'] as String? ?? 'قسم',
            'completed': departmentData['completed'] as int? ?? 0,
            'inProgress': departmentData['inProgress'] as int? ?? 0,
            'delayed': departmentData['delayed'] as int? ?? 0,
          });
        }
      }

      _data.sort(
          (a, b) => (b['completed'] as int).compareTo(a['completed'] as int));

      if (_data.length > 5) {
        _data = _data.sublist(0, 5);
      }
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات البديلة للأقسام: $e');
      _data = [
        {
          'name': 'خطأ في التحميل',
          'completed': 0,
          'inProgress': 0,
          'delayed': 0
        }
      ];
    }
  }

  /// تحميل بيانات عدادات المهام
  Future<void> _loadTaskCountersData() async {
    try {
      // استخدام API للحصول على إحصائيات المهام
      final stats = await _statisticsApiService.getDashboardStatistics();

      // استخراج البيانات
      final total = stats['totalTasks'] ?? 0;
      final completed = stats['completedTasks'] ?? 0;
      final pending = stats['pendingTasks'] ?? 0;
      final overdue = stats['overdueTasks'] ?? 0;

      // تحويل البيانات إلى الصيغة المطلوبة للرسم البياني
      _data = [
        {
          'total': total,
          'completed': completed,
          'inProgress': pending,
          'delayed': overdue,
        }
      ];
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات عدادات المهام: $e');
      // في حالة الخطأ، استخدم البيانات المحلية كبديل
      await _loadTaskCountersDataFallback();
    }
  }

  /// تحميل بيانات عدادات المهام كبديل في حالة فشل الـ API
  // Future<void> _loadTaskCountersDataFallback() async {
  //   try {
  //     await _taskController.loadAllTasks();
  //     final tasks = _taskController.allTasks.where((task) => !task.isDeleted).toList();

  //     int total = tasks.length;
  //     int completed = tasks.where((task) => task.status == 4).length;
  //     int inProgress = tasks.where((task) => task.status == 2).length;
  //     int delayed = tasks.where((task) => task.status == 3).length;

  //     _data = [
  //       {
  //         'total': total,
  //         'completed': completed,
  //         'inProgress': inProgress,
  //         'delayed': delayed,
  //       }
  //     ];
  //   } catch (e) {
  //     debugPrint('خطأ في تحميل البيانات البديلة للعدادات: $e');
  //     _data = [
  //       {
  //         'total': 0,
  //         'completed': 0,
  //         'inProgress': 0,
  //         'delayed': 0,
  //       }
  //     ];
  //   }
  // }

  /// تحميل بيانات عدادات المهام كبديل في حالة فشل الـ API
  Future<void> _loadTaskCountersDataFallback() async {
    try {
      await _taskController.loadAllTasks();
      final tasks =
          _taskController.allTasks.where((task) => !task.isDeleted).toList();

      int total = tasks.length;
      int completed = tasks.where((task) => task.status == 'completed').length;
      int inProgress = tasks.where((task) => task.status == 'in_progress').length;
      int delayed = tasks.where((task) => task.status == 'waiting_for_info').length;

      _data = [
        {
          'total': total,
          'completed': completed,
          'inProgress': inProgress,
          'delayed': delayed,
        }
      ];
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات البديلة للعدادات: $e');
      _data = [
        {
          'total': 0,
          'completed': 0,
          'inProgress': 0,
          'delayed': 0,
        }
      ];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // رأس العنصر
          _buildHeader(),

          // محتوى العنصر
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildContent(),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رأس العنصر
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          // عنوان العنصر
          Expanded(
            child: Text(
              widget.widget.title,
              style: AppStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // أزرار التحكم
          if (widget.isEditing) ...[
            IconButton(
              icon: const Icon(Icons.edit, size: 20),
              tooltip: 'تعديل',
              onPressed: widget.onEdit,
            ),
            IconButton(
              icon: const Icon(Icons.delete, size: 20),
              tooltip: 'حذف',
              onPressed: widget.onDelete,
            ),
          ] else ...[
            // أيقونة تخصيص المخطط (ملء الشاشة)
            IconButton(
              icon: const Icon(Icons.fullscreen, size: 20),
              tooltip: 'تخصيص المخطط',
              onPressed: _showFullscreenChartView,
            ),
            IconButton(
              icon: const Icon(Icons.refresh, size: 20),
              tooltip: 'تحديث',
              onPressed: _loadData,
            ),
          ],
        ],
      ),
    );
  }

  /// بناء محتوى العنصر
  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: LoadingIndicator(message: 'جاري تحميل البيانات...'),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: AppStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_data.isEmpty) {
      return Center(
        child: Text(
          'لا توجد بيانات متاحة',
          style: AppStyles.bodyMedium,
          textAlign: TextAlign.center,
        ),
      );
    }

    // عرض المحتوى حسب نوع العنصر
    return _buildChartContent();
  }

  /// بناء محتوى المخطط
  Widget _buildChartContent() {
    switch (widget.widget.type) {
      case 'taskStatusChart':
        return _buildPieChart();

      case 'taskProgressChart':
        return _buildLineChart();

      case 'userPerformanceChart':
      case 'departmentPerformanceChart':
        return _buildBarChart();

      case 'taskCounters':
        return _buildKpiCard();

      default:
        return Center(
          child: Text(
            'نوع مخطط غير مدعوم: ${widget.widget.type}',
            style: AppStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
        );
    }
  }

  /// بناء مخطط دائري
  Widget _buildPieChart() {
    return _buildSimpleChart(
      title: 'توزيع المهام حسب الحالة',
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            for (var item in _data)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: item['color'] as Color? ?? Colors.grey,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        item['label'] as String? ?? '',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                    Text(
                      '${item['value']}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء مخطط خطي
  Widget _buildLineChart() {
    return _buildSimpleChart(
      title: 'تقدم المهام على مدار الوقت',
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // مفتاح المخطط
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                _buildLegendItem('مكتملة', Colors.green),
                _buildLegendItem('قيد التنفيذ', Colors.blue),
                _buildLegendItem('متأخرة', Colors.red),
              ],
            ),
            const SizedBox(height: 16),
            // بيانات المخطط
            for (var item in _data)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item['date'] as String? ?? '',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          flex: (item['completed'] as int? ?? 0),
                          child: Container(
                            height: 16,
                            color: Colors.green,
                          ),
                        ),
                        Expanded(
                          flex: (item['inProgress'] as int? ?? 0),
                          child: Container(
                            height: 16,
                            color: Colors.blue,
                          ),
                        ),
                        Expanded(
                          flex: (item['delayed'] as int? ?? 0),
                          child: Container(
                            height: 16,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء مخطط شريطي
  Widget _buildBarChart() {
    return _buildSimpleChart(
      title: widget.widget.type == 'userPerformanceChart'
          ? 'أداء المستخدمين'
          : 'أداء الأقسام',
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // مفتاح المخطط
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                _buildLegendItem('مكتملة', Colors.green),
                _buildLegendItem('قيد التنفيذ', Colors.blue),
                _buildLegendItem('متأخرة', Colors.red),
              ],
            ),
            const SizedBox(height: 16),
            // بيانات المخطط
            for (var item in _data)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item['name'] as String? ?? '',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: LinearProgressIndicator(
                            value: (item['completed'] as int? ?? 0) / 100,
                            backgroundColor: Colors.grey.shade200,
                            valueColor: const AlwaysStoppedAnimation<Color>(
                                Colors.green),
                            minHeight: 10,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text('${item['completed'] ?? 0}'),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: LinearProgressIndicator(
                            value: (item['inProgress'] as int? ?? 0) / 100,
                            backgroundColor: Colors.grey.shade200,
                            valueColor: const AlwaysStoppedAnimation<Color>(
                                Colors.blue),
                            minHeight: 10,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text('${item['inProgress'] ?? 0}'),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: LinearProgressIndicator(
                            value: (item['delayed'] as int? ?? 0) / 100,
                            backgroundColor: Colors.grey.shade200,
                            valueColor:
                                const AlwaysStoppedAnimation<Color>(Colors.red),
                            minHeight: 10,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text('${item['delayed'] ?? 0}'),
                      ],
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة مؤشر الأداء
  Widget _buildKpiCard() {
    final kpiData = _data.isNotEmpty ? _data.first : <String, dynamic>{};
    final total = kpiData['total'] as int? ?? 0;
    final completed = kpiData['completed'] as int? ?? 0;
    final inProgress = kpiData['inProgress'] as int? ?? 0;
    final delayed = kpiData['delayed'] as int? ?? 0;

    return _buildSimpleChart(
      title: 'عدادات المهام',
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildKpiItem('إجمالي المهام', total, Icons.task_alt, Colors.blue),
            const SizedBox(height: 16),
            _buildKpiItem(
                'المهام المكتملة', completed, Icons.check_circle, Colors.green),
            const SizedBox(height: 16),
            _buildKpiItem(
                'المهام قيد التنفيذ', inProgress, Icons.pending, Colors.orange),
            const SizedBox(height: 16),
            _buildKpiItem(
                'المهام المتأخرة', delayed, Icons.warning, Colors.red),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر مؤشر أداء
  Widget _buildKpiItem(String label, int value, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Color.fromRGBO(
                color.r.toInt(), color.g.toInt(), color.b.toInt(), 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
            ),
          ),
        ),
        Text(
          '$value',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// بناء عنصر مفتاح المخطط
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  /// بناء مخطط بسيط
  Widget _buildSimpleChart({required String title, required Widget child}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Expanded(child: child),
      ],
    );
  }
}
