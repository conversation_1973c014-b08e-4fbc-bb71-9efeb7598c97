-- <PERSON>ript to fix encoded permission descriptions
-- Creation Date: 2025-01-06
-- Purpose: Fix encoded descriptions in permissions table
-- Using Latin characters to avoid encoding issues

-- Start transaction
BEGIN TRANSACTION;

-- Fix first group: Records 40-71 (first encoded descriptions)
UPDATE permissions SET description = 'Dashboard Management' WHERE id = 40;
UPDATE permissions SET description = 'View Tasks' WHERE id = 41;
UPDATE permissions SET description = 'Create New Tasks' WHERE id = 42;
UPDATE permissions SET description = 'Edit Tasks' WHERE id = 43;
UPDATE permissions SET description = 'Delete Tasks' WHERE id = 44;
UPDATE permissions SET description = 'Assign Tasks to Users' WHERE id = 45;
UPDATE permissions SET description = 'Update Own Tasks' WHERE id = 46;
UPDATE permissions SET description = 'View Users' WHERE id = 47;
UPDATE permissions SET description = 'Create New Users' WHERE id = 48;
UPDATE permissions SET description = 'Edit User Data' WHERE id = 49;
UPDATE permissions SET description = 'Manage User Roles' WHERE id = 51;
UPDATE permissions SET description = 'View Reports' WHERE id = 52;
UPDATE permissions SET description = 'Create New Reports' WHERE id = 53;
UPDATE permissions SET description = 'Export Reports' WHERE id = 54;
UPDATE permissions SET description = 'System Management' WHERE id = 55;
UPDATE permissions SET description = 'Create Backups' WHERE id = 56;
UPDATE permissions SET description = 'Restore Backups' WHERE id = 57;
UPDATE permissions SET description = 'Database Management' WHERE id = 58;
UPDATE permissions SET description = 'View Profile' WHERE id = 59;
UPDATE permissions SET description = 'Edit Profile' WHERE id = 60;
UPDATE permissions SET description = 'View Notifications' WHERE id = 61;
UPDATE permissions SET description = 'Manage Notifications' WHERE id = 62;
UPDATE permissions SET description = 'Calendar Management' WHERE id = 64;
UPDATE permissions SET description = 'View Departments' WHERE id = 65;
UPDATE permissions SET description = 'Manage Departments' WHERE id = 66;
UPDATE permissions SET description = 'View All Tasks' WHERE id = 71;

-- Fix second group: Records 1205-1227 (second encoded descriptions)
UPDATE permissions SET description = 'View Task Details and Navigate' WHERE id = 1205;
UPDATE permissions SET description = 'Update Task Progress' WHERE id = 1206;
UPDATE permissions SET description = 'Filter and Sort Tasks' WHERE id = 1207;
UPDATE permissions SET description = 'Sort Tasks by Different Criteria' WHERE id = 1208;
UPDATE permissions SET description = 'Manage Task Board and Add Columns' WHERE id = 1209;
UPDATE permissions SET description = 'Pin and Unpin Messages' WHERE id = 1210;
UPDATE permissions SET description = 'Edit Sent Messages' WHERE id = 1211;
UPDATE permissions SET description = 'Delete Messages' WHERE id = 1212;
UPDATE permissions SET description = 'Reply to Messages' WHERE id = 1213;
UPDATE permissions SET description = 'Mark Messages for Follow-up' WHERE id = 1214;
UPDATE permissions SET description = 'Assign Department Manager' WHERE id = 1215;
UPDATE permissions SET description = 'Add Users to Department' WHERE id = 1216;
UPDATE permissions SET description = 'Remove Users from Department' WHERE id = 1217;
UPDATE permissions SET description = 'Comprehensive Department User Management' WHERE id = 1218;
UPDATE permissions SET description = 'View Contribution Reports' WHERE id = 1219;
UPDATE permissions SET description = 'Export Reports as PDF' WHERE id = 1220;
UPDATE permissions SET description = 'Workload and Performance Reports' WHERE id = 1221;
UPDATE permissions SET description = 'Change Password' WHERE id = 1222;
UPDATE permissions SET description = 'Database Repair' WHERE id = 1223;
UPDATE permissions SET description = 'Create Database Backups' WHERE id = 1224;
UPDATE permissions SET description = 'Restore Database Backups' WHERE id = 1225;
UPDATE permissions SET description = 'Test and Verify Permissions' WHERE id = 1226;
UPDATE permissions SET description = 'Diagnostic and Development Tools' WHERE id = 1227;

-- Fix third group: Records with question marks
UPDATE permissions SET description = 'Notification Settings' WHERE id = 1232;
UPDATE permissions SET description = 'Dynamic Report Access' WHERE id = 1233;
UPDATE permissions SET description = 'Database Repair and Maintenance' WHERE id = 1234;
UPDATE permissions SET description = 'View Archive Documents' WHERE id = 1235;
UPDATE permissions SET description = 'Manage Search History' WHERE id = 1236;

-- Fix encoded permission names
UPDATE permissions SET name = 'settings.notifications' WHERE id = 1232;
UPDATE permissions SET name = 'reports.dynamic_access' WHERE id = 1233;
UPDATE permissions SET name = 'admin.database_repair' WHERE id = 1234;
UPDATE permissions SET name = 'archive.view_documents' WHERE id = 1235;
UPDATE permissions SET name = 'search.manage_history' WHERE id = 1236;

-- Fix encoded permission groups
UPDATE permissions SET permission_group = 'Settings' WHERE id = 1232;
UPDATE permissions SET permission_group = 'Reports' WHERE id = 1233;
UPDATE permissions SET permission_group = 'Admin' WHERE id = 1234;
UPDATE permissions SET permission_group = 'Archive' WHERE id = 1235;
UPDATE permissions SET permission_group = 'Search' WHERE id = 1236;

-- Fix encoded categories
UPDATE permissions SET category = 'Settings' WHERE id = 1232;
UPDATE permissions SET category = 'Reports' WHERE id = 1233;
UPDATE permissions SET category = 'Management' WHERE id = 1234;
UPDATE permissions SET category = 'Archive' WHERE id = 1235;
UPDATE permissions SET category = 'Search' WHERE id = 1236;

-- Update modification timestamp for all fixed records
UPDATE permissions 
SET updated_at = UNIX_TIMESTAMP() 
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
);

-- Verify results
SELECT 
    id, 
    name, 
    description, 
    permission_group,
    category
FROM permissions 
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
)
ORDER BY id;

-- End transaction
COMMIT;

-- Success message
SELECT 'All encoded descriptions have been fixed successfully!' as result;
