import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart'; // استخدام Syncfusion Charts بدلاً من fl_chart
import 'package:flutter_application_2/models/advanced_filter_options.dart';
import 'package:flutter_application_2/models/chart_enums.dart';

import '../../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';

import 'unified_filter_export_widget.dart';

/// كلاس لتمثيل نقطة بيانات في المخطط القطبي
class _PolarDataPoint {
  final String category;
  final double value;

  _PolarDataPoint(this.category, this.value);
}

/// مكون مخطط راداري محسن
///
/// يوفر هذا المكون مخططًا رادارياً متقدمًا مع دعم للتصفية والتصدير
class EnhancedRadarChart extends StatefulWidget {
  /// بيانات المخطط
  /// مفتاح الخريطة هو اسم السلسلة، والقيمة هي قائمة من القيم
  final Map<String, List<double>> data;

  /// عناوين المحاور
  final List<String> axisLabels;

  /// عنوان المخطط
  final String? title;

  /// ألوان السلاسل (اختياري)
  final Map<String, Color>? seriesColors;

  /// القيمة القصوى للمحاور (اختياري)
  final double? maxValue;

  /// عدد الدوائر المحيطة
  final int gridCircleCount;

  /// هل يتم عرض قيم البيانات
  final bool showValues;

  /// هل يتم عرض خطوط الشبكة
  final bool showGrid;

  /// هل يتم عرض مفتاح المخطط
  final bool showLegend;

  /// دالة التصفية (اختياري)
  final Function(
          DateTime? startDate, DateTime? endDate, TimeFilterType filterType)?
      onFilterChanged;

  /// دالة التصدير (اختياري)
  final Function(String format)? onExport;

  /// دالة تغيير نوع المخطط (اختياري)
  final Function(ChartType)? onChartTypeChanged;

  /// إظهار خيارات التصفية (اختياري)
  final bool showFilterOptions;

  /// إظهار خيارات التصدير (اختياري)
  final bool showExportOptions;

  /// إظهار خيارات تغيير نوع المخطط (اختياري)
  final bool showChartTypeOptions;

  /// تنسيقات التصدير المدعومة (اختياري)
  final List<String> supportedExportFormats;

  /// أنواع المخططات المدعومة (اختياري)
  final List<ChartType> supportedChartTypes;

  /// نوع المخطط
  final ChartType chartType;

  /// خيارات التصفية المتقدمة
  final AdvancedFilterOptions advancedFilterOptions;

  /// إنشاء مكون مخطط راداري محسن
  const EnhancedRadarChart({
    super.key,
    required this.data,
    required this.axisLabels,
    this.title,
    this.seriesColors,
    this.maxValue,
    this.gridCircleCount = 4,
    this.showValues = true,
    this.showGrid = true,
    this.showLegend = true,
    this.onFilterChanged,
    this.onExport,
    this.onChartTypeChanged,
    this.showFilterOptions = false,
    this.showExportOptions = false,
    this.showChartTypeOptions = false,
    this.supportedExportFormats = const ['pdf', 'excel', 'csv', 'image'],
    this.supportedChartTypes = const [
      ChartType.radar,
      ChartType.bar,
      ChartType.pie,
    ],
    required this.chartType,
    required this.advancedFilterOptions,
  });

  @override
  State<EnhancedRadarChart> createState() => _EnhancedRadarChartState();
}

class _EnhancedRadarChartState extends State<EnhancedRadarChart> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان المخطط وأزرار التصفية والتصدير
        if (widget.title != null ||
            widget.showFilterOptions ||
            widget.showExportOptions)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildHeader(),
          ),

        // المخطط الراداري
        Expanded(
          child: _buildRadarChart(),
        ),

        // مفتاح المخطط
        if (widget.showLegend && widget.data.length > 1)
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: _buildLegend(),
          ),
      ],
    );
  }

  /// بناء رأس المخطط
  Widget _buildHeader() {
    // استخدام مكون UnifiedFilterExportWidget بدلاً من الأكواد المكررة
    return UnifiedFilterExportWidget(
      title: widget.title ?? 'مخطط راداري',
      chartKey: 'radar_chart',
      onFilterChanged: (startDate, endDate, filterType, chartKey) {
        if (widget.onFilterChanged != null) {
          widget.onFilterChanged!(startDate, endDate, filterType);
        }
      },
      onExport: (format, title) {
        if (widget.onExport != null) {
          widget.onExport!(format);
        }
      },
      onChartTypeChanged: widget.onChartTypeChanged != null
          ? (chartType, chartKey) {
              widget.onChartTypeChanged!(chartType);
            }
          : null,
      showFilter: widget.showFilterOptions,
      showExport: widget.showExportOptions,
      showChartTypeSelector: widget.showChartTypeOptions,
      supportedChartTypes: widget.supportedChartTypes,
      filterType: TimeFilterType.month,
      startDate: DateTime.now().subtract(const Duration(days: 30)),
      endDate: DateTime.now(),
      currentChartType: ChartType.radar,
      chartType: widget.chartType,
      advancedFilterOptions: widget.advancedFilterOptions,
    );
  }

  // تم تعليق هذه الدوال لأنها لم تعد مستخدمة بعد استخدام UnifiedFilterExportWidget

  /*
  /// بناء أزرار تغيير نوع المخطط
  Widget _buildChartTypeButtons() {
    // استخدام أزرار مباشرة بدلاً من مكون ChartTypeSelector لتجنب التكرار
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: widget.supportedChartTypes.map((type) {
          final isSelected = type == ChartType.radar;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2.0),
            child: ChoiceChip(
              label: Icon(
                ChartTypeUtils.getChartTypeIcon(type),
                size: 20,
                color: isSelected ? Colors.white : Colors.grey.shade700,
              ),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  widget.onChartTypeChanged!(type);
                }
              },
              backgroundColor: Colors.grey.shade200,
              selectedColor: AppColors.primary,
              tooltip: ChartTypeUtils.getChartTypeLabel(type),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// عرض مربع حوار التصفية
  void _showFilterDialog() {
    // يمكن هنا عرض مربع حوار للتصفية
  }
  */

  /// بناء المخطط الراداري باستخدام Syncfusion Polar Chart
  Widget _buildRadarChart() {
    // التحقق من وجود بيانات
    if (widget.data.isEmpty) {
      return UnifiedFilterExportWidget.buildNoDataMessage(
        context,
        message: 'لا توجد بيانات للعرض.\nقد يكون ذلك بسبب الفلتر المطبق.',
        onCancelFilter: () {
          // إلغاء الفلتر وإعادة تعيينه إلى الكل
          if (widget.onFilterChanged != null) {
            widget.onFilterChanged!(null, null, TimeFilterType.all);
          }
        },
      );
    }

    // تحديد القيمة القصوى
    double maxVal = widget.maxValue ?? 0;
    if (maxVal == 0) {
      for (final series in widget.data.values) {
        for (final value in series) {
          if (value > maxVal) maxVal = value;
        }
      }
      // إضافة هامش للقيمة القصوى
      maxVal *= 1.2;
    }

    // إنشاء ألوان مختلفة لكل سلسلة بيانات
    final defaultColors = [
      AppColors.primary,
      AppColors.statusCompleted,
      AppColors.statusInProgress,
      AppColors.statusPending,
      AppColors.priorityHigh,
      AppColors.priorityMedium,
      AppColors.priorityLow,
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.shade400, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: SfCartesianChart(
        // تفعيل الميزات التفاعلية المتقدمة من Syncfusion
        enableAxisAnimation: true,
        enableSideBySideSeriesPlacement: true,

        // تفعيل Tooltip المتقدم
        tooltipBehavior: TooltipBehavior(
          enable: true,
          format: 'point.x: point.y',
          header: '',
          canShowMarker: true,
          activationMode: ActivationMode.singleTap,
          animationDuration: 500,
          borderColor: Colors.blue,
          borderWidth: 2,
        ),

        // إعدادات المحاور
        primaryXAxis: CategoryAxis(
          title: AxisTitle(text: 'المحاور'),
          majorGridLines: const MajorGridLines(width: 1),
          labelStyle: const TextStyle(fontSize: 12),
        ),

        primaryYAxis: NumericAxis(
          title: AxisTitle(text: 'القيم'),
          maximum: maxVal,
          majorGridLines: const MajorGridLines(width: 1),
          labelStyle: const TextStyle(fontSize: 12),
        ),

        // إعدادات المفتاح
        legend: widget.showLegend ? Legend(
          isVisible: true,
          position: LegendPosition.bottom,
          overflowMode: LegendItemOverflowMode.wrap,
          textStyle: const TextStyle(fontSize: 12),
          itemPadding: 8,
        ) : Legend(isVisible: false),

        // إعدادات السلاسل
        series: _buildCartesianSeries(defaultColors),
      ),
    );
  }

  /// إنشاء سلاسل Syncfusion Cartesian Chart للمخطط الراداري
  List<CartesianSeries> _buildCartesianSeries(List<Color> defaultColors) {
    final List<CartesianSeries> series = [];
    final seriesNames = widget.data.keys.toList();

    // إنشاء سلسلة لكل مجموعة بيانات
    for (int i = 0; i < seriesNames.length; i++) {
      final seriesName = seriesNames[i];
      final seriesData = widget.data[seriesName]!;
      final color = widget.seriesColors?[seriesName] ??
          defaultColors[i % defaultColors.length];

      // تحويل البيانات إلى نقاط للمخطط
      final List<_PolarDataPoint> chartData = [];
      for (int j = 0; j < widget.axisLabels.length && j < seriesData.length; j++) {
        chartData.add(_PolarDataPoint(widget.axisLabels[j], seriesData[j]));
      }

      // إنشاء سلسلة خطية لمحاكاة المخطط الراداري
      series.add(
        LineSeries<_PolarDataPoint, String>(
          name: seriesName,
          dataSource: chartData,
          xValueMapper: (_PolarDataPoint data, _) => data.category,
          yValueMapper: (_PolarDataPoint data, _) => data.value,
          color: color,
          width: 2,
          markerSettings: MarkerSettings(
            isVisible: true,
            height: 6,
            width: 6,
            color: color,
            borderColor: Colors.white,
            borderWidth: 1,
          ),
          dataLabelSettings: DataLabelSettings(
            isVisible: widget.showValues,
            labelPosition: ChartDataLabelPosition.outside,
            textStyle: const TextStyle(fontSize: 10),
          ),
          enableTooltip: true,
          animationDuration: 1000,
          selectionBehavior: SelectionBehavior(
            enable: true,
            selectedColor: color.withAlpha(128),
            unselectedColor: color.withAlpha(76),
          ),
        ),
      );
    }

    return series;
  }

  /// بناء مفتاح المخطط
  Widget _buildLegend() {
    final seriesNames = widget.data.keys.toList();
    final defaultColors = [
      AppColors.primary,
      AppColors.statusCompleted,
      AppColors.statusInProgress,
      AppColors.statusPending,
      AppColors.priorityHigh,
      AppColors.priorityMedium,
      AppColors.priorityLow,
    ];

    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 16,
      runSpacing: 8,
      children: seriesNames.asMap().entries.map((entry) {
        final index = entry.key;
        final seriesName = entry.value;
        final color = widget.seriesColors?[seriesName] ??
            defaultColors[index % defaultColors.length];

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              seriesName,
              style: AppStyles.caption,
            ),
          ],
        );
      }).toList(),
    );
  }
}
