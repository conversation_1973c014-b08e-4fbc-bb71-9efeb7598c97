-- سكريبت تحليل مشاكل الترميز في جدول الصلاحيات
-- تاريخ الإنشاء: 2025-01-06
-- الهدف: تحليل وتحديد السجلات التي تعاني من مشاكل الترميز

-- 1. البحث عن السجلات التي تحتوي على أحرف مرمزة خاطئة
SELECT 
    'السجلات المرمزة خطأ - المجموعة الأولى' as category,
    COUNT(*) as count
FROM permissions 
WHERE description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%Ø§%';

-- 2. عرض السجلات المرمزة خطأ مع تفاصيلها
SELECT 
    id,
    name,
    description,
    permission_group,
    'مرمز خطأ' as status
FROM permissions 
WHERE description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%Ø§%'
ORDER BY id;

-- 3. البحث عن السجلات التي تحتوي على علامات استفهام
SELECT 
    'السجلات بعلامات استفهام' as category,
    COUNT(*) as count
FROM permissions 
WHERE description LIKE '%?%';

-- 4. عرض السجلات بعلامات الاستفهام
SELECT 
    id,
    name,
    description,
    permission_group,
    'علامات استفهام' as status
FROM permissions 
WHERE description LIKE '%?%'
ORDER BY id;

-- 5. إحصائيات شاملة لحالة الترميز
SELECT 
    CASE 
        WHEN description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%Ø§%' THEN 'مرمز خطأ'
        WHEN description LIKE '%?%' THEN 'علامات استفهام'
        WHEN description REGEXP '[ا-ي]' THEN 'عربي صحيح'
        WHEN description REGEXP '[a-zA-Z]' THEN 'إنجليزي'
        ELSE 'غير محدد'
    END as encoding_status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM permissions), 2) as percentage
FROM permissions 
GROUP BY encoding_status
ORDER BY count DESC;

-- 6. تحليل توزيع المشاكل حسب المجموعات
SELECT 
    permission_group,
    COUNT(*) as total_permissions,
    SUM(CASE WHEN description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%Ø§%' THEN 1 ELSE 0 END) as encoded_wrong,
    SUM(CASE WHEN description LIKE '%?%' THEN 1 ELSE 0 END) as question_marks,
    SUM(CASE WHEN description REGEXP '[ا-ي]' THEN 1 ELSE 0 END) as arabic_correct
FROM permissions 
GROUP BY permission_group
ORDER BY encoded_wrong DESC, question_marks DESC;

-- 7. تحليل النطاقات المتأثرة بالترميز الخاطئ
SELECT 
    'نطاق المعرفات المتأثرة' as analysis,
    MIN(id) as min_id,
    MAX(id) as max_id,
    COUNT(*) as affected_count
FROM permissions 
WHERE description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%Ø§%'
UNION ALL
SELECT 
    'نطاق علامات الاستفهام' as analysis,
    MIN(id) as min_id,
    MAX(id) as max_id,
    COUNT(*) as affected_count
FROM permissions 
WHERE description LIKE '%?%';

-- 8. قائمة بجميع السجلات التي تحتاج إصلاح
SELECT 
    id,
    name,
    description,
    permission_group,
    created_at,
    CASE 
        WHEN description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%Ø§%' THEN 'ترميز خاطئ'
        WHEN description LIKE '%?%' THEN 'علامات استفهام'
        ELSE 'أخرى'
    END as problem_type
FROM permissions 
WHERE description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%Ø§%' OR description LIKE '%?%'
ORDER BY id;

-- 9. تحليل التواريخ لفهم متى حدثت مشاكل الترميز
SELECT 
    FROM_UNIXTIME(created_at, '%Y-%m-%d') as creation_date,
    COUNT(*) as total_created,
    SUM(CASE WHEN description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%Ø§%' THEN 1 ELSE 0 END) as encoded_wrong,
    SUM(CASE WHEN description LIKE '%?%' THEN 1 ELSE 0 END) as question_marks
FROM permissions 
GROUP BY FROM_UNIXTIME(created_at, '%Y-%m-%d')
ORDER BY creation_date;

-- 10. تقرير نهائي بالإحصائيات
SELECT 
    'تقرير شامل لمشاكل الترميز' as report_title,
    (SELECT COUNT(*) FROM permissions) as total_permissions,
    (SELECT COUNT(*) FROM permissions WHERE description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%Ø§%') as wrong_encoding,
    (SELECT COUNT(*) FROM permissions WHERE description LIKE '%?%') as question_marks,
    (SELECT COUNT(*) FROM permissions WHERE description REGEXP '[ا-ي]') as correct_arabic,
    (SELECT COUNT(*) FROM permissions WHERE description LIKE '%Ø%' OR description LIKE '%Ù%' OR description LIKE '%Ø§%' OR description LIKE '%?%') as needs_fixing;
