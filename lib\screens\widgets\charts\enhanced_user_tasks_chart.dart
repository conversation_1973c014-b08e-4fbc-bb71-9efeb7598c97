import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/advanced_filter_options.dart';
import 'package:flutter_application_2/models/chart_enums.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'dart:math' as math;

import '../../../constants/app_colors.dart';
import '../../../constants/app_styles.dart';

import 'unified_filter_export_widget.dart';

/// نموذج بيانات المخطط
class ChartData {
  /// المحور السيني
  final String x;

  /// المحور الصادي
  final double y;

  /// الفئة
  final String category;

  /// إنشاء نموذج بيانات المخطط
  const ChartData(this.x, this.y, this.category);
}

/// نموذج بيانات توزيع المهام حسب المستخدمين
class UserTasksData {
  /// اسم المستخدم
  final String userName;

  /// عدد المهام المكتملة
  final int completedTasks;

  /// عدد المهام قيد التنفيذ
  final int inProgressTasks;

  /// عدد المهام المعلقة
  final int pendingTasks;

  /// إجمالي عدد المهام
  final int totalTasks;

  /// لون البيانات (اختياري)
  final Color? color;

  /// إنشاء نموذج بيانات توزيع المهام حسب المستخدمين
  const UserTasksData({
    required this.userName,
    required this.completedTasks,
    required this.inProgressTasks,
    required this.pendingTasks,
    required this.totalTasks,
    this.color,
  });
}

/// مكون مخطط توزيع المهام حسب المستخدمين المحسن
///
/// يوفر هذا المكون مخططًا لتوزيع المهام حسب المستخدمين مع دعم للتصفية والتصدير
class EnhancedUserTasksChart extends StatefulWidget {
  /// بيانات المخطط
  final List<UserTasksData> data;

  /// عنوان المخطط
  final String? title;

  /// ألوان المخطط (اختياري)
  final List<Color>? colors;

  /// دالة التصفية (اختياري)
  final Function(
          DateTime? startDate, DateTime? endDate, TimeFilterType filterType)?
      onFilterChanged;

  /// دالة التصدير (اختياري)
  final Function(String format)? onExport;

  /// دالة تغيير نوع المخطط (اختياري)
  final Function(ChartType)? onChartTypeChanged;

  /// إظهار خيارات التصفية (اختياري)
  final bool showFilterOptions;

  /// إظهار خيارات التصدير (اختياري)
  final bool showExportOptions;

  /// إظهار خيارات تغيير نوع المخطط (اختياري)
  final bool showChartTypeOptions;

  /// نوع المخطط الحالي
  final ChartType currentChartType;

  /// أنواع المخططات المدعومة (اختياري)
  final List<ChartType> supportedChartTypes;

  /// خيارات التصفية المتقدمة
  final AdvancedFilterOptions advancedFilterOptions;

  /// إنشاء مكون مخطط توزيع المهام حسب المستخدمين المحسن
  const EnhancedUserTasksChart({
    super.key,
    required this.data,
    this.title,
    this.colors,
    this.onFilterChanged,
    this.onExport,
    this.onChartTypeChanged,
    this.showFilterOptions = true,
    this.showExportOptions = true,
    this.showChartTypeOptions = true,
    this.currentChartType = ChartType.bar,
    this.supportedChartTypes = const [
      ChartType.bar,
      ChartType.pie,
      ChartType.treemap,
    ],
    required this.advancedFilterOptions,
  });

  @override
  State<EnhancedUserTasksChart> createState() => _EnhancedUserTasksChartState();
}

class _EnhancedUserTasksChartState extends State<EnhancedUserTasksChart> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان المخطط وأزرار التصفية والتصدير
        if (widget.title != null ||
            widget.showFilterOptions ||
            widget.showExportOptions)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildHeader(),
          ),

        // المخطط
        Expanded(
          child: _buildChart(),
        ),

        // مفتاح المخطط
        Padding(
          padding: const EdgeInsets.only(top: 16),
          child: _buildLegend(),
        ),
      ],
    );
  }

  /// بناء رأس المخطط
  Widget _buildHeader() {
    return UnifiedFilterExportWidget(
      title: widget.title ?? 'توزيع المهام حسب المستخدمين',
      chartKey: 'user_tasks_chart',
      onFilterChanged: (startDate, endDate, filterType, chartKey) {
        if (widget.onFilterChanged != null) {
          widget.onFilterChanged!(startDate, endDate, filterType);
        }
      },
      onExport: (format, title) {
        if (widget.onExport != null) {
          widget.onExport!(format);
        }
      },
      onChartTypeChanged: widget.onChartTypeChanged != null
          ? (chartType, chartKey) {
              widget.onChartTypeChanged!(chartType);
            }
          : null,
      showFilter: widget.showFilterOptions,
      showExport: widget.showExportOptions,
      showChartTypeSelector: widget.showChartTypeOptions,
      supportedChartTypes: widget.supportedChartTypes,
      filterType: TimeFilterType.month,
      startDate: DateTime.now().subtract(const Duration(days: 30)),
      endDate: DateTime.now(),
      currentChartType: widget.currentChartType,
      chartIcon: Icons.people_alt,
      chartType: widget.currentChartType,
      advancedFilterOptions: widget.advancedFilterOptions,
    );
  }

  /// بناء المخطط
  Widget _buildChart() {
    if (widget.data.isEmpty) {
      return UnifiedFilterExportWidget.buildNoDataMessage(
        context,
        message: 'لا توجد بيانات للعرض.\nقد يكون ذلك بسبب الفلتر المطبق.',
        onCancelFilter: () {
          // إلغاء الفلتر وإعادة تعيينه إلى الكل
          if (widget.onFilterChanged != null) {
            widget.onFilterChanged!(null, null, TimeFilterType.all);
          }
        },
      );
    }

    switch (widget.currentChartType) {
      case ChartType.bar:
        return _buildBarChart();
      case ChartType.pie:
        return _buildPieChart();
      case ChartType.treemap:
        return _buildTreemapChart();
      default:
        return _buildBarChart();
    }
  }

  /// بناء مخطط شريطي
  Widget _buildBarChart() {
    // إعداد البيانات للمخطط
    final List<ChartData> chartData = [];

    for (final data in widget.data) {
      chartData.add(ChartData(data.userName, data.completedTasks.toDouble(), 'مكتملة'));
      chartData.add(ChartData(data.userName, data.inProgressTasks.toDouble(), 'قيد التنفيذ'));
      chartData.add(ChartData(data.userName, data.pendingTasks.toDouble(), 'معلقة'));
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.shade400, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: SfCartesianChart(
        // تفعيل الميزات التفاعلية المتقدمة من Syncfusion
        enableAxisAnimation: true,
        enableSideBySideSeriesPlacement: true,

        // تفعيل Tooltip المتقدم
        tooltipBehavior: TooltipBehavior(
          enable: true,
          format: 'point.x: point.y',
          header: '',
          canShowMarker: true,
          activationMode: ActivationMode.singleTap,
          animationDuration: 500,
          borderColor: Colors.blue,
          borderWidth: 2,
        ),

        // إعدادات المحاور
        primaryXAxis: CategoryAxis(
          title: AxisTitle(text: 'المستخدمين'),
          majorGridLines: const MajorGridLines(width: 0),
          labelStyle: const TextStyle(fontSize: 12),
        ),

        primaryYAxis: NumericAxis(
          title: AxisTitle(text: 'عدد المهام'),
          majorGridLines: const MajorGridLines(width: 1),
          labelStyle: const TextStyle(fontSize: 12),
        ),

        // إعدادات المفتاح
        legend: Legend(
          isVisible: true,
          position: LegendPosition.bottom,
          overflowMode: LegendItemOverflowMode.wrap,
          textStyle: const TextStyle(fontSize: 12),
          itemPadding: 8,
        ),

        // إعدادات السلاسل
        series: <CartesianSeries>[
          ColumnSeries<ChartData, String>(
            name: 'مكتملة',
            dataSource: chartData.where((data) => data.category == 'مكتملة').toList(),
            xValueMapper: (ChartData data, _) => data.x,
            yValueMapper: (ChartData data, _) => data.y,
            color: AppColors.statusCompleted,
            dataLabelSettings: const DataLabelSettings(isVisible: true),
            enableTooltip: true,
            animationDuration: 1000,
          ),
          ColumnSeries<ChartData, String>(
            name: 'قيد التنفيذ',
            dataSource: chartData.where((data) => data.category == 'قيد التنفيذ').toList(),
            xValueMapper: (ChartData data, _) => data.x,
            yValueMapper: (ChartData data, _) => data.y,
            color: AppColors.statusInProgress,
            dataLabelSettings: const DataLabelSettings(isVisible: true),
            enableTooltip: true,
            animationDuration: 1000,
          ),
          ColumnSeries<ChartData, String>(
            name: 'معلقة',
            dataSource: chartData.where((data) => data.category == 'معلقة').toList(),
            xValueMapper: (ChartData data, _) => data.x,
            yValueMapper: (ChartData data, _) => data.y,
            color: AppColors.statusPending,
            dataLabelSettings: const DataLabelSettings(isVisible: true),
            enableTooltip: true,
            animationDuration: 1000,
          ),
        ],
      ),
    );
  }

  /// بناء مخطط دائري
  Widget _buildPieChart() {
    // تجميع البيانات حسب المستخدمين
    final List<ChartData> pieData = [];
    for (final data in widget.data) {
      pieData.add(ChartData(data.userName, data.totalTasks.toDouble(), 'total'));
    }

    final defaultColors = [
      AppColors.primary,
      AppColors.statusCompleted,
      AppColors.statusInProgress,
      AppColors.statusPending,
      AppColors.priorityHigh,
      AppColors.priorityMedium,
      AppColors.priorityLow,
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.shade400, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: SfCircularChart(
        // تفعيل الميزات التفاعلية المتقدمة من Syncfusion
        enableMultiSelection: true,
        selectionGesture: ActivationMode.singleTap,

        // تفعيل Tooltip المتقدم
        tooltipBehavior: TooltipBehavior(
          enable: true,
          format: 'point.x: point.y',
          header: '',
          canShowMarker: true,
          activationMode: ActivationMode.singleTap,
          animationDuration: 500,
          borderColor: Colors.blue,
          borderWidth: 2,
        ),

        // إعدادات المفتاح
        legend: Legend(
          isVisible: true,
          position: LegendPosition.bottom,
          overflowMode: LegendItemOverflowMode.wrap,
          textStyle: const TextStyle(fontSize: 12),
          itemPadding: 8,
        ),

        // إعدادات السلاسل
        series: <CircularSeries>[
          PieSeries<ChartData, String>(
            dataSource: pieData,
            xValueMapper: (ChartData data, _) => data.x,
            yValueMapper: (ChartData data, _) => data.y,
            pointColorMapper: (ChartData data, int index) =>
                widget.data[index].color ??
                widget.colors?[index % (widget.colors?.length ?? 1)] ??
                defaultColors[index % defaultColors.length],
            dataLabelSettings: const DataLabelSettings(
              isVisible: true,
              labelPosition: ChartDataLabelPosition.outside,
            ),
            enableTooltip: true,
            animationDuration: 1000,
            selectionBehavior: SelectionBehavior(
              enable: true,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مخطط شجري
  Widget _buildTreemapChart() {
    // تجميع البيانات حسب المستخدمين
    final Map<String, double> treemapData = {};
    for (final data in widget.data) {
      treemapData[data.userName] = data.totalTasks.toDouble();
    }

    // إنشاء ألوان للمستخدمين
    final Map<String, Color> userColors = {};
    final defaultColors = [
      AppColors.primary,
      AppColors.statusCompleted,
      AppColors.statusInProgress,
      AppColors.statusPending,
      AppColors.priorityHigh,
      AppColors.priorityMedium,
      AppColors.priorityLow,
    ];

    // تعيين لون لكل مستخدم
    for (int i = 0; i < widget.data.length; i++) {
      final data = widget.data[i];
      userColors[data.userName] = data.color ??
          widget.colors?[i % (widget.colors?.length ?? 1)] ??
          defaultColors[i % defaultColors.length];
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return _TreemapLayout(
          data: treemapData,
          colors: userColors,
          showValues: true,
          showPercentages: true,
          width: constraints.maxWidth,
          height: constraints.maxHeight,
        );
      },
    );
  }

  /// بناء مفتاح المخطط
  Widget _buildLegend() {
    // إذا كان المخطط الحالي هو مخطط شجري، نعرض مفتاح المخطط للمستخدمين
    if (widget.currentChartType == ChartType.treemap) {
      // إنشاء مفتاح المخطط للمستخدمين
      return Wrap(
        alignment: WrapAlignment.center,
        spacing: 16,
        runSpacing: 8,
        children: widget.data.asMap().entries.map((entry) {
          final index = entry.key;
          final data = entry.value;
          final color = data.color ??
              widget.colors?[index % (widget.colors?.length ?? 1)] ??
              AppColors.primary;

          return _buildLegendItem(
            data.userName,
            color,
          );
        }).toList(),
      );
    }

    // للمخططات الأخرى، نعرض مفتاح المخطط لحالات المهام
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildLegendItem(
          'مكتملة',
          AppColors.statusCompleted,
        ),
        const SizedBox(width: 16),
        _buildLegendItem(
          'قيد التنفيذ',
          AppColors.statusInProgress,
        ),
        const SizedBox(width: 16),
        _buildLegendItem(
          'معلقة',
          AppColors.statusPending,
        ),
      ],
    );
  }

  /// بناء عنصر مفتاح المخطط
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: AppStyles.caption,
        ),
      ],
    );
  }
}



/// مكون تخطيط الخريطة الشجرية
class _TreemapLayout extends StatelessWidget {
  final Map<String, double> data;
  final Map<String, Color> colors;
  final bool showValues;
  final bool showPercentages;
  final double width;
  final double height;

  const _TreemapLayout({
    required this.data,
    required this.colors,
    required this.showValues,
    required this.showPercentages,
    required this.width,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(width, height),
      painter: _TreemapPainter(
        data: data,
        colors: colors,
        showValues: showValues,
        showPercentages: showPercentages,
      ),
    );
  }
}

/// نموذج عنصر الخريطة الشجرية
class _TreemapItem {
  /// اسم العنصر
  final String label;

  /// قيمة العنصر
  final double value;

  /// لون العنصر
  final Color color;

  /// إنشاء عنصر خريطة شجرية
  const _TreemapItem({
    required this.label,
    required this.value,
    required this.color,
  });
}

/// رسام الخريطة الشجرية
class _TreemapPainter extends CustomPainter {
  final Map<String, double> data;
  final Map<String, Color> colors;
  final bool showValues;
  final bool showPercentages;

  _TreemapPainter({
    required this.data,
    required this.colors,
    required this.showValues,
    required this.showPercentages,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    // حساب إجمالي القيم
    final double totalValue = data.values.fold(0, (sum, value) => sum + value);

    // تحويل البيانات إلى قائمة مرتبة تنازليًا
    final items = data.entries.map((entry) {
      return _TreemapItem(
        label: entry.key,
        value: entry.value,
        color: colors[entry.key] ?? Colors.blue,
      );
    }).toList();

    // ترتيب العناصر تنازليًا حسب القيمة
    items.sort((a, b) => b.value.compareTo(a.value));

    // رسم الخريطة الشجرية
    _drawTreemap(canvas, Rect.fromLTWH(0, 0, size.width, size.height), items,
        totalValue);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;

  /// رسم الخريطة الشجرية باستخدام خوارزمية "squarified treemap"
  void _drawTreemap(
      Canvas canvas, Rect rect, List<_TreemapItem> items, double totalValue) {
    if (items.isEmpty || rect.isEmpty) return;

    // تنفيذ خوارزمية "squarified treemap" لتحسين نسب العرض إلى الارتفاع
    _squarifiedTreemap(canvas, rect, items, totalValue);
  }

  /// تنفيذ خوارزمية "squarified treemap"
  void _squarifiedTreemap(
      Canvas canvas, Rect rect, List<_TreemapItem> items, double totalValue) {
    if (items.isEmpty || rect.isEmpty) return;

    // حساب إجمالي القيم للعناصر المتبقية
    final remainingTotal = items.fold(0.0, (sum, item) => sum + item.value);

    // تقسيم العناصر إلى مجموعات لتحسين نسب العرض إلى الارتفاع
    final isHorizontal = rect.width > rect.height;

    // تحديد عدد العناصر في المجموعة الحالية
    int bestCount = 1;
    double bestRatio = double.infinity;

    // البحث عن أفضل عدد من العناصر للمجموعة الحالية
    for (int i = 1; i <= items.length; i++) {
      final subItems = items.sublist(0, i);
      final subTotal = subItems.fold(0.0, (sum, item) => sum + item.value);
      final ratio = _calculateWorstRatio(
          rect, subItems, subTotal / remainingTotal, isHorizontal);

      if (ratio < bestRatio) {
        bestRatio = ratio;
        bestCount = i;
      } else {
        // إذا بدأت النسبة في التدهور، نتوقف
        break;
      }
    }

    // تقسيم العناصر إلى مجموعتين
    final currentItems = items.sublist(0, bestCount);
    final nextItems = items.sublist(bestCount);

    // حساب إجمالي القيم للمجموعة الحالية
    final currentTotal =
        currentItems.fold(0.0, (sum, item) => sum + item.value);
    final currentRatio = currentTotal / remainingTotal;

    // رسم المجموعة الحالية
    Rect currentRect, nextRect;

    if (isHorizontal) {
      // تقسيم أفقي
      final currentWidth = rect.width * currentRatio;
      currentRect =
          Rect.fromLTWH(rect.left, rect.top, currentWidth, rect.height);
      nextRect = Rect.fromLTWH(rect.left + currentWidth, rect.top,
          rect.width - currentWidth, rect.height);
    } else {
      // تقسيم رأسي
      final currentHeight = rect.height * currentRatio;
      currentRect =
          Rect.fromLTWH(rect.left, rect.top, rect.width, currentHeight);
      nextRect = Rect.fromLTWH(rect.left, rect.top + currentHeight, rect.width,
          rect.height - currentHeight);
    }

    // رسم المجموعة الحالية
    _layoutRow(canvas, currentRect, currentItems, currentTotal, isHorizontal,
        totalValue);

    // رسم المجموعة التالية
    if (nextItems.isNotEmpty) {
      _squarifiedTreemap(canvas, nextRect, nextItems, totalValue);
    }
  }

  /// حساب أسوأ نسبة عرض إلى ارتفاع في المجموعة
  double _calculateWorstRatio(
      Rect rect, List<_TreemapItem> items, double ratio, bool isHorizontal) {
    if (items.isEmpty) return double.infinity;

    double worstRatio = 0;

    if (isHorizontal) {
      // تقسيم أفقي
      final width = rect.width * ratio;
      final height = rect.height;

      for (final item in items) {
        final itemRatio =
            item.value / items.fold(0.0, (sum, i) => sum + i.value);
        final itemHeight = height * itemRatio;
        final aspectRatio = width / itemHeight;

        worstRatio = math.max(
            worstRatio, aspectRatio > 1 ? aspectRatio : 1 / aspectRatio);
      }
    } else {
      // تقسيم رأسي
      final width = rect.width;
      final height = rect.height * ratio;

      for (final item in items) {
        final itemRatio =
            item.value / items.fold(0.0, (sum, i) => sum + i.value);
        final itemWidth = width * itemRatio;
        final aspectRatio = itemWidth / height;

        worstRatio = math.max(
            worstRatio, aspectRatio > 1 ? aspectRatio : 1 / aspectRatio);
      }
    }

    return worstRatio;
  }

  /// تخطيط صف من العناصر
  void _layoutRow(Canvas canvas, Rect rect, List<_TreemapItem> items,
      double total, bool isHorizontal, double grandTotal) {
    if (items.isEmpty || rect.isEmpty) return;

    double offset = 0;

    if (isHorizontal) {
      // تخطيط أفقي
      for (final item in items) {
        final ratio = item.value / total;
        final itemHeight = rect.height * ratio;
        final itemRect =
            Rect.fromLTWH(rect.left, rect.top + offset, rect.width, itemHeight);

        _drawItem(canvas, itemRect, item, grandTotal);
        offset += itemHeight;
      }
    } else {
      // تخطيط رأسي
      for (final item in items) {
        final ratio = item.value / total;
        final itemWidth = rect.width * ratio;
        final itemRect =
            Rect.fromLTWH(rect.left + offset, rect.top, itemWidth, rect.height);

        _drawItem(canvas, itemRect, item, grandTotal);
        offset += itemWidth;
      }
    }
  }

  /// رسم عنصر واحد
  void _drawItem(
      Canvas canvas, Rect rect, _TreemapItem item, double totalValue) {
    // رسم المستطيل
    final paint = Paint()
      ..color = item.color
      ..style = PaintingStyle.fill;

    canvas.drawRect(rect, paint);

    // رسم الحدود
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    canvas.drawRect(rect, borderPaint);

    // رسم النص إذا كان المستطيل كبيرًا بما يكفي
    if (rect.width > 40 && rect.height > 30 && showValues) {
      // رسم اسم العنصر
      final textPainter = TextPainter(
        text: TextSpan(
          text: item.label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.rtl,
      );

      textPainter.layout(maxWidth: rect.width - 8);

      // حساب موقع النص
      final textX = rect.left + 4;
      final textY = rect.top + 4;

      // رسم النص
      textPainter.paint(canvas, Offset(textX, textY));

      // رسم النسبة المئوية إذا كان مطلوبًا
      if (showPercentages) {
        final percentage = (item.value / totalValue * 100).toStringAsFixed(1);
        final percentageText = TextPainter(
          text: TextSpan(
            text: '$percentage%',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
            ),
          ),
          textDirection: TextDirection.rtl,
        );

        percentageText.layout(maxWidth: rect.width - 8);

        // حساب موقع النص
        final percentageX = rect.left + 4;
        final percentageY = textY + textPainter.height + 2;

        // رسم النص
        percentageText.paint(canvas, Offset(percentageX, percentageY));
      }
    }
  }
}
