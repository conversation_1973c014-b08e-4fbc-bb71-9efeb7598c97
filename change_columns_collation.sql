-- تغيير ترميز أعمدة جدول الصلاحيات لدعم العربية
-- الحل الثاني: تغيير ترميز الأعمدة فقط

USE databasetasks;
GO

-- إنشاء نسخة احتياطية من الجدول
SELECT * INTO permissions_backup_collation FROM permissions;

-- تغيير ترميز الأعمدة النصية لدعم العربية
ALTER TABLE permissions 
ALTER COLUMN name NVARCHAR(100) COLLATE Arabic_CI_AS;

ALTER TABLE permissions 
ALTER COLUMN description NVARCHAR(MAX) COLLATE Arabic_CI_AS;

ALTER TABLE permissions 
ALTER COLUMN permission_group NVARCHAR(50) COLLATE Arabic_CI_AS;

ALTER TABLE permissions 
ALTER COLUMN category NVARCHAR(100) COLLATE Arabic_CI_AS;

-- التحقق من التغيير
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    COLLATION_NAME
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'permissions' 
AND COLUMN_NAME IN ('name', 'description', 'permission_group', 'category');

PRINT N'تم تغيير ترميز أعمدة الجدول بنجاح!';
GO
