-- ===================================================================
-- إضافة صلاحيات إدارة مديري الأقسام
-- تاريخ الإنشاء: 2025-01-12
-- الهدف: إضافة الصلاحيات المطلوبة لتعيين وإدارة مديري الأقسام
-- ===================================================================

USE [databasetasks]
GO

PRINT '🚀 بدء إضافة صلاحيات إدارة مديري الأقسام...'

DECLARE @CurrentTime BIGINT = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())

-- ===== صلاحيات إدارة مديري الأقسام =====
PRINT '📍 إضافة صلاحيات إدارة مديري الأقسام...'

-- تعيين مدير قسم
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'departments.assign_manager')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, is_active, created_at, updated_at)
    VALUES ('departments.assign_manager', 'تعيين مدير للقسم', 'departments', 'إدارة الأقسام', 3, 'admin_panel_settings', '#FF5722', 0, 1, @CurrentTime, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية: departments.assign_manager'
END
ELSE
    PRINT '⚠️ صلاحية departments.assign_manager موجودة مسبقاً'

-- إزالة مدير قسم
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'departments.remove_manager')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, is_active, created_at, updated_at)
    VALUES ('departments.remove_manager', 'إزالة مدير القسم', 'departments', 'إدارة الأقسام', 3, 'person_remove', '#F44336', 0, 1, @CurrentTime, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية: departments.remove_manager'
END
ELSE
    PRINT '⚠️ صلاحية departments.remove_manager موجودة مسبقاً'

-- إدارة مستخدمي القسم
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'departments.manage_users')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, is_active, created_at, updated_at)
    VALUES ('departments.manage_users', 'إدارة مستخدمي القسم', 'departments', 'إدارة الأقسام', 2, 'group', '#2196F3', 0, 1, @CurrentTime, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية: departments.manage_users'
END
ELSE
    PRINT '⚠️ صلاحية departments.manage_users موجودة مسبقاً'

-- إضافة مستخدمين للقسم
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'departments.add_users')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, is_active, created_at, updated_at)
    VALUES ('departments.add_users', 'إضافة مستخدمين للقسم', 'departments', 'إدارة الأقسام', 2, 'person_add', '#4CAF50', 0, 1, @CurrentTime, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية: departments.add_users'
END
ELSE
    PRINT '⚠️ صلاحية departments.add_users موجودة مسبقاً'

-- إزالة مستخدمين من القسم
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'departments.remove_users')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, is_active, created_at, updated_at)
    VALUES ('departments.remove_users', 'إزالة مستخدمين من القسم', 'departments', 'إدارة الأقسام', 2, 'person_remove', '#FF9800', 0, 1, @CurrentTime, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية: departments.remove_users'
END
ELSE
    PRINT '⚠️ صلاحية departments.remove_users موجودة مسبقاً'

-- ===== تعيين الصلاحيات للأدوار الافتراضية =====
PRINT '📍 تعيين صلاحيات إدارة الأقسام للأدوار...'

-- الحصول على معرفات الأدوار
DECLARE @AdminRoleId INT = (SELECT id FROM roles WHERE name = 'Admin' OR name = 'admin')
DECLARE @SuperAdminRoleId INT = (SELECT id FROM roles WHERE name = 'SuperAdmin' OR name = 'super_admin')
DECLARE @ManagerRoleId INT = (SELECT id FROM roles WHERE name = 'Manager' OR name = 'manager')

-- الحصول على معرفات الصلاحيات
DECLARE @AssignManagerPermId INT = (SELECT id FROM permissions WHERE name = 'departments.assign_manager')
DECLARE @RemoveManagerPermId INT = (SELECT id FROM permissions WHERE name = 'departments.remove_manager')
DECLARE @ManageUsersPermId INT = (SELECT id FROM permissions WHERE name = 'departments.manage_users')
DECLARE @AddUsersPermId INT = (SELECT id FROM permissions WHERE name = 'departments.add_users')
DECLARE @RemoveUsersPermId INT = (SELECT id FROM permissions WHERE name = 'departments.remove_users')

-- تعيين صلاحيات للمدير العام (SuperAdmin)
IF @SuperAdminRoleId IS NOT NULL
BEGIN
    -- تعيين مدير قسم
    IF @AssignManagerPermId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM role_default_permissions WHERE role_id = @SuperAdminRoleId AND permission_id = @AssignManagerPermId)
    BEGIN
        INSERT INTO role_default_permissions (role_id, permission_id, granted_at, granted_by, is_active, created_at)
        VALUES (@SuperAdminRoleId, @AssignManagerPermId, @CurrentTime, 1, 1, @CurrentTime)
        PRINT '✅ تم تعيين صلاحية تعيين مدير القسم للمدير العام'
    END

    -- إزالة مدير قسم
    IF @RemoveManagerPermId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM role_default_permissions WHERE role_id = @SuperAdminRoleId AND permission_id = @RemoveManagerPermId)
    BEGIN
        INSERT INTO role_default_permissions (role_id, permission_id, granted_at, granted_by, is_active, created_at)
        VALUES (@SuperAdminRoleId, @RemoveManagerPermId, @CurrentTime, 1, 1, @CurrentTime)
        PRINT '✅ تم تعيين صلاحية إزالة مدير القسم للمدير العام'
    END

    -- إدارة مستخدمي القسم
    IF @ManageUsersPermId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM role_default_permissions WHERE role_id = @SuperAdminRoleId AND permission_id = @ManageUsersPermId)
    BEGIN
        INSERT INTO role_default_permissions (role_id, permission_id, granted_at, granted_by, is_active, created_at)
        VALUES (@SuperAdminRoleId, @ManageUsersPermId, @CurrentTime, 1, 1, @CurrentTime)
        PRINT '✅ تم تعيين صلاحية إدارة مستخدمي القسم للمدير العام'
    END

    -- إضافة مستخدمين للقسم
    IF @AddUsersPermId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM role_default_permissions WHERE role_id = @SuperAdminRoleId AND permission_id = @AddUsersPermId)
    BEGIN
        INSERT INTO role_default_permissions (role_id, permission_id, granted_at, granted_by, is_active, created_at)
        VALUES (@SuperAdminRoleId, @AddUsersPermId, @CurrentTime, 1, 1, @CurrentTime)
        PRINT '✅ تم تعيين صلاحية إضافة مستخدمين للقسم للمدير العام'
    END

    -- إزالة مستخدمين من القسم
    IF @RemoveUsersPermId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM role_default_permissions WHERE role_id = @SuperAdminRoleId AND permission_id = @RemoveUsersPermId)
    BEGIN
        INSERT INTO role_default_permissions (role_id, permission_id, granted_at, granted_by, is_active, created_at)
        VALUES (@SuperAdminRoleId, @RemoveUsersPermId, @CurrentTime, 1, 1, @CurrentTime)
        PRINT '✅ تم تعيين صلاحية إزالة مستخدمين من القسم للمدير العام'
    END
END

-- تعيين صلاحيات للمدير (Admin)
IF @AdminRoleId IS NOT NULL
BEGIN
    -- تعيين مدير قسم
    IF @AssignManagerPermId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM role_default_permissions WHERE role_id = @AdminRoleId AND permission_id = @AssignManagerPermId)
    BEGIN
        INSERT INTO role_default_permissions (role_id, permission_id, granted_at, granted_by, is_active, created_at)
        VALUES (@AdminRoleId, @AssignManagerPermId, @CurrentTime, 1, 1, @CurrentTime)
        PRINT '✅ تم تعيين صلاحية تعيين مدير القسم للمدير'
    END

    -- إدارة مستخدمي القسم
    IF @ManageUsersPermId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM role_default_permissions WHERE role_id = @AdminRoleId AND permission_id = @ManageUsersPermId)
    BEGIN
        INSERT INTO role_default_permissions (role_id, permission_id, granted_at, granted_by, is_active, created_at)
        VALUES (@AdminRoleId, @ManageUsersPermId, @CurrentTime, 1, 1, @CurrentTime)
        PRINT '✅ تم تعيين صلاحية إدارة مستخدمي القسم للمدير'
    END

    -- إضافة مستخدمين للقسم
    IF @AddUsersPermId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM role_default_permissions WHERE role_id = @AdminRoleId AND permission_id = @AddUsersPermId)
    BEGIN
        INSERT INTO role_default_permissions (role_id, permission_id, granted_at, granted_by, is_active, created_at)
        VALUES (@AdminRoleId, @AddUsersPermId, @CurrentTime, 1, 1, @CurrentTime)
        PRINT '✅ تم تعيين صلاحية إضافة مستخدمين للقسم للمدير'
    END

    -- إزالة مستخدمين من القسم
    IF @RemoveUsersPermId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM role_default_permissions WHERE role_id = @AdminRoleId AND permission_id = @RemoveUsersPermId)
    BEGIN
        INSERT INTO role_default_permissions (role_id, permission_id, granted_at, granted_by, is_active, created_at)
        VALUES (@AdminRoleId, @RemoveUsersPermId, @CurrentTime, 1, 1, @CurrentTime)
        PRINT '✅ تم تعيين صلاحية إزالة مستخدمين من القسم للمدير'
    END
END

-- تعيين صلاحيات محدودة للمدير (Manager)
IF @ManagerRoleId IS NOT NULL
BEGIN
    -- إدارة مستخدمي القسم (للقسم الخاص به فقط)
    IF @ManageUsersPermId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM role_default_permissions WHERE role_id = @ManagerRoleId AND permission_id = @ManageUsersPermId)
    BEGIN
        INSERT INTO role_default_permissions (role_id, permission_id, granted_at, granted_by, is_active, created_at)
        VALUES (@ManagerRoleId, @ManageUsersPermId, @CurrentTime, 1, 1, @CurrentTime)
        PRINT '✅ تم تعيين صلاحية إدارة مستخدمي القسم للمدير'
    END

    -- إضافة مستخدمين للقسم (للقسم الخاص به فقط)
    IF @AddUsersPermId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM role_default_permissions WHERE role_id = @ManagerRoleId AND permission_id = @AddUsersPermId)
    BEGIN
        INSERT INTO role_default_permissions (role_id, permission_id, granted_at, granted_by, is_active, created_at)
        VALUES (@ManagerRoleId, @AddUsersPermId, @CurrentTime, 1, 1, @CurrentTime)
        PRINT '✅ تم تعيين صلاحية إضافة مستخدمين للقسم للمدير'
    END
END

PRINT '🎉 تم الانتهاء من إضافة صلاحيات إدارة مديري الأقسام بنجاح!'
PRINT '=================================================='

-- التحقق من النتائج
PRINT '📊 ملخص الصلاحيات المضافة:'
SELECT 
    name as 'اسم الصلاحية',
    description as 'الوصف',
    permission_group as 'المجموعة',
    level as 'المستوى'
FROM permissions 
WHERE name LIKE 'departments.%' 
AND name IN ('departments.assign_manager', 'departments.remove_manager', 'departments.manage_users', 'departments.add_users', 'departments.remove_users')
ORDER BY name

GO
