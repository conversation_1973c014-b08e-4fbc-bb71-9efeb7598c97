-- إصلاح أسماء الصلاحيات التي تحتوي على مسافات إضافية
-- تاريخ الإنشاء: 2025-01-10
-- الهدف: إزالة المسافات الإضافية من أسماء الصلاحيات في قاعدة البيانات

-- البحث عن الصلاحيات التي تحتوي على مسافات في النهاية
SELECT 
    id,
    name,
    L<PERSON>(name) as name_length,
    CASE 
        WHEN name != RTRIM(name) THEN 'يحتوي على مسافات'
        ELSE 'نظيف'
    END as status
FROM permissions 
WHERE name != RTRIM(name);

-- عرض الصلاحيات المتأثرة
SELECT 
    p.id,
    p.name as original_name,
    RTRIM(p.name) as cleaned_name,
    COUNT(up.user_id) as users_count
FROM permissions p
LEFT JOIN user_permissions up ON p.id = up.permission_id
WHERE p.name != RTRIM(p.name)
GROUP BY p.id, p.name;

-- إصلاح أسماء الصلاحيات (إزالة المسافات)
UPDATE permissions 
SET name = RTRIM(name),
    updated_at = GETDATE()
WHERE name != RTRIM(name);

-- التحقق من النتيجة
SELECT 
    id,
    name,
    LEN(name) as name_length,
    updated_at
FROM permissions 
WHERE name LIKE 'tasks.view_all%';

-- عرض جميع صلاحيات المهام للتأكد
SELECT 
    id,
    name,
    permission_group,
    description
FROM permissions 
WHERE name LIKE 'tasks.%'
ORDER BY name;

-- التحقق من صلاحيات المستخدم 21 بعد الإصلاح
SELECT 
    u.name as user_name,
    p.name as permission_name,
    up.is_active,
    up.granted_at
FROM user_permissions up
JOIN users u ON up.user_id = u.id
JOIN permissions p ON up.permission_id = p.id
WHERE u.id = 21 AND p.name LIKE 'tasks.view%'
ORDER BY p.name;
