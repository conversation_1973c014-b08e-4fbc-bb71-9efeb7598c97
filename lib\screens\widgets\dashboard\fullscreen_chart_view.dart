import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/chart_enums.dart';
import 'package:flutter_application_2/models/dashboard_widget_model.dart';
import 'package:get/get.dart';
import 'dart:math' as math;

import '../../../constants/app_styles.dart';
import '../../../controllers/task_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../controllers/department_controller.dart';
import '../../../utils/chart_type_utils.dart';
import '../common/loading_indicator.dart';
import '../../../models/task_status_enum.dart';
import '../../../models/user_model.dart';
import '../../../models/department_model.dart';

/// عرض المخطط بملء الشاشة
///
/// يعرض المخطط بملء الشاشة مع خيارات التصفية وتغيير نوع المخطط
class FullscreenChartView extends StatefulWidget {
  /// عنصر لوحة المعلومات - يمكن أن يكون null في الإصدار الجديد
  final DashboardWidget? widget;

  /// إعدادات المخطط
  final Map<String, dynamic> settings;

  /// دالة يتم استدعاؤها عند تحديث الإعدادات
  final Function(Map<String, dynamic> settings)? onSettingsUpdated;

  /// عنوان المخطط
  final String? title;

  /// مفتاح المخطط
  final String? chartKey;

  /// نوع المخطط
  final ChartType? chartType;

  /// تاريخ البداية
  final DateTime? startDate;

  /// تاريخ النهاية
  final DateTime? endDate;

  /// نوع الفلتر
  final TimeFilterType? filterType;

  /// دالة يتم استدعاؤها عند تغيير نوع المخطط
  final Function(ChartType)? onChartTypeChanged;

  /// دالة يتم استدعاؤها عند تغيير الفلتر
  final Function(DateTime?, DateTime?, TimeFilterType)? onFilterChanged;

  /// محتوى المخطط
  final Widget? chartContent;

  const FullscreenChartView({
    super.key,
    this.widget,
    required this.settings,
    this.onSettingsUpdated,
    this.title,
    this.chartKey,
    this.chartType,
    this.startDate,
    this.endDate,
    this.filterType,
    this.onChartTypeChanged,
    this.onFilterChanged,
    this.chartContent,
  });

  @override
  State<FullscreenChartView> createState() => _FullscreenChartViewState();
}

class _FullscreenChartViewState extends State<FullscreenChartView> {
  final TaskController _taskController = Get.find<TaskController>();
  final UserController _userController = Get.find<UserController>();
  final DepartmentController _departmentController =
      Get.find<DepartmentController>();

  bool _isLoading = true;
  Map<String, dynamic> _settings = {};
  String _selectedChartType = 'bar';
  DateTime? _startDate;
  DateTime? _endDate;
  String _filterType = 'month';
  TimeFilterType _timeFilterType = TimeFilterType.month;
  String _chartTitle = '';
  String _chartKey = '';

  @override
  void initState() {
    super.initState();
    _settings = Map<String, dynamic>.from(widget.settings);

    // تهيئة البيانات من المعلمات الجديدة
    if (widget.chartType != null) {
      _selectedChartType = _getStringFromChartType(widget.chartType!);
    } else {
      _selectedChartType = _getInitialChartType();
    }

    if (widget.title != null) {
      _chartTitle = widget.title!;
    } else if (widget.widget != null) {
      _chartTitle = widget.widget!.title;
    }

    if (widget.chartKey != null) {
      _chartKey = widget.chartKey!;
    }

    if (widget.startDate != null) {
      _startDate = widget.startDate;
    }

    if (widget.endDate != null) {
      _endDate = widget.endDate;
    }

    if (widget.filterType != null) {
      _timeFilterType = widget.filterType!;
      _filterType = _getStringFromTimeFilterType(_timeFilterType);
    }

    if (_startDate == null || _endDate == null) {
      _initDateFilter();
    }

    // تحميل البيانات فقط إذا لم يتم تمرير محتوى المخطط
    if (widget.chartContent == null) {
      _loadData();
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// الحصول على نوع المخطط الأولي
  String _getInitialChartType() {
    if (widget.widget == null) return 'bar';

    switch (widget.widget!.type) {
      case DashboardWidgetType.taskStatusChart:
        return _settings['chartType'] ?? 'pie';
      case DashboardWidgetType.taskProgressChart:
        return _settings['chartType'] ?? 'line';
      case DashboardWidgetType.userPerformanceChart:
      case DashboardWidgetType.departmentPerformanceChart:
        return _settings['chartType'] ?? 'bar';
      default:
        return 'bar';
    }
  }

  /// تحويل TimeFilterType إلى سلسلة نصية
  String _getStringFromTimeFilterType(TimeFilterType filterType) {
    switch (filterType) {
      case TimeFilterType.week:
        return 'week';
      case TimeFilterType.month:
        return 'month';
      case TimeFilterType.quarter:
        return 'quarter';
      case TimeFilterType.year:
        return 'year';
      case TimeFilterType.all:
        return 'all';
      default:
        return 'month';
    }
  }

  /// تهيئة فلتر التاريخ
  void _initDateFilter() {
    final now = DateTime.now();
    _filterType = _settings['timeRange'] ?? 'month';

    switch (_filterType) {
      case 'week':
        final weekStart = now.subtract(Duration(days: now.weekday - 1));
        _startDate = DateTime(weekStart.year, weekStart.month, weekStart.day);
        _endDate = _startDate!
            .add(const Duration(days: 6, hours: 23, minutes: 59, seconds: 59));
        break;
      case 'month':
        _startDate = DateTime(now.year, now.month, 1);
        _endDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
        break;
      case 'quarter':
        final quarterMonth = (now.month - 1) ~/ 3 * 3 + 1;
        _startDate = DateTime(now.year, quarterMonth, 1);
        _endDate = DateTime(now.year, quarterMonth + 3, 0, 23, 59, 59);
        break;
      case 'year':
        _startDate = DateTime(now.year, 1, 1);
        _endDate = DateTime(now.year, 12, 31, 23, 59, 59);
        break;
      case 'all':
        _startDate = null;
        _endDate = null;
        break;
      default:
        _startDate = DateTime(now.year, now.month, 1);
        _endDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
    }
  }

  /// تحميل البيانات
  void _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // تحميل البيانات حسب نوع المخطط
    if (widget.widget != null) {
      switch (widget.widget!.type) {
        case DashboardWidgetType.taskStatusChart:
          await _taskController.loadAllTasks();
          break;
        case DashboardWidgetType.taskProgressChart:
          await _taskController.loadAllTasks();
          break;
        case DashboardWidgetType.userPerformanceChart:
          await _taskController.loadAllTasks();
          await _userController.loadAllUsers();
          break;
        case DashboardWidgetType.departmentPerformanceChart:
          await _taskController.loadAllTasks();
          await _departmentController.loadAllDepartments();
          break;
        default:
          break;
      }
    } else {
      // تحميل البيانات الافتراضية
      await _taskController.loadAllTasks();
      await _userController.loadAllUsers();
      await _departmentController.loadAllDepartments();
    }

    setState(() {
      _isLoading = false;
    });
  }

  /// تحديث الإعدادات
  void _updateSettings() {
    _settings['chartType'] = _selectedChartType;
    _settings['timeRange'] = _filterType;

    // استدعاء دالة تغيير نوع المخطط إذا كانت موجودة
    if (widget.onChartTypeChanged != null) {
      widget.onChartTypeChanged!(_getChartTypeFromString(_selectedChartType));
    }

    // استدعاء دالة تغيير الفلتر إذا كانت موجودة
    if (widget.onFilterChanged != null) {
      widget.onFilterChanged!(_startDate, _endDate, _timeFilterType);
    }

    if (widget.onSettingsUpdated != null) {
      widget.onSettingsUpdated!(_settings);
    }

    Get.back();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_chartTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            tooltip: 'حفظ التغييرات',
            onPressed: _updateSettings,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلتر
          _buildFilterBar(),

          // محتوى المخطط
          Expanded(
            child: _isLoading
                ? const Center(child: LoadingIndicator())
                : widget.chartContent != null
                    ? Center(child: widget.chartContent)
                    : _buildChartContent(),
          ),

          // شريط أنواع المخططات
          _buildChartTypeSelector(),
        ],
      ),
    );
  }

  /// بناء شريط الفلتر
  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey[850]
            : Colors.grey[100],
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          const Text('الفترة الزمنية:'),
          const SizedBox(width: 16),
          DropdownButton<String>(
            value: _filterType,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _filterType = value;
                  _initDateFilter();
                });
              }
            },
            items: const [
              DropdownMenuItem(value: 'week', child: Text('أسبوع')),
              DropdownMenuItem(value: 'month', child: Text('شهر')),
              DropdownMenuItem(value: 'quarter', child: Text('ربع سنوي')),
              DropdownMenuItem(value: 'year', child: Text('سنة')),
              DropdownMenuItem(value: 'all', child: Text('الكل')),
            ],
          ),
          const Spacer(),
          ElevatedButton.icon(
            onPressed: _loadData,
            icon: const Icon(Icons.refresh),
            label: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى المخطط
  Widget _buildChartContent() {
    return FutureBuilder<Map<String, dynamic>>(
      future: _getChartData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: LoadingIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'خطأ في تحميل البيانات: ${snapshot.error}',
              style: AppStyles.bodyMedium.copyWith(color: Colors.red),
              textAlign: TextAlign.center,
            ),
          );
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(
            child: Text(
              'لا توجد بيانات متاحة',
              style: AppStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
          );
        }

        // عرض المخطط المناسب حسب النوع المحدد
        return _buildVisualization(snapshot.data!);
      },
    );
  }

  /// بناء التصور المرئي حسب النوع المحدد
  Widget _buildVisualization(Map<String, dynamic> data) {
    switch (_selectedChartType) {
      case 'bar':
        return _buildBarChart(data);
      case 'line':
        return _buildLineChart(data);
      case 'pie':
        return _buildPieChart(data);
      case 'area':
        return _buildAreaChart(data);
      case 'scatter':
        return _buildScatterChart(data);
      case 'radar':
        return _buildRadarChart(data);
      case 'gauge':
        return _buildGaugeChart(data);
      default:
        return Center(
          child: Text(
            'نوع مخطط غير مدعوم: $_selectedChartType',
            style: AppStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
        );
    }
  }

  /// بناء مخطط الأعمدة
  Widget _buildBarChart(Map<String, dynamic> data) {
    // التحقق من وجود البيانات وأنها ليست فارغة
    if (data.isEmpty ||
        !data.containsKey('data') ||
        data['data'] == null ||
        data['data'].isEmpty) {
      return const Center(child: Text('لا توجد بيانات متاحة'));
    }

    final chartData = data['data'] as List<dynamic>;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مخطط الأعمدة',
            style: AppStyles.titleMedium,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: LayoutBuilder(
              builder: (context, constraints) {
                return SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: SizedBox(
                    width:
                        math.max(constraints.maxWidth, chartData.length * 80.0),
                    child: _buildBarChartContent(chartData),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى مخطط الأعمدة
  Widget _buildBarChartContent(List<dynamic> chartData) {
    // تحديد الحقول حسب نوع المخطط
    String labelField = '';
    String valueField = '';

    if (widget.widget != null) {
      switch (widget.widget!.type) {
        case DashboardWidgetType.taskStatusChart:
          labelField = 'status';
          valueField = 'count';
          break;
        case DashboardWidgetType.userPerformanceChart:
          labelField = 'userName';
          valueField = 'completionRate';
          break;
        case DashboardWidgetType.departmentPerformanceChart:
          labelField = 'departmentName';
          valueField = 'completionRate';
          break;
        default:
          // التحقق من وجود بيانات قبل الوصول إلى العناصر
          if (chartData.isNotEmpty && chartData.first is Map) {
            final keys = chartData.first.keys.toList();
            if (keys.isNotEmpty) {
              labelField = keys.first;
              valueField = keys.length > 1 ? keys.last : keys.first;
            } else {
              // قيم افتراضية إذا لم تكن هناك مفاتيح
              labelField = 'label';
              valueField = 'value';
            }
          } else {
            // قيم افتراضية إذا لم تكن هناك بيانات
            labelField = 'label';
            valueField = 'value';
            return Center(child: Text('لا توجد بيانات كافية لعرض المخطط'));
          }
      }
    } else {
      // التحقق من وجود بيانات قبل الوصول إلى العناصر
      if (chartData.isNotEmpty && chartData.first is Map) {
        final keys = chartData.first.keys.toList();
        if (keys.isNotEmpty) {
          labelField = keys.first;
          valueField = keys.length > 1 ? keys.last : keys.first;
        } else {
          // قيم افتراضية إذا لم تكن هناك مفاتيح
          labelField = 'label';
          valueField = 'value';
        }
      } else {
        // قيم افتراضية إذا لم تكن هناك بيانات
        labelField = 'label';
        valueField = 'value';
        return Center(child: Text('لا توجد بيانات كافية لعرض المخطط'));
      }
    }

    // الحصول على القيمة القصوى
    double maxValue = 0;
    for (final item in chartData) {
      final value = item[valueField] is int
          ? (item[valueField] as int).toDouble()
          : (item[valueField] as double);
      if (value > maxValue) maxValue = value;
    }

    // تقريب القيمة القصوى لأعلى
    maxValue = (maxValue * 1.2).ceilToDouble();

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // محور القيم (Y)
        Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: List.generate(6, (index) {
            final value = maxValue * (5 - index) / 5;
            return Container(
              height: 30,
              alignment: Alignment.centerRight,
              child: Text(
                value.toStringAsFixed(0),
                style: AppStyles.bodySmall,
              ),
            );
          }),
        ),
        const SizedBox(width: 8),
        // الأعمدة
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: chartData.map((item) {
              final label = item[labelField].toString();
              final value = item[valueField] is int
                  ? (item[valueField] as int).toDouble()
                  : (item[valueField] as double);
              final barHeight = (value / maxValue) * 150;

              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // قيمة العمود
                    Text(
                      value.toStringAsFixed(1),
                      style: AppStyles.bodySmall,
                    ),
                    const SizedBox(height: 4),
                    // العمود
                    Container(
                      width: 40,
                      height: barHeight,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(4),
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    // تسمية العمود
                    SizedBox(
                      width: 60,
                      child: Text(
                        label,
                        style: AppStyles.bodySmall,
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  /// بناء مخطط خطي
  Widget _buildLineChart(Map<String, dynamic> data) {
    // التحقق من وجود البيانات وأنها ليست فارغة
    if (data.isEmpty ||
        !data.containsKey('data') ||
        data['data'] == null ||
        data['data'].isEmpty) {
      return const Center(child: Text('لا توجد بيانات متاحة'));
    }

    final chartData = data['data'] as List<dynamic>;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مخطط خطي',
            style: AppStyles.titleMedium,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: LayoutBuilder(
              builder: (context, constraints) {
                return SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: SizedBox(
                    width:
                        math.max(constraints.maxWidth, chartData.length * 60.0),
                    child: _buildLineChartContent(chartData),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى المخطط الخطي
  Widget _buildLineChartContent(List<dynamic> chartData) {
    // تحديد الحقول حسب نوع المخطط
    String labelField = 'date';
    List<String> valueFields = ['created', 'completed'];
    List<String> valueLabels = ['تم إنشاؤها', 'تم إكمالها'];
    List<Color> colors = [Colors.blue, Colors.green];

    // الحصول على القيمة القصوى
    double maxValue = 0;
    for (final item in chartData) {
      for (final field in valueFields) {
        final value = item[field] is int
            ? (item[field] as int).toDouble()
            : (item[field] as double? ?? 0);
        if (value > maxValue) maxValue = value;
      }
    }

    // تقريب القيمة القصوى لأعلى
    maxValue = (maxValue * 1.2).ceilToDouble();

    return Column(
      children: [
        // المفتاح
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(valueFields.length, (index) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    color: colors[index],
                  ),
                  const SizedBox(width: 4),
                  Text(valueLabels[index]),
                ],
              ),
            );
          }),
        ),
        const SizedBox(height: 16),
        // المخطط
        Expanded(
          child: CustomPaint(
            size: Size(chartData.length * 60.0, 200),
            painter: _LineChartPainter(
              data: chartData,
              labelField: labelField,
              valueFields: valueFields,
              colors: colors,
              maxValue: maxValue,
            ),
          ),
        ),
        // المحور السيني (X)
        SizedBox(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: chartData.length,
            itemBuilder: (context, index) {
              final item = chartData[index];
              return Container(
                width: 60,
                alignment: Alignment.center,
                child: Text(
                  item[labelField].toString().split('-').sublist(1).join('/'),
                  style: AppStyles.bodySmall,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// بناء مخطط دائري
  Widget _buildPieChart(Map<String, dynamic> data) {
    // التحقق من وجود البيانات وأنها ليست فارغة
    if (data.isEmpty ||
        !data.containsKey('data') ||
        data['data'] == null ||
        data['data'].isEmpty) {
      return const Center(child: Text('لا توجد بيانات متاحة'));
    }

    final chartData = data['data'] as List<dynamic>;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مخطط دائري',
            style: AppStyles.titleMedium,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _buildPieChartContent(chartData),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى المخطط الدائري
  Widget _buildPieChartContent(List<dynamic> chartData) {
    // تحديد الحقول حسب نوع المخطط
    String labelField = '';
    String valueField = '';

    if (widget.widget != null) {
      switch (widget.widget!.type) {
        case DashboardWidgetType.taskStatusChart:
          labelField = 'status';
          valueField = 'count';
          break;
        default:
          // التحقق من وجود بيانات قبل الوصول إلى العناصر
          if (chartData.isNotEmpty && chartData.first is Map) {
            final keys = chartData.first.keys.toList();
            if (keys.isNotEmpty) {
              labelField = keys.first;
              valueField = keys.length > 1 ? keys.last : keys.first;
            } else {
              // قيم افتراضية إذا لم تكن هناك مفاتيح
              labelField = 'label';
              valueField = 'value';
            }
          } else {
            // قيم افتراضية إذا لم تكن هناك بيانات
            labelField = 'label';
            valueField = 'value';
            return Center(child: Text('لا توجد بيانات كافية لعرض المخطط'));
          }
      }
    } else {
      // التحقق من وجود بيانات قبل الوصول إلى العناصر
      if (chartData.isNotEmpty && chartData.first is Map) {
        final keys = chartData.first.keys.toList();
        if (keys.isNotEmpty) {
          labelField = keys.first;
          valueField = keys.length > 1 ? keys.last : keys.first;
        } else {
          // قيم افتراضية إذا لم تكن هناك مفاتيح
          labelField = 'label';
          valueField = 'value';
        }
      } else {
        // قيم افتراضية إذا لم تكن هناك بيانات
        labelField = 'label';
        valueField = 'value';
        return Center(child: Text('لا توجد بيانات كافية لعرض المخطط'));
      }
    }

    // حساب المجموع
    double total = 0;
    for (final item in chartData) {
      total += item[valueField] is int
          ? (item[valueField] as int).toDouble()
          : (item[valueField] as double);
    }

    // ألوان المخطط
    final List<Color> colors = [
      Colors.blue,
      Colors.green,
      Colors.red,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.amber,
      Colors.indigo,
      Colors.cyan,
    ];

    return Row(
      children: [
        // المخطط الدائري
        Expanded(
          flex: 3,
          child: CustomPaint(
            size: const Size(200, 200),
            painter: _PieChartPainter(
              data: chartData,
              labelField: labelField,
              valueField: valueField,
              colors: colors,
            ),
          ),
        ),
        // المفتاح
        Expanded(
          flex: 2,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: List.generate(chartData.length, (index) {
                final item = chartData[index];
                final label = item[labelField].toString();
                final value = item[valueField] is int
                    ? (item[valueField] as int).toDouble()
                    : (item[valueField] as double);
                final percentage = (value / total * 100).toStringAsFixed(1);

                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      Container(
                        width: 16,
                        height: 16,
                        color: colors[index % colors.length],
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          label,
                          style: AppStyles.bodySmall,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '$percentage%',
                        style: AppStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء مخطط مساحي
  Widget _buildAreaChart(Map<String, dynamic> data) {
    // استخدام نفس منطق المخطط الخطي مع إضافة منطقة تحت الخط
    return _buildLineChart(data);
  }

  /// بناء مخطط انتشاري
  Widget _buildScatterChart(Map<String, dynamic> data) {
    return Center(
      child: Text(
        'مخطط انتشاري (قيد التطوير)',
        style: AppStyles.bodyMedium,
        textAlign: TextAlign.center,
      ),
    );
  }

  /// بناء مخطط راداري
  Widget _buildRadarChart(Map<String, dynamic> data) {
    return Center(
      child: Text(
        'مخطط راداري (قيد التطوير)',
        style: AppStyles.bodyMedium,
        textAlign: TextAlign.center,
      ),
    );
  }

  /// بناء مخطط مقياس
  Widget _buildGaugeChart(Map<String, dynamic> data) {
    return Center(
      child: Text(
        'مخطط مقياس (قيد التطوير)',
        style: AppStyles.bodyMedium,
        textAlign: TextAlign.center,
      ),
    );
  }

  /// الحصول على بيانات المخطط
  Future<Map<String, dynamic>> _getChartData() async {
    try {
      if (widget.widget != null) {
        switch (widget.widget!.type) {
          case DashboardWidgetType.taskStatusChart:
            return await _getTaskStatusChartData();
          case DashboardWidgetType.taskProgressChart:
            return await _getTaskProgressChartData();
          case DashboardWidgetType.userPerformanceChart:
            return await _getUserPerformanceChartData();
          case DashboardWidgetType.departmentPerformanceChart:
            return await _getDepartmentPerformanceChartData();
          default:
            // إرجاع هيكل بيانات فارغ ولكن صالح
            return {'data': []};
        }
      } else {
        // إذا لم يكن هناك widget، استخدم البيانات الافتراضية
        return await _getTaskStatusChartData();
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات المخطط: $e');
      // إرجاع هيكل بيانات فارغ ولكن صالح في حالة حدوث خطأ
      return {'data': []};
    }
  }

  /// الحصول على بيانات مخطط حالة المهام
  Future<Map<String, dynamic>> _getTaskStatusChartData() async {
    final tasks = _taskController.allTasks;
    final Map<String, int> statusCounts = {};

    // تصفية المهام حسب التاريخ إذا كان محددًا
    final filteredTasks = tasks.where((task) {
      if (_startDate == null || _endDate == null) return true;
      final taskDate = task.createdAtDateTime;
      return taskDate.isAfter(_startDate!) && taskDate.isBefore(_endDate!);
    }).toList();

    // حساب عدد المهام لكل حالة
    for (final task in filteredTasks) {
      final statusName = _getStatusName(TaskStatus.fromString(task.status));
      statusCounts[statusName] = (statusCounts[statusName] ?? 0) + 1;
    }

    // تحويل البيانات إلى التنسيق المطلوب
    final List<Map<String, dynamic>> result = [];
    statusCounts.forEach((status, count) {
      result.add({
        'status': status,
        'count': count,
      });
    });

    return {'data': result};
  }

  /// الحصول على اسم الحالة بالعربية
  String _getStatusName(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return 'قيد الانتظار';
      case TaskStatus.inProgress:
        return 'قيد التنفيذ';
      case TaskStatus.waitingForInfo:
        return 'في انتظار معلومات';
      case TaskStatus.completed:
        return 'مكتملة';
      case TaskStatus.cancelled:
        return 'ملغاة';
      case TaskStatus.news:
        return 'جديدة';
      default:
        return status.toString().split('.').last;
    }
  }

  /// الحصول على بيانات مخطط تقدم المهام
  Future<Map<String, dynamic>> _getTaskProgressChartData() async {
    final tasks = _taskController.allTasks;
    final Map<String, int> completedByDate = {};
    final Map<String, int> createdByDate = {};

    // تصفية المهام حسب التاريخ إذا كان محددًا
    final filteredTasks = tasks.where((task) {
      if (_startDate == null || _endDate == null) return true;
      final taskDate = task.createdAtDateTime;
      return taskDate.isAfter(_startDate!) && taskDate.isBefore(_endDate!);
    }).toList();

    // تجميع البيانات حسب التاريخ
    for (final task in filteredTasks) {
      final createdDate = _formatDate(task.createdAtDateTime);
      createdByDate[createdDate] = (createdByDate[createdDate] ?? 0) + 1;

      if (task.status == TaskStatus.completed.id) {
        final completedDate = task.completedAt != null
            ? _formatDate(DateTime.fromMillisecondsSinceEpoch(task.completedAt! * 1000))
            : _formatDate(task.createdAtDateTime);
        completedByDate[completedDate] =
            (completedByDate[completedDate] ?? 0) + 1;
      }
    }

    // تحويل البيانات إلى التنسيق المطلوب
    final List<Map<String, dynamic>> result = [];
    final allDates = {...createdByDate.keys, ...completedByDate.keys}.toList()
      ..sort();

    for (final date in allDates) {
      result.add({
        'date': date,
        'created': createdByDate[date] ?? 0,
        'completed': completedByDate[date] ?? 0,
      });
    }

    return {'data': result};
  }

  /// الحصول على بيانات مخطط أداء المستخدمين
  Future<Map<String, dynamic>> _getUserPerformanceChartData() async {
    final tasks = _taskController.allTasks;
    final users = _userController.allUsers;
    final Map<int, Map<String, int>> userPerformance = {};

    // تصفية المهام حسب التاريخ إذا كان محددًا
    final filteredTasks = tasks.where((task) {
      if (_startDate == null || _endDate == null) return true;
      final taskDate = task.createdAtDateTime;
      return taskDate.isAfter(_startDate!) && taskDate.isBefore(_endDate!);
    }).toList();

    // تجميع البيانات حسب المستخدم
    for (final task in filteredTasks) {
      final assigneeId = task.assigneeId;
      if (assigneeId == null) continue;

      if (!userPerformance.containsKey(assigneeId)) {
        userPerformance[assigneeId] = {
          'total': 0,
          'completed': 0,
          'inProgress': 0,
          'overdue': 0,
        };
      }

      userPerformance[assigneeId]!['total'] =
          (userPerformance[assigneeId]!['total'] ?? 0) + 1;

      if (task.status == TaskStatus.completed.id) {
        userPerformance[assigneeId]!['completed'] =
            (userPerformance[assigneeId]!['completed'] ?? 0) + 1;
      } else if (task.status == TaskStatus.inProgress.id) {
        userPerformance[assigneeId]!['inProgress'] =
            (userPerformance[assigneeId]!['inProgress'] ?? 0) + 1;
      }

      if (task.dueDate != null &&
          DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000).isBefore(DateTime.now()) &&
          task.status != TaskStatus.completed.id) {
        userPerformance[assigneeId]!['overdue'] =
            (userPerformance[assigneeId]!['overdue'] ?? 0) + 1;
      }
    }

    // تحويل البيانات إلى التنسيق المطلوب
    final List<Map<String, dynamic>> result = [];
    userPerformance.forEach((userId, performance) {
      User? user;
      try {
        user = users.firstWhere((u) => u.id == userId);
      } catch (e) {
        // إذا لم يتم العثور على المستخدم، تجاهل هذا السجل
        return;
      }

      result.add({
        'userId': userId,
        'userName': user.name,
        'total': performance['total'] ?? 0,
        'completed': performance['completed'] ?? 0,
        'inProgress': performance['inProgress'] ?? 0,
        'overdue': performance['overdue'] ?? 0,
        'completionRate': performance['total'] != 0
            ? (performance['completed'] ?? 0) / performance['total']! * 100
            : 0,
      });
    });

    // ترتيب النتائج حسب معدل الإكمال
    result.sort((a, b) => b['completionRate'].compareTo(a['completionRate']));

    return {'data': result};
  }

  /// الحصول على بيانات مخطط أداء الأقسام
  Future<Map<String, dynamic>> _getDepartmentPerformanceChartData() async {
    final tasks = _taskController.allTasks;
    final departments = _departmentController.allDepartments;
    final Map<int, Map<String, int>> departmentPerformance = {};

    // تصفية المهام حسب التاريخ إذا كان محددًا
    final filteredTasks = tasks.where((task) {
      if (_startDate == null || _endDate == null) return true;
      final taskDate = task.createdAtDateTime;
      return taskDate.isAfter(_startDate!) && taskDate.isBefore(_endDate!);
    }).toList();

    // تجميع البيانات حسب القسم
    for (final task in filteredTasks) {
      final departmentId = task.departmentId;
      if (departmentId == null) continue;

      if (!departmentPerformance.containsKey(departmentId)) {
        departmentPerformance[departmentId] = {
          'total': 0,
          'completed': 0,
          'inProgress': 0,
          'overdue': 0,
        };
      }

      departmentPerformance[departmentId]!['total'] =
          (departmentPerformance[departmentId]!['total'] ?? 0) + 1;

      if (task.status == TaskStatus.completed.id) {
        departmentPerformance[departmentId]!['completed'] =
            (departmentPerformance[departmentId]!['completed'] ?? 0) + 1;
      } else if (task.status == TaskStatus.inProgress.id) {
        departmentPerformance[departmentId]!['inProgress'] =
            (departmentPerformance[departmentId]!['inProgress'] ?? 0) + 1;
      }

      if (task.dueDate != null &&
          DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000).isBefore(DateTime.now()) &&
          task.status != TaskStatus.completed.id) {
        departmentPerformance[departmentId]!['overdue'] =
            (departmentPerformance[departmentId]!['overdue'] ?? 0) + 1;
      }
    }

    // تحويل البيانات إلى التنسيق المطلوب
    final List<Map<String, dynamic>> result = [];
    departmentPerformance.forEach((deptId, performance) {
      Department? department;
      try {
        department = departments.firstWhere((d) => d.id == deptId);
      } catch (e) {
        // إذا لم يتم العثور على القسم، تجاهل هذا السجل
        return;
      }

      result.add({
        'departmentId': deptId,
        'departmentName': department.name,
        'total': performance['total'] ?? 0,
        'completed': performance['completed'] ?? 0,
        'inProgress': performance['inProgress'] ?? 0,
        'overdue': performance['overdue'] ?? 0,
        'completionRate': performance['total'] != 0
            ? (performance['completed'] ?? 0) / performance['total']! * 100
            : 0,
      });
    });

    // ترتيب النتائج حسب معدل الإكمال
    result.sort((a, b) => b['completionRate'].compareTo(a['completionRate']));

    return {'data': result};
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// بناء محدد نوع المخطط
  Widget _buildChartTypeSelector() {
    // تحويل نوع المخطط الحالي من سلسلة نصية إلى ChartType
    final ChartType currentType = _getChartTypeFromString(_selectedChartType);

    // تحديد أنواع المخططات المتاحة - إضافة جميع الأنواع المدعومة
    final List<ChartType> availableTypes = [
      ChartType.bar,
      ChartType.line,
      ChartType.pie,
      ChartType.area,
      ChartType.scatter,
      ChartType.radar,
      ChartType.bubble,
      ChartType.donut,
      ChartType.gauge,
      ChartType.treemap,
      ChartType.funnel,
      ChartType.waterfall,
      ChartType.heatmap,
    ];

    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey[850]
            : Colors.grey[100],
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: availableTypes.map((type) {
            final isSelected = currentType == type;
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
              child: ChoiceChip(
                label: Icon(
                  ChartTypeUtils.getChartTypeIcon(type),
                  size: 32,
                  color: isSelected ? Colors.white : Colors.grey.shade700,
                ),
                selected: isSelected,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _selectedChartType = _getStringFromChartType(type);
                    });
                  }
                },
                backgroundColor: Colors.grey.shade200,
                selectedColor: Theme.of(context).primaryColor,
                tooltip: ChartTypeUtils.getChartTypeLabel(type),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// تحويل نوع المخطط من سلسلة نصية إلى ChartType
  ChartType _getChartTypeFromString(String typeStr) {
    switch (typeStr) {
      case 'pie':
        return ChartType.pie;
      case 'bar':
        return ChartType.bar;
      case 'line':
        return ChartType.line;
      case 'area':
        return ChartType.area;
      case 'scatter':
        return ChartType.scatter;
      case 'radar':
        return ChartType.radar;
      case 'gauge':
        return ChartType.gauge;
      case 'bubble':
        return ChartType.bubble;
      case 'donut':
        return ChartType.donut;
      case 'treemap':
        return ChartType.treemap;
      case 'funnel':
        return ChartType.funnel;
      case 'waterfall':
        return ChartType.waterfall;
      case 'heatmap':
        return ChartType.heatmap;
      default:
        return ChartType.bar;
    }
  }

  /// تحويل نوع المخطط من ChartType إلى سلسلة نصية
  String _getStringFromChartType(ChartType type) {
    switch (type) {
      case ChartType.pie:
        return 'pie';
      case ChartType.bar:
        return 'bar';
      case ChartType.line:
        return 'line';
      case ChartType.area:
        return 'area';
      case ChartType.scatter:
        return 'scatter';
      case ChartType.radar:
        return 'radar';
      case ChartType.gauge:
        return 'gauge';
      case ChartType.bubble:
        return 'bubble';
      case ChartType.donut:
        return 'donut';
      case ChartType.treemap:
        return 'treemap';
      case ChartType.funnel:
        return 'funnel';
      case ChartType.waterfall:
        return 'waterfall';
      case ChartType.heatmap:
        return 'heatmap';
      default:
        return 'bar';
    }
  }
}

/// رسام المخطط الخطي
class _LineChartPainter extends CustomPainter {
  final List<dynamic> data;
  final String labelField;
  final List<String> valueFields;
  final List<Color> colors;
  final double maxValue;

  _LineChartPainter({
    required this.data,
    required this.labelField,
    required this.valueFields,
    required this.colors,
    required this.maxValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;

    final dotPaint = Paint()..style = PaintingStyle.fill;

    final gridPaint = Paint()
      ..color = Colors.grey.withAlpha(50)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // رسم الشبكة
    _drawGrid(canvas, size, gridPaint);

    // رسم المخطط لكل حقل
    for (int fieldIndex = 0; fieldIndex < valueFields.length; fieldIndex++) {
      final field = valueFields[fieldIndex];
      final color = colors[fieldIndex % colors.length];

      paint.color = color;
      dotPaint.color = color;

      final path = Path();
      bool isFirstPoint = true;

      // رسم الخط
      for (int i = 0; i < data.length; i++) {
        final item = data[i];
        final value = item[field] is int
            ? (item[field] as int).toDouble()
            : (item[field] as double? ?? 0);

        final x = i * (size.width / (data.length - 1));
        final y = size.height - (value / maxValue * size.height);

        if (isFirstPoint) {
          path.moveTo(x, y);
          isFirstPoint = false;
        } else {
          path.lineTo(x, y);
        }

        // رسم النقاط
        canvas.drawCircle(Offset(x, y), 4, dotPaint);
      }

      canvas.drawPath(path, paint);
    }
  }

  /// رسم الشبكة
  void _drawGrid(Canvas canvas, Size size, Paint paint) {
    // رسم الخطوط الأفقية
    for (int i = 0; i <= 5; i++) {
      final y = size.height * i / 5;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }

    // رسم الخطوط الرأسية
    for (int i = 0; i < data.length; i++) {
      final x = i * (size.width / (data.length - 1));
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// رسام المخطط الدائري
class _PieChartPainter extends CustomPainter {
  final List<dynamic> data;
  final String labelField;
  final String valueField;
  final List<Color> colors;

  _PieChartPainter({
    required this.data,
    required this.labelField,
    required this.valueField,
    required this.colors,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2;

    // حساب المجموع
    double total = 0;
    for (final item in data) {
      total += item[valueField] is int
          ? (item[valueField] as int).toDouble()
          : (item[valueField] as double);
    }

    // رسم القطاعات
    double startAngle = 0;
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      final value = item[valueField] is int
          ? (item[valueField] as int).toDouble()
          : (item[valueField] as double);

      final sweepAngle = (value / total) * 2 * math.pi;

      paint.color = colors[i % colors.length];

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );

      startAngle += sweepAngle;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
