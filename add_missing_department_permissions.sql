-- إضافة الصلاحيات المفقودة لإدارة الأقسام
-- تاريخ الإنشاء: 2025-01-12
-- الهدف: إضافة جميع الصلاحيات المطلوبة لنظام الأقسام المحسن

USE [databasetasks]
GO

PRINT '🏢 بدء إضافة الصلاحيات المفقودة لإدارة الأقسام...'
PRINT '=================================================='

-- إضافة صلاحية تعيين مدير القسم
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'departments.assign_manager')
BEGIN
    INSERT INTO [dbo].[permissions] (
        [name], [description], [permission_group], [category], [level], 
        [icon], [color], [is_default], [is_active], [created_at], [updated_at]
    )
    VALUES (
        'departments.assign_manager',
        N'تعيين مدير للقسم',
        'departments',
        N'إدارة الأقسام',
        3,
        'admin_panel_settings',
        '#9C27B0',
        0, 1,
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()),
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
    )
    PRINT '✅ تم إضافة صلاحية departments.assign_manager'
END
ELSE
    PRINT '⚠️ صلاحية departments.assign_manager موجودة مسبقاً'

-- إضافة صلاحية إضافة مستخدمين للقسم
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'departments.add_users')
BEGIN
    INSERT INTO [dbo].[permissions] (
        [name], [description], [permission_group], [category], [level], 
        [icon], [color], [is_default], [is_active], [created_at], [updated_at]
    )
    VALUES (
        'departments.add_users',
        N'إضافة مستخدمين للقسم',
        'departments',
        N'إدارة الأقسام',
        3,
        'person_add',
        '#4CAF50',
        0, 1,
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()),
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
    )
    PRINT '✅ تم إضافة صلاحية departments.add_users'
END
ELSE
    PRINT '⚠️ صلاحية departments.add_users موجودة مسبقاً'

-- إضافة صلاحية إزالة مستخدمين من القسم
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'departments.remove_users')
BEGIN
    INSERT INTO [dbo].[permissions] (
        [name], [description], [permission_group], [category], [level], 
        [icon], [color], [is_default], [is_active], [created_at], [updated_at]
    )
    VALUES (
        'departments.remove_users',
        N'إزالة مستخدمين من القسم',
        'departments',
        N'إدارة الأقسام',
        3,
        'person_remove',
        '#F44336',
        0, 1,
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()),
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
    )
    PRINT '✅ تم إضافة صلاحية departments.remove_users'
END
ELSE
    PRINT '⚠️ صلاحية departments.remove_users موجودة مسبقاً'

-- إضافة صلاحية إدارة شاملة لمستخدمي القسم
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'departments.manage_users')
BEGIN
    INSERT INTO [dbo].[permissions] (
        [name], [description], [permission_group], [category], [level], 
        [icon], [color], [is_default], [is_active], [created_at], [updated_at]
    )
    VALUES (
        'departments.manage_users',
        N'إدارة شاملة لمستخدمي القسم',
        'departments',
        N'إدارة الأقسام',
        4,
        'group',
        '#FF9800',
        0, 1,
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()),
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
    )
    PRINT '✅ تم إضافة صلاحية departments.manage_users'
END
ELSE
    PRINT '⚠️ صلاحية departments.manage_users موجودة مسبقاً'

PRINT ''
PRINT '📊 ملخص جميع صلاحيات الأقسام:'
SELECT 
    [name] as 'اسم الصلاحية',
    [description] as 'الوصف',
    [level] as 'المستوى',
    [is_active] as 'نشطة'
FROM [dbo].[permissions] 
WHERE [permission_group] = 'departments'
ORDER BY [level], [name]

PRINT ''
PRINT '🎯 التصنيف الجديد للصلاحيات:'
PRINT '📋 المستوى 1 - العرض والوصول:'
PRINT '  - departments.view: الوصول الأساسي لعرض الأقسام'
PRINT '  - departments.admin: عرض تفاصيل القسم الكاملة (مدير القسم)'
PRINT ''
PRINT '🔧 المستوى 2-4 - الإدارة والتعديل:'
PRINT '  - departments.manage: التعديل، الإضافة، النقل، الدمج + عرض جميع الأقسام'
PRINT ''
PRINT '👥 المستوى 3-4 - إدارة المستخدمين:'
PRINT '  - departments.assign_manager: تعيين مدير القسم'
PRINT '  - departments.add_users: إضافة مستخدمين للقسم'
PRINT '  - departments.remove_users: إزالة مستخدمين من القسم'
PRINT '  - departments.manage_users: إدارة شاملة لمستخدمي القسم'

PRINT ''
PRINT '🎉 تم الانتهاء من إضافة جميع الصلاحيات المطلوبة بنجاح!'
PRINT '=================================================='

GO
