-- ===================================================================
-- سكريبت إصلاح قاعدة البيانات لحل مشاكل UserPermissions
-- تاريخ الإنشاء: 2025-01-05
-- الهدف: إصلاح جدول user_permissions وإضافة الحقول المفقودة
-- ===================================================================

USE [TaskManagementDB]
GO

PRINT '🚀 بدء تطبيق إصلاحات قاعدة البيانات...'
PRINT '=================================================='

-- ===================================================================
-- 1. التحقق من وجود جدول user_permissions وإضافة created_at إذا كان مفقوداً
-- ===================================================================

PRINT '🔍 التحقق من جدول user_permissions...'

-- التحقق من وجود عمود created_at
IF NOT EXISTS (
    SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'user_permissions' 
    AND COLUMN_NAME = 'created_at'
)
BEGIN
    PRINT '➕ إضافة عمود created_at إلى جدول user_permissions...'
    
    ALTER TABLE [dbo].[user_permissions]
    ADD [created_at] BIGINT NOT NULL 
    DEFAULT (datediff(second,'1970-01-01',getutcdate()))
    
    PRINT '✅ تم إضافة عمود created_at بنجاح'
END
ELSE
BEGIN
    PRINT '✅ عمود created_at موجود بالفعل'
END

-- ===================================================================
-- 2. التحقق من وجود جدول custom_roles وإنشاؤه إذا لم يكن موجوداً
-- ===================================================================

PRINT '🔍 التحقق من جدول custom_roles...'

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'custom_roles')
BEGIN
    PRINT '➕ إنشاء جدول custom_roles...'
    
    CREATE TABLE [dbo].[custom_roles] (
        [id] INT IDENTITY(1,1) NOT NULL,
        [name] NVARCHAR(100) NOT NULL,
        [description] NVARCHAR(255) NULL,
        [parent_role_id] INT NULL,
        [created_by] INT NOT NULL,
        [created_at] BIGINT NOT NULL DEFAULT (datediff(second,'1970-01-01',getutcdate())),
        [updated_at] BIGINT NULL,
        [is_deleted] BIT NOT NULL DEFAULT (0),
        
        CONSTRAINT [PK_custom_roles] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_custom_roles_created_by_users] FOREIGN KEY ([created_by]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [FK_custom_roles_parent] FOREIGN KEY ([parent_role_id]) REFERENCES [dbo].[custom_roles] ([id])
    )
    
    PRINT '✅ تم إنشاء جدول custom_roles بنجاح'
END
ELSE
BEGIN
    PRINT '✅ جدول custom_roles موجود بالفعل'
    
    -- التحقق من الأعمدة المطلوبة وإضافتها إذا كانت مفقودة
    IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'custom_roles' AND COLUMN_NAME = 'created_at')
    BEGIN
        ALTER TABLE [dbo].[custom_roles] ADD [created_at] BIGINT NOT NULL DEFAULT (datediff(second,'1970-01-01',getutcdate()))
        PRINT '✅ تم إضافة عمود created_at إلى custom_roles'
    END
    
    IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'custom_roles' AND COLUMN_NAME = 'updated_at')
    BEGIN
        ALTER TABLE [dbo].[custom_roles] ADD [updated_at] BIGINT NULL
        PRINT '✅ تم إضافة عمود updated_at إلى custom_roles'
    END
END

-- ===================================================================
-- 3. التحقق من وجود جدول user_custom_roles وإنشاؤه إذا لم يكن موجوداً
-- ===================================================================

PRINT '🔍 التحقق من جدول user_custom_roles...'

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'user_custom_roles')
BEGIN
    PRINT '➕ إنشاء جدول user_custom_roles...'
    
    CREATE TABLE [dbo].[user_custom_roles] (
        [id] INT IDENTITY(1,1) NOT NULL,
        [user_id] INT NOT NULL,
        [custom_role_id] INT NOT NULL,
        [assigned_at] BIGINT NOT NULL DEFAULT (datediff(second,'1970-01-01',getutcdate())),
        [is_deleted] BIT NOT NULL DEFAULT (0),
        
        CONSTRAINT [PK_user_custom_roles] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [UQ_user_custom_roles] UNIQUE ([user_id], [custom_role_id]),
        CONSTRAINT [FK_user_custom_roles_user] FOREIGN KEY ([user_id]) REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [FK_user_custom_roles_custom_role] FOREIGN KEY ([custom_role_id]) REFERENCES [dbo].[custom_roles] ([id])
    )
    
    PRINT '✅ تم إنشاء جدول user_custom_roles بنجاح'
END
ELSE
BEGIN
    PRINT '✅ جدول user_custom_roles موجود بالفعل'
    
    -- التحقق من عمود assigned_at
    IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'user_custom_roles' AND COLUMN_NAME = 'assigned_at')
    BEGIN
        ALTER TABLE [dbo].[user_custom_roles] ADD [assigned_at] BIGINT NOT NULL DEFAULT (datediff(second,'1970-01-01',getutcdate()))
        PRINT '✅ تم إضافة عمود assigned_at إلى user_custom_roles'
    END
END

-- ===================================================================
-- 4. التحقق من وجود جدول custom_role_permissions وإنشاؤه إذا لم يكن موجوداً
-- ===================================================================

PRINT '🔍 التحقق من جدول custom_role_permissions...'

IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'custom_role_permissions')
BEGIN
    PRINT '➕ إنشاء جدول custom_role_permissions...'
    
    CREATE TABLE [dbo].[custom_role_permissions] (
        [id] INT IDENTITY(1,1) NOT NULL,
        [custom_role_id] INT NOT NULL,
        [permission_id] INT NOT NULL,
        [created_at] BIGINT NOT NULL DEFAULT (datediff(second,'1970-01-01',getutcdate())),
        [created_by] INT NOT NULL,
        [is_deleted] BIT NOT NULL DEFAULT (0),
        
        CONSTRAINT [PK_custom_role_permissions] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [UQ_custom_role_permissions] UNIQUE ([custom_role_id], [permission_id]),
        CONSTRAINT [FK_custom_role_permissions_custom_role] FOREIGN KEY ([custom_role_id]) REFERENCES [dbo].[custom_roles] ([id]),
        CONSTRAINT [FK_custom_role_permissions_permission] FOREIGN KEY ([permission_id]) REFERENCES [dbo].[permissions] ([id]),
        CONSTRAINT [FK_custom_role_permissions_created_by_users] FOREIGN KEY ([created_by]) REFERENCES [dbo].[users] ([id])
    )
    
    PRINT '✅ تم إنشاء جدول custom_role_permissions بنجاح'
END
ELSE
BEGIN
    PRINT '✅ جدول custom_role_permissions موجود بالفعل'
END

-- ===================================================================
-- 5. تحديث البيانات الموجودة
-- ===================================================================

PRINT '🔄 تحديث البيانات الموجودة...'

-- تحديث created_at للسجلات الموجودة في user_permissions إذا كانت فارغة أو صفر
UPDATE [dbo].[user_permissions] 
SET [created_at] = [granted_at]
WHERE [created_at] IS NULL OR [created_at] = 0

PRINT '✅ تم تحديث created_at في user_permissions'

-- ===================================================================
-- 6. إنشاء فهارس لتحسين الأداء
-- ===================================================================

PRINT '📊 إنشاء فهارس لتحسين الأداء...'

-- فهرس على user_permissions.created_at
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_user_permissions_created_at')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_user_permissions_created_at] 
    ON [dbo].[user_permissions] ([created_at])
    PRINT '✅ تم إنشاء فهرس IX_user_permissions_created_at'
END

-- فهرس على custom_roles.is_deleted
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_custom_roles_is_deleted')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_custom_roles_is_deleted] 
    ON [dbo].[custom_roles] ([is_deleted])
    PRINT '✅ تم إنشاء فهرس IX_custom_roles_is_deleted'
END

-- فهرس على user_custom_roles.is_deleted
IF NOT EXISTS (SELECT 1 FROM sys.indexes WHERE name = 'IX_user_custom_roles_is_deleted')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_user_custom_roles_is_deleted] 
    ON [dbo].[user_custom_roles] ([is_deleted])
    PRINT '✅ تم إنشاء فهرس IX_user_custom_roles_is_deleted'
END

-- ===================================================================
-- 7. التحقق النهائي من البنية
-- ===================================================================

PRINT '🔍 التحقق النهائي من البنية...'

-- التحقق من user_permissions
SELECT 
    'user_permissions' as TableName,
    COUNT(*) as RecordCount,
    COUNT(CASE WHEN created_at IS NOT NULL AND created_at > 0 THEN 1 END) as ValidCreatedAt
FROM [dbo].[user_permissions]

-- التحقق من الجداول الجديدة
SELECT 
    TABLE_NAME,
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME) as ColumnCount
FROM INFORMATION_SCHEMA.TABLES t
WHERE TABLE_NAME IN ('custom_roles', 'user_custom_roles', 'custom_role_permissions')
ORDER BY TABLE_NAME

PRINT '=================================================='
PRINT '🎉 تم تطبيق جميع الإصلاحات بنجاح!'
PRINT '=================================================='
PRINT ''
PRINT '📋 ملخص التغييرات:'
PRINT '✅ إضافة عمود created_at إلى user_permissions'
PRINT '✅ إنشاء/تحديث جدول custom_roles'
PRINT '✅ إنشاء/تحديث جدول user_custom_roles'
PRINT '✅ إنشاء/تحديث جدول custom_role_permissions'
PRINT '✅ تحديث البيانات الموجودة'
PRINT '✅ إنشاء فهارس لتحسين الأداء'
PRINT ''
PRINT '🚀 يمكنك الآن إعادة تشغيل التطبيق!'

GO
