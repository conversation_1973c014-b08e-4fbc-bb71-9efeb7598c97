import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/dashboard_model.dart';
import '../../../services/dashboard_service.dart';

/// مربع حوار إعدادات لوحة المعلومات
class DashboardSettingsDialog extends StatefulWidget {
  final Dashboard dashboard;
  const DashboardSettingsDialog({super.key, required this.dashboard});
  @override
  State<DashboardSettingsDialog> createState() =>
      _DashboardSettingsDialogState();
}

class _DashboardSettingsDialogState extends State<DashboardSettingsDialog> {
  // خدمة لوحة المعلومات
  final DashboardService _dashboardService = Get.find<DashboardService>();

  // حقول الإدخال
  late final TextEditingController _titleController;
  late final TextEditingController _descriptionController;

  // قيم الشبكة
  late int _gridRows;
  late int _gridColumns;

  // حالة الحفظ
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.dashboard.title);
    _descriptionController =
        TextEditingController(text: widget.dashboard.description);
    _gridRows = widget.dashboard.gridRows;
    _gridColumns = widget.dashboard.gridColumns;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تحديد ألوان حسب السمة - مستوحاة من Monday.com
    final Color accentColor = const Color(0xFF00A9FF); // أزرق Monday.com

    final Color cardColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF292F4C)
        : Colors.white;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: cardColor,
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(
                  Icons.settings,
                  color: accentColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'إعدادات لوحة المعلومات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Get.back(),
                  tooltip: 'إغلاق',
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),

            // حقل العنوان
            TextField(
              controller: _titleController,
              decoration: InputDecoration(
                labelText: 'عنوان لوحة المعلومات',
                hintText: 'أدخل عنوانًا للوحة المعلومات',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.title),
              ),
            ),
            const SizedBox(height: 16),

            // حقل الوصف
            TextField(
              controller: _descriptionController,
              decoration: InputDecoration(
                labelText: 'وصف لوحة المعلومات',
                hintText: 'أدخل وصفًا للوحة المعلومات (اختياري)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.description),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 24),

            // إعدادات الشبكة
            const Text(
              'إعدادات الشبكة:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // عدد الصفوف
            Row(
              children: [
                const Text('عدد الصفوف:'),
                const SizedBox(width: 16),
                Expanded(
                  child: SliderTheme(
                    data: SliderThemeData(
                      activeTrackColor: accentColor,
                      thumbColor: accentColor,
                      overlayColor: const Color(0x1A00A9FF), // 0.1 opacity
                      valueIndicatorColor: accentColor,
                      valueIndicatorTextStyle: const TextStyle(
                        color: Colors.white,
                      ),
                    ),
                    child: Slider(
                      value: _gridRows.toDouble(),
                      min: 6,
                      max: 24,
                      divisions: 18,
                      label: '$_gridRows',
                      onChanged: (value) {
                        setState(() {
                          _gridRows = value.toInt();
                        });
                      },
                    ),
                  ),
                ),
                SizedBox(
                  width: 40,
                  child: Text(
                    '$_gridRows',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // عدد الأعمدة
            Row(
              children: [
                const Text('عدد الأعمدة:'),
                const SizedBox(width: 16),
                Expanded(
                  child: SliderTheme(
                    data: SliderThemeData(
                      activeTrackColor: accentColor,
                      thumbColor: accentColor,
                      overlayColor: const Color(0x1A00A9FF), // 0.1 opacity
                      valueIndicatorColor: accentColor,
                      valueIndicatorTextStyle: const TextStyle(
                        color: Colors.white,
                      ),
                    ),
                    child: Slider(
                      value: _gridColumns.toDouble(),
                      min: 6,
                      max: 24,
                      divisions: 18,
                      label: '$_gridColumns',
                      onChanged: (value) {
                        setState(() {
                          _gridColumns = value.toInt();
                        });
                      },
                    ),
                  ),
                ),
                SizedBox(
                  width: 40,
                  child: Text(
                    '$_gridColumns',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _isSaving ? null : _saveSettings,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: accentColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                  child: _isSaving
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('حفظ'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// حفظ الإعدادات
  void _saveSettings() async {
    // التحقق من الإدخال
    if (_titleController.text.trim().isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال عنوان للوحة المعلومات',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // تحديث معلومات لوحة المعلومات
      final success = await _dashboardService.updateDashboardInfo(
        _titleController.text.trim(),
        _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
      );

      if (success) {
        // تحديث أبعاد الشبكة
        final gridSuccess = await _dashboardService.updateGridDimensions(
          _gridRows,
          _gridColumns,
        );

        if (gridSuccess) {
          Get.back(result: true);
          Get.snackbar(
            'تم',
            'تم حفظ الإعدادات بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
        } else {
          Get.snackbar(
            'خطأ',
            'حدث خطأ أثناء تحديث أبعاد الشبكة',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } else {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء حفظ الإعدادات',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حفظ الإعدادات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }
}
