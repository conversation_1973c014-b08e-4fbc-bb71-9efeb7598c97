# حل مشكلة UserPermissions - الحقل المفقود created_at

## 🔍 **الس<PERSON><PERSON> الجذري للمشكلة**

المشكلة الحقيقية كانت في **الحقل المفقود `created_at`** في جدول `user_permissions`:

```sql
CREATE TABLE [dbo].[user_permissions] (
    [id]            INT    IDENTITY (1, 1) NOT NULL,
    [user_id]       INT    NOT NULL,
    [permission_id] INT    NOT NULL,
    [granted_by]    INT    NOT NULL,
    [granted_at]    BIGINT NOT NULL,
    [is_active]     BIT    DEFAULT ((1)) NOT NULL,
    [expires_at]    BIGINT NULL,
    [is_deleted]    BIT    DEFAULT ((0)) NOT NULL,
    [created_at]    BIGINT DEFAULT (datediff(second,'1970-01-01',getutcdate())) NOT NULL, -- ⚠️ هذا الحقل مطلوب!
    PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [UQ_user_permission] UNIQUE NONCLUSTERED ([user_id] ASC, [permission_id] ASC)
);
```

**المشكلة**: 
- الحقل `created_at` **مطلوب** (NOT NULL) في قاعدة البيانات
- لكن النماذج في الباك اند والفرونت اند لا تحتوي على هذا الحقل
- عندما يحاول الباك اند إدراج البيانات، قاعدة البيانات ترفض لأن `created_at` مفقود

## 🛠️ **الحلول المطبقة**

### 1. ✅ تحديث نموذج UserPermission في الباك اند
**الملف**: `webApi/webApi/Models/UserPermission.cs`

```csharp
public partial class UserPermission
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public int PermissionId { get; set; }
    public int GrantedBy { get; set; }
    public long GrantedAt { get; set; }
    public bool IsActive { get; set; }
    public long? ExpiresAt { get; set; }
    public bool IsDeleted { get; set; }
    public long CreatedAt { get; set; } // ✅ إضافة الحقل المفقود

    public virtual User GrantedByNavigation { get; set; } = null!;
    public virtual Permission Permission { get; set; } = null!;
    public virtual User User { get; set; } = null!;
}
```

### 2. ✅ تحديث تكوين قاعدة البيانات
**الملف**: `webApi/webApi/Models/TasksDbContext.cs`

```csharp
entity.Property(e => e.CreatedAt)
    .HasDefaultValueSql("datediff(second,'1970-01-01',getutcdate())")
    .HasColumnName("created_at");
```

### 3. ✅ تحديث UserPermissionsController
**الملف**: `webApi/webApi/Controllers/UserPermissionsController.cs`

```csharp
// تعيين CreatedAt إذا لم يكن موجوداً
if (userPermission.CreatedAt == 0)
{
    userPermission.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
}
```

### 4. ✅ تحديث نماذج Flutter
**الملفات**: 
- `lib/models/user_permission_model.dart`
- `lib/models/user_permission_models.dart`

```dart
class UserPermission {
  final int id;
  final int userId;
  final int permissionId;
  final int grantedBy;
  final int grantedAt;
  final bool isActive;
  final int? expiresAt;
  final bool isDeleted;
  final int createdAt; // ✅ إضافة الحقل المفقود

  const UserPermission({
    required this.id,
    required this.userId,
    required this.permissionId,
    required this.grantedBy,
    required this.grantedAt,
    this.isActive = true,
    this.expiresAt,
    this.isDeleted = false,
    int? createdAt,
    this.user,
    this.permission,
    this.grantedByNavigation,
  }) : createdAt = createdAt ?? grantedAt; // استخدم grantedAt كقيمة افتراضية
}
```

### 5. ✅ تحديث toJson() و fromJson()

```dart
// fromJson
factory UserPermission.fromJson(Map<String, dynamic> json) {
  return UserPermission(
    // ... باقي الحقول
    createdAt: json['createdAt'] as int? ?? json['grantedAt'] as int,
  );
}

// toJson
Map<String, dynamic> toJson() {
  return {
    // ... باقي الحقول
    'createdAt': createdAt,
  };
}

// toJsonForApi
Map<String, dynamic> toJsonForApi() {
  return {
    // ... باقي الحقول
    'createdAt': createdAt, // إضافة createdAt المطلوب
  };
}
```

### 6. ✅ تحديث user_permissions_dialog.dart

```dart
final userPermission = UserPermission(
  id: 0,
  userId: widget.user.id,
  permissionId: entry.key,
  grantedBy: currentUserId,
  grantedAt: currentTime,
  isActive: true,
  isDeleted: false,
  createdAt: currentTime, // ✅ إضافة createdAt
);
```

## 🧪 **كيفية اختبار الحل**

### 1. اختبار مباشر في الباك اند
```bash
GET http://localhost:5176/api/UserPermissions/test-post/20/45
```

### 2. اختبار في Flutter
```bash
dart test_user_permissions_with_created_at.dart
```

### 3. اختبار في التطبيق
1. افتح شاشة إدارة المستخدمين
2. اختر مستخدم
3. حاول إضافة صلاحية جديدة
4. تحقق من Console - يجب أن ترى:
   ```
   🔍 إرسال بيانات الصلاحية: {id: 0, userId: 20, permissionId: 45, grantedBy: 21, grantedAt: 1751754054, isActive: true, expiresAt: null, isDeleted: false, createdAt: 1751754054}
   ✅ تم إضافة الصلاحية بنجاح
   ```

## 🎯 **النتائج المتوقعة**

بعد تطبيق هذه الحلول:

1. ✅ **لن تظهر رسالة "One or more validation errors occurred"**
2. ✅ **ستتم إضافة الصلاحيات بنجاح**
3. ✅ **البيانات ستحتوي على createdAt صحيح**
4. ✅ **قاعدة البيانات ستقبل الإدراج**
5. ✅ **التطبيق سيعمل بشكل طبيعي**

## 🔍 **الرسائل التشخيصية الجديدة**

### في الباك اند:
```
🔍 البيانات النهائية: UserId=20, PermissionId=45, GrantedBy=21, GrantedAt=1751754054, CreatedAt=1751754054
✅ تم إضافة الصلاحية بنجاح: 123
```

### في Flutter:
```
🔍 إرسال بيانات الصلاحية: {id: 0, userId: 20, permissionId: 45, grantedBy: 21, grantedAt: 1751754054, isActive: true, expiresAt: null, isDeleted: false, createdAt: 1751754054}
✅ تم إضافة الصلاحية بنجاح: 45
```

## 🚨 **ملاحظات مهمة**

1. **createdAt vs grantedAt**: 
   - `createdAt`: متى تم إنشاء السجل في قاعدة البيانات
   - `grantedAt`: متى تم منح الصلاحية للمستخدم
   - عادة يكونان نفس القيمة

2. **القيمة الافتراضية**: إذا لم يتم تمرير `createdAt`، سيتم استخدام `grantedAt`

3. **التوافق مع الإصدارات السابقة**: الحل يدعم البيانات القديمة التي لا تحتوي على `createdAt`

4. **قاعدة البيانات**: لديها قيمة افتراضية لـ `created_at` لكن الأفضل إرسالها صراحة

## 📋 **الملفات المحدثة**

### الباك اند:
1. `webApi/webApi/Models/UserPermission.cs` - إضافة CreatedAt
2. `webApi/webApi/Models/TasksDbContext.cs` - تكوين CreatedAt
3. `webApi/webApi/Controllers/UserPermissionsController.cs` - تعيين CreatedAt

### Flutter:
1. `lib/models/user_permission_model.dart` - إضافة createdAt
2. `lib/models/user_permission_models.dart` - إضافة createdAt
3. `lib/screens/admin/users/user_permissions_dialog.dart` - تمرير createdAt
4. `lib/services/api/permissions_api_service.dart` - استخدام toJsonForApi()

## 🎉 **الخلاصة**

المشكلة كانت بسيطة لكن مخفية: **حقل مطلوب في قاعدة البيانات لكنه مفقود في النماذج**. 

الآن بعد إضافة `created_at` في جميع الطبقات، ستعمل إضافة الصلاحيات بنجاح! 🚀
