-- Script to fix encoded permission descriptions using HEX encoding
-- Creation Date: 2025-01-06
-- Purpose: Fix encoded descriptions in permissions table using HEX to avoid encoding issues

-- Start transaction
BEGIN TRANSACTION;

-- Fix first group: Records 40-71 (first encoded descriptions)
-- Using HEX encoding for Arabic text to avoid encoding issues
UPDATE permissions SET description = UNHEX('D8A5D8AFD8A7D8B1D8A9206C6F68D8A9D8A920D8A7D984D8AAD8ADD983D985') WHERE id = 40; -- إدارة لوحة التحكم
UPDATE permissions SET description = UNHEX('D8B9D8B1D8B6D8A720D8A7D984D985D987D8A7D985') WHERE id = 41; -- عرض المهام
UPDATE permissions SET description = UNHEX('D8A5D986D8B4D8A7D8A1D985D987D8A7D98520D8ACD8AFD98AD8AFD8A9') WHERE id = 42; -- إن<PERSON><PERSON><PERSON> مهام جديدة
UPDATE permissions SET description = UNHEX('D8AAD8B9D8AFD98AD984D8A720D8A7D984D985D987D8A7D985') WHERE id = 43; -- تعديل المهام
UPDATE permissions SET description = UNHEX('D8ADD8B0D98120D8A7D984D985D987D8A7D985') WHERE id = 44; -- حذف المهام
UPDATE permissions SET description = UNHEX('D8AAD8B9D98AD98AD986D8A720D8A7D984D985D987D8A7D98520D984D984D985D8B3D8AAD8AED8AFD985D98AD986') WHERE id = 45; -- تعيين المهام للمستخدمين
UPDATE permissions SET description = UNHEX('D8AAD8ADD8AFD98AD8ABD8A720D8A7D984D985D987D8A7D98520D8A7D984D8AED8A7D8B5D8A9') WHERE id = 46; -- تحديث المهام الخاصة
UPDATE permissions SET description = UNHEX('D8B9D8B1D8B6D8A720D8A7D984D985D8B3D8AAD8AED8AFD985D98AD986') WHERE id = 47; -- عرض المستخدمين
UPDATE permissions SET description = UNHEX('D8A5D986D8B4D8A7D8A1D985D8B3D8AAD8AED8AFD985D98AD986D8ACD8AFD8AF') WHERE id = 48; -- إنشاء مستخدمين جدد
UPDATE permissions SET description = UNHEX('D8AAD8B9D8AFD98AD984D8A8D98AD8A7D986D8A7D8AAD8A720D8A7D984D985D8B3D8AAD8AED8AFD985D98AD986') WHERE id = 49; -- تعديل بيانات المستخدمين
UPDATE permissions SET description = UNHEX('D8A5D8AFD8A7D8B1D8A9D8A3D8AFD988D8A7D8B1D8A720D8A7D984D985D8B3D8AAD8AED8AFD985D98AD986') WHERE id = 51; -- إدارة أدوار المستخدمين
UPDATE permissions SET description = UNHEX('D8B9D8B1D8B6D8A720D8A7D984D8AAD982D8A7D8B1D98AD8B1') WHERE id = 52; -- عرض التقارير
UPDATE permissions SET description = UNHEX('D8A5D986D8B4D8A7D8A1D8AAD982D8A7D8B1D98AD8B1D8ACD8AFD98AD8AFD8A9') WHERE id = 53; -- إنشاء تقارير جديدة
UPDATE permissions SET description = UNHEX('D8AAD8B5D8AFD98AD8B1D8A720D8A7D984D8AAD982D8A7D8B1D98AD8B1') WHERE id = 54; -- تصدير التقارير
UPDATE permissions SET description = UNHEX('D8A5D8AFD8A7D8B1D8A9D8A720D8A7D984D986D8B8D8A7D985') WHERE id = 55; -- إدارة النظام
UPDATE permissions SET description = UNHEX('D8A5D986D8B4D8A7D8A1D986D8B3D8AED8A7D8ADD8AAD98AD8A7D8B7D98AD8A9') WHERE id = 56; -- إنشاء نسخ احتياطية
UPDATE permissions SET description = UNHEX('D8A7D8B3D8AAD8B9D8A7D8AFD8A9D8A720D8A7D984D986D8B3D8AED8A720D8A7D984D8A7D8ADD8AAD98AD8A7D8B7D98AD8A9') WHERE id = 57; -- استعادة النسخ الاحتياطية
UPDATE permissions SET description = UNHEX('D8A5D8AFD8A7D8B1D8A9D982D8A7D8B9D8AFD8A9D8A720D8A7D984D8A8D98AD8A7D986D8A7D8AA') WHERE id = 58; -- إدارة قاعدة البيانات
UPDATE permissions SET description = UNHEX('D8B9D8B1D8B6D8A720D8A7D984D985D984D98120D8A7D984D8B4D8AED8B5D98A') WHERE id = 59; -- عرض الملف الشخصي
UPDATE permissions SET description = UNHEX('D8AAD8B9D8AFD98AD984D8A720D8A7D984D985D984D98120D8A7D984D8B4D8AED8B5D98A') WHERE id = 60; -- تعديل الملف الشخصي
UPDATE permissions SET description = UNHEX('D8B9D8B1D8B6D8A720D8A7D984D8A5D8B4D8B9D8A7D8B1D8A7D8AA') WHERE id = 61; -- عرض الإشعارات
UPDATE permissions SET description = UNHEX('D8A5D8AFD8A7D8B1D8A9D8A720D8A7D984D8A5D8B4D8B9D8A7D8B1D8A7D8AA') WHERE id = 62; -- إدارة الإشعارات
UPDATE permissions SET description = UNHEX('D8A5D8AFD8A7D8B1D8A9D8A720D8A7D984D8AAD982D988D98AD985') WHERE id = 64; -- إدارة التقويم
UPDATE permissions SET description = UNHEX('D8B9D8B1D8B6D8A720D8A7D984D8A3D982D8B3D8A7D985') WHERE id = 65; -- عرض الأقسام
UPDATE permissions SET description = UNHEX('D8A5D8AFD8A7D8B1D8A9D8A720D8A7D984D8A3D982D8B3D8A7D985') WHERE id = 66; -- إدارة الأقسام
UPDATE permissions SET description = UNHEX('D8B9D8B1D8B6D8ACD985D98AD8B9D8A720D8A7D984D985D987D8A7D985') WHERE id = 71; -- عرض جميع المهام

-- Alternative approach: Use CONVERT function with charset
-- UPDATE permissions SET description = CONVERT('إدارة لوحة التحكم' USING utf8mb4) WHERE id = 40;

-- Update modification timestamp for first group
UPDATE permissions 
SET updated_at = UNIX_TIMESTAMP() 
WHERE id IN (40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71);

-- Verify first group results
SELECT 
    id, 
    name, 
    description, 
    permission_group
FROM permissions 
WHERE id IN (40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71)
ORDER BY id;

-- End transaction
COMMIT;

-- Success message
SELECT 'Fixed first group of encoded descriptions successfully!' as result;
