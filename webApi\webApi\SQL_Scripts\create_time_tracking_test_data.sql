-- إنشاء بيانات تجريبية لاختبار تتبع الوقت
-- Create test data for time tracking testing

USE databasetasks;
GO

PRINT '🕒 بدء إنشاء بيانات تجريبية لتتبع الوقت...'

-- ===================================================================
-- 1. التحقق من وجود الجدول
-- ===================================================================

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='time_tracking_entries' AND xtype='U')
BEGIN
    PRINT '❌ جدول time_tracking_entries غير موجود!'
    PRINT 'يرجى تشغيل script إنشاء الجدول أولاً'
    RETURN
END

PRINT '✅ جدول time_tracking_entries موجود'

-- ===================================================================
-- 2. التحقق من وجود مهام ومستخدمين
-- ===================================================================

DECLARE @TaskCount INT = (SELECT COUNT(*) FROM tasks WHERE is_deleted = 0)
DECLARE @UserCount INT = (SELECT COUNT(*) FROM users WHERE is_deleted = 0)

PRINT '📊 عدد المهام المتاحة: ' + CAST(@TaskCount AS NVARCHAR(10))
PRINT '👥 عدد المستخدمين المتاحين: ' + CAST(@UserCount AS NVARCHAR(10))

IF @TaskCount = 0
BEGIN
    PRINT '⚠️ لا توجد مهام في النظام. يرجى إضافة مهام أولاً'
    RETURN
END

IF @UserCount = 0
BEGIN
    PRINT '⚠️ لا يوجد مستخدمين في النظام. يرجى إضافة مستخدمين أولاً'
    RETURN
END

-- ===================================================================
-- 3. مسح البيانات التجريبية السابقة (اختياري)
-- ===================================================================

PRINT '🧹 مسح البيانات التجريبية السابقة...'
DELETE FROM time_tracking_entries WHERE description LIKE '%تجريبي%' OR description LIKE '%test%'
PRINT '✅ تم مسح البيانات التجريبية السابقة'

-- ===================================================================
-- 4. إنشاء بيانات تجريبية
-- ===================================================================

PRINT '📝 إنشاء بيانات تجريبية لتتبع الوقت...'

DECLARE @CurrentTime BIGINT = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
DECLARE @OneDayAgo BIGINT = @CurrentTime - (24 * 60 * 60)
DECLARE @TwoDaysAgo BIGINT = @CurrentTime - (2 * 24 * 60 * 60)
DECLARE @OneWeekAgo BIGINT = @CurrentTime - (7 * 24 * 60 * 60)

-- الحصول على أول مهمة ومستخدم متاحين
DECLARE @FirstTaskId INT = (SELECT TOP 1 id FROM tasks WHERE is_deleted = 0 ORDER BY id)
DECLARE @FirstUserId INT = (SELECT TOP 1 id FROM users WHERE is_deleted = 0 ORDER BY id)
DECLARE @SecondUserId INT = (SELECT TOP 1 id FROM users WHERE is_deleted = 0 AND id != @FirstUserId ORDER BY id)

-- إذا لم يوجد مستخدم ثاني، استخدم الأول
IF @SecondUserId IS NULL
    SET @SecondUserId = @FirstUserId

-- ===================================================================
-- 5. إدخالات تتبع وقت مكتملة (منتهية)
-- ===================================================================

PRINT '⏰ إنشاء إدخالات تتبع وقت مكتملة...'

-- إدخال 1: عمل لمدة ساعتين منذ أسبوع
INSERT INTO time_tracking_entries (
    task_id, user_id, start_time, end_time, duration, description, created_at, updated_at, is_deleted
) VALUES (
    @FirstTaskId, 
    @FirstUserId, 
    @OneWeekAgo, 
    @OneWeekAgo + (2 * 60 * 60), -- ساعتين
    2 * 60 * 60, -- 7200 ثانية
    'عمل تجريبي - تطوير الميزة الأساسية',
    @OneWeekAgo,
    @OneWeekAgo + (2 * 60 * 60),
    0
)

-- إدخال 2: عمل لمدة 3 ساعات منذ يومين
INSERT INTO time_tracking_entries (
    task_id, user_id, start_time, end_time, duration, description, created_at, updated_at, is_deleted
) VALUES (
    @FirstTaskId, 
    @SecondUserId, 
    @TwoDaysAgo, 
    @TwoDaysAgo + (3 * 60 * 60), -- 3 ساعات
    3 * 60 * 60, -- 10800 ثانية
    'عمل تجريبي - اختبار الوظائف',
    @TwoDaysAgo,
    @TwoDaysAgo + (3 * 60 * 60),
    0
)

-- إدخال 3: عمل لمدة ساعة ونصف أمس
INSERT INTO time_tracking_entries (
    task_id, user_id, start_time, end_time, duration, description, created_at, updated_at, is_deleted
) VALUES (
    @FirstTaskId, 
    @FirstUserId, 
    @OneDayAgo, 
    @OneDayAgo + (90 * 60), -- ساعة ونصف
    90 * 60, -- 5400 ثانية
    'عمل تجريبي - مراجعة الكود',
    @OneDayAgo,
    @OneDayAgo + (90 * 60),
    0
)

-- ===================================================================
-- 6. إدخال تتبع وقت نشط (غير منتهي)
-- ===================================================================

PRINT '🔄 إنشاء إدخال تتبع وقت نشط...'

-- إدخال نشط: بدأ منذ ساعة ولم ينته بعد
INSERT INTO time_tracking_entries (
    task_id, user_id, start_time, end_time, duration, description, created_at, updated_at, is_deleted
) VALUES (
    @FirstTaskId, 
    @SecondUserId, 
    @CurrentTime - (60 * 60), -- بدأ منذ ساعة
    NULL, -- لم ينته بعد
    NULL, -- لا توجد مدة محددة بعد
    'عمل تجريبي نشط - تطوير ميزة جديدة',
    @CurrentTime - (60 * 60),
    NULL,
    0
)

-- ===================================================================
-- 7. إدخالات إضافية لمهام مختلفة (إذا توفرت)
-- ===================================================================

PRINT '📚 إنشاء إدخالات إضافية للمهام الأخرى...'

-- الحصول على مهمة ثانية إذا توفرت
DECLARE @SecondTaskId INT = (SELECT TOP 1 id FROM tasks WHERE is_deleted = 0 AND id != @FirstTaskId ORDER BY id)

IF @SecondTaskId IS NOT NULL
BEGIN
    -- إدخال لمهمة ثانية
    INSERT INTO time_tracking_entries (
        task_id, user_id, start_time, end_time, duration, description, created_at, updated_at, is_deleted
    ) VALUES (
        @SecondTaskId, 
        @FirstUserId, 
        @TwoDaysAgo + (4 * 60 * 60), 
        @TwoDaysAgo + (6 * 60 * 60), -- ساعتين
        2 * 60 * 60,
        'عمل تجريبي - مهمة ثانية',
        @TwoDaysAgo + (4 * 60 * 60),
        @TwoDaysAgo + (6 * 60 * 60),
        0
    )
    PRINT '✅ تم إنشاء إدخال للمهمة الثانية'
END

-- ===================================================================
-- 8. التحقق من النتائج
-- ===================================================================

PRINT '📊 التحقق من البيانات المُنشأة...'

DECLARE @TotalEntries INT = (SELECT COUNT(*) FROM time_tracking_entries WHERE is_deleted = 0)
DECLARE @ActiveEntries INT = (SELECT COUNT(*) FROM time_tracking_entries WHERE end_time IS NULL AND is_deleted = 0)
DECLARE @CompletedEntries INT = (SELECT COUNT(*) FROM time_tracking_entries WHERE end_time IS NOT NULL AND is_deleted = 0)

PRINT '📈 إجمالي الإدخالات: ' + CAST(@TotalEntries AS NVARCHAR(10))
PRINT '🔄 الإدخالات النشطة: ' + CAST(@ActiveEntries AS NVARCHAR(10))
PRINT '✅ الإدخالات المكتملة: ' + CAST(@CompletedEntries AS NVARCHAR(10))

-- عرض عينة من البيانات
PRINT '📋 عينة من البيانات المُنشأة:'
SELECT TOP 5
    id,
    task_id,
    user_id,
    start_time,
    end_time,
    duration,
    description,
    CASE WHEN end_time IS NULL THEN 'نشط' ELSE 'مكتمل' END as status
FROM time_tracking_entries 
WHERE is_deleted = 0
ORDER BY created_at DESC

PRINT '🎉 تم إنشاء البيانات التجريبية لتتبع الوقت بنجاح!'
PRINT '💡 يمكنك الآن اختبار وظائف تتبع الوقت في التطبيق'

GO
