-- سكريبت تنظيف التكرارات المتقدم - يتعامل مع القيود المرجعية
-- يجب تشغيل هذا السكريبت لحل مشاكل التكرار

USE [databasetasks]
GO

PRINT '🧹 بدء تنظيف التكرارات المتقدم...'

-- التحقق من وجود الجدول
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'permissions')
BEGIN
    PRINT '❌ جدول permissions غير موجود.'
    RETURN
END

-- عرض إحصائيات قبل التنظيف
PRINT ''
PRINT '📊 إحصائيات قبل التنظيف:'
SELECT 
    COUNT(*) as [إجمالي الصلاحيات],
    COUNT(DISTINCT name) as [الصلاحيات الفريدة],
    COUNT(*) - COUNT(DISTINCT name) as [عدد التكرارات]
FROM permissions

-- عرض التكرارات المكتشفة
PRINT ''
PRINT '🔍 التكرارات المكتشفة:'
SELECT 
    name as [اسم الصلاحية],
    COUNT(*) as [عدد التكرارات],
    STRING_AGG(CAST(id AS NVARCHAR(10)), ', ') as [معرفات التكرارات]
FROM permissions 
GROUP BY name 
HAVING COUNT(*) > 1
ORDER BY COUNT(*) DESC, name

-- الخطوة 1: تحديث المراجع في الجداول الأخرى
PRINT ''
PRINT '🔄 تحديث المراجع في الجداول الأخرى...'

-- تحديث جدول role_default_permissions
UPDATE rdp 
SET permission_id = latest.latest_id
FROM role_default_permissions rdp
INNER JOIN (
    -- الحصول على أحدث ID لكل صلاحية مكررة
    SELECT 
        p1.id as old_id,
        p2.latest_id
    FROM permissions p1
    INNER JOIN (
        SELECT 
            name,
            MAX(id) as latest_id
        FROM permissions 
        GROUP BY name
        HAVING COUNT(*) > 1
    ) p2 ON p1.name = p2.name
    WHERE p1.id != p2.latest_id
) latest ON rdp.permission_id = latest.old_id

DECLARE @UpdatedReferences INT = @@ROWCOUNT
PRINT '✅ تم تحديث ' + CAST(@UpdatedReferences AS NVARCHAR(10)) + ' مرجع في role_default_permissions'

-- تحديث جدول user_permissions إذا كان موجوداً
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'user_permissions')
BEGIN
    UPDATE up 
    SET permission_id = latest.latest_id
    FROM user_permissions up
    INNER JOIN (
        SELECT 
            p1.id as old_id,
            p2.latest_id
        FROM permissions p1
        INNER JOIN (
            SELECT 
                name,
                MAX(id) as latest_id
            FROM permissions 
            GROUP BY name
            HAVING COUNT(*) > 1
        ) p2 ON p1.name = p2.name
        WHERE p1.id != p2.latest_id
    ) latest ON up.permission_id = latest.old_id

    DECLARE @UpdatedUserPermissions INT = @@ROWCOUNT
    PRINT '✅ تم تحديث ' + CAST(@UpdatedUserPermissions AS NVARCHAR(10)) + ' مرجع في user_permissions'
END

-- الخطوة 2: حذف التكرارات بعد تحديث المراجع
PRINT ''
PRINT '🗑️ حذف التكرارات بعد تحديث المراجع...'

-- استخدام CTE لتحديد التكرارات
;WITH DuplicatePermissions AS (
    SELECT
        id,
        name,
        ROW_NUMBER() OVER (PARTITION BY name ORDER BY id DESC) as rn
    FROM permissions
)
DELETE FROM permissions
WHERE id IN (
    SELECT id
    FROM DuplicatePermissions
    WHERE rn > 1
);;

DECLARE @DeletedCount INT = @@ROWCOUNT
PRINT '✅ تم حذف ' + CAST(@DeletedCount AS NVARCHAR(10)) + ' صلاحية مكررة'

-- الخطوة 3: تنظيف البيانات الغريبة
PRINT ''
PRINT '🧽 تنظيف البيانات الغريبة...'

-- حذف الصلاحيات ذات الأوصاف الغريبة (بعد تحديث المراجع)
-- أولاً نحدث المراجع للصلاحيات الغريبة
UPDATE rdp 
SET permission_id = NULL
FROM role_default_permissions rdp
INNER JOIN permissions p ON rdp.permission_id = p.id
WHERE p.description = 'q' 
   OR p.description IS NULL 
   OR p.description = ''
   OR p.permission_group = 'CustomRoles'

-- الآن يمكننا حذف الصلاحيات الغريبة
DELETE FROM permissions 
WHERE description = 'q' 
   OR description IS NULL 
   OR description = ''
   OR permission_group = 'CustomRoles';

DECLARE @CleanedCount INT = @@ROWCOUNT
IF @CleanedCount > 0
BEGIN
    PRINT '✅ تم حذف ' + CAST(@CleanedCount AS NVARCHAR(10)) + ' صلاحية ذات بيانات غريبة'
END
ELSE
BEGIN
    PRINT '✅ لا توجد بيانات غريبة للحذف'
END

-- الخطوة 4: إنشاء الفهرس الفريد
PRINT ''
PRINT '🔧 إنشاء الفهرس الفريد...'

-- التحقق من عدم وجود تكرارات قبل إنشاء الفهرس
DECLARE @RemainingDuplicates INT
SELECT @RemainingDuplicates = COUNT(*) 
FROM (
    SELECT name 
    FROM permissions 
    GROUP BY name 
    HAVING COUNT(*) > 1
) as duplicates

IF @RemainingDuplicates = 0
BEGIN
    -- إنشاء فهرس فريد لمنع التكرار المستقبلي
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_permissions_name_unique')
    BEGIN
        CREATE UNIQUE INDEX IX_permissions_name_unique ON permissions (name)
        PRINT '✅ تم إنشاء فهرس فريد لمنع التكرار المستقبلي'
    END
    ELSE
    BEGIN
        PRINT '✅ الفهرس الفريد موجود بالفعل'
    END
END
ELSE
BEGIN
    PRINT '⚠️ لا يمكن إنشاء الفهرس الفريد - لا تزال هناك تكرارات'
END

-- عرض إحصائيات بعد التنظيف
PRINT ''
PRINT '📊 إحصائيات بعد التنظيف:'
SELECT 
    COUNT(*) as [إجمالي الصلاحيات],
    COUNT(DISTINCT name) as [الصلاحيات الفريدة],
    COUNT(*) - COUNT(DISTINCT name) as [عدد التكرارات المتبقية],
    COUNT(DISTINCT permission_group) as [عدد المجموعات]
FROM permissions

-- فحص التكرارات المتبقية
PRINT ''
PRINT '🔍 فحص التكرارات المتبقية:'
SELECT @RemainingDuplicates = COUNT(*) 
FROM (
    SELECT name 
    FROM permissions 
    GROUP BY name 
    HAVING COUNT(*) > 1
) as duplicates

IF @RemainingDuplicates = 0
BEGIN
    PRINT '✅ ممتاز! لا توجد تكرارات متبقية'
END
ELSE
BEGIN
    PRINT '⚠️ تحذير: لا تزال هناك ' + CAST(@RemainingDuplicates AS NVARCHAR(10)) + ' صلاحية مكررة'
    
    -- عرض التكرارات المتبقية
    SELECT 
        name as [الصلاحية المكررة],
        COUNT(*) as [عدد التكرارات]
    FROM permissions 
    GROUP BY name 
    HAVING COUNT(*) > 1
END

-- إنشاء تقرير نهائي
PRINT ''
PRINT '📄 تقرير التنظيف النهائي:'
PRINT '================================'
PRINT '• المراجع المحدثة: ' + CAST(@UpdatedReferences AS NVARCHAR(10))
PRINT '• الصلاحيات المحذوفة: ' + CAST(@DeletedCount AS NVARCHAR(10))
PRINT '• البيانات الغريبة المحذوفة: ' + CAST(@CleanedCount AS NVARCHAR(10))
PRINT '• التكرارات المتبقية: ' + CAST(@RemainingDuplicates AS NVARCHAR(10))

PRINT ''
PRINT '🎉 تم الانتهاء من تنظيف جدول الصلاحيات بنجاح!'
PRINT 'يمكنك الآن إضافة صلاحيات جديدة بأمان.'

GO
