USE databasetasks;

-- التحقق من النتائج بعد الإصلاح بالإنجليزية
SELECT TOP 15
    id, 
    name, 
    description, 
    permission_group
FROM permissions 
WHERE id IN (40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55)
ORDER BY id;

-- فحص عدد السجلات التي تم إصلاحها
SELECT COUNT(*) as total_fixed_records
FROM permissions 
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
)
AND description NOT LIKE '%O%' 
AND description NOT LIKE '%U%';
