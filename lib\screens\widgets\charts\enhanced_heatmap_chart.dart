import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/advanced_filter_options.dart';
import 'package:flutter_application_2/models/chart_enums.dart';

import '../../../constants/app_styles.dart';

import 'unified_filter_export_widget.dart';

/// نموذج بيانات خلية الخريطة الحرارية
class HeatmapCell {
  /// قيمة الخلية
  final double value;

  /// تسمية الخلية (اختياري)
  final String? label;

  /// لون الخلية (اختياري)
  final Color? color;

  /// إنشاء نموذج بيانات خلية الخريطة الحرارية
  const HeatmapCell({
    required this.value,
    this.label,
    this.color,
  });
}

/// مكون خريطة حرارية محسنة
///
/// يوفر هذا المكون خريطة حرارية متقدمة مع دعم للتصفية والتصدير
class EnhancedHeatmapChart extends StatefulWidget {
  /// بيانات المخطط
  /// مصفوفة ثنائية الأبعاد من خلايا الخريطة الحرارية
  final List<List<HeatmapCell>> data;

  /// عنوان المخطط
  final String? title;

  /// تسميات المحور س
  final List<String>? xLabels;

  /// تسميات المحور ص
  final List<String>? yLabels;

  /// عنوان المحور س
  final String? xAxisTitle;

  /// عنوان المحور ص
  final String? yAxisTitle;

  /// القيمة الدنيا للتدرج اللوني
  final double? minValue;

  /// القيمة القصوى للتدرج اللوني
  final double? maxValue;

  /// لون القيمة الدنيا
  final Color lowColor;

  /// لون القيمة المتوسطة
  final Color midColor;

  /// لون القيمة القصوى
  final Color highColor;

  /// هل يتم عرض القيم داخل الخلايا
  final bool showValues;

  /// هل يتم عرض حدود الخلايا
  final bool showBorders;

  /// هل يتم عرض مقياس الألوان
  final bool showColorScale;

  /// دالة التصفية (اختياري)
  final Function(
          DateTime? startDate, DateTime? endDate, TimeFilterType filterType)?
      onFilterChanged;

  /// دالة التصدير (اختياري)
  final Function(String format)? onExport;

  /// دالة تغيير نوع المخطط (اختياري)
  final Function(ChartType)? onChartTypeChanged;

  /// إظهار خيارات التصفية (اختياري)
  final bool showFilterOptions;

  /// إظهار خيارات التصدير (اختياري)
  final bool showExportOptions;

  /// إظهار خيارات تغيير نوع المخطط (اختياري)
  final bool showChartTypeOptions;

  /// تنسيقات التصدير المدعومة (اختياري)
  final List<String> supportedExportFormats;

  /// أنواع المخططات المدعومة (اختياري)
  final List<ChartType> supportedChartTypes;

  /// نوع المخطط
  final ChartType chartType;

  /// خيارات التصفية المتقدمة
  final AdvancedFilterOptions advancedFilterOptions;

  /// إنشاء مكون خريطة حرارية محسنة
  const EnhancedHeatmapChart({
    super.key,
    required this.data,
    this.title,
    this.xLabels,
    this.yLabels,
    this.xAxisTitle,
    this.yAxisTitle,
    this.minValue,
    this.maxValue,
    this.lowColor = const Color(0xFFEFF3FF),
    this.midColor = const Color(0xFF6B8FF7),
    this.highColor = const Color(0xFF0747A6),
    this.showValues = true,
    this.showBorders = true,
    this.showColorScale = true,
    this.onFilterChanged,
    this.onExport,
    this.onChartTypeChanged,
    this.showFilterOptions = false,
    this.showExportOptions = false,
    this.showChartTypeOptions = false,
    this.supportedExportFormats = const ['pdf', 'excel', 'csv', 'image'],
    this.supportedChartTypes = const [
      ChartType.heatmap,
      ChartType.bar,
      ChartType.table,
    ],
    required this.chartType,
    required this.advancedFilterOptions,
  });

  @override
  State<EnhancedHeatmapChart> createState() => _EnhancedHeatmapChartState();
}

class _EnhancedHeatmapChartState extends State<EnhancedHeatmapChart> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان المخطط وأزرار التصفية والتصدير
        if (widget.title != null ||
            widget.showFilterOptions ||
            widget.showExportOptions)
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildHeader(),
          ),

        // الخريطة الحرارية
        Expanded(
          child: _buildHeatmap(),
        ),

        // عناوين المحاور
        if (widget.xAxisTitle != null || widget.yAxisTitle != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (widget.yAxisTitle != null)
                  Text(
                    widget.yAxisTitle!,
                    style: AppStyles.caption,
                  )
                else
                  const SizedBox.shrink(),
                if (widget.xAxisTitle != null)
                  Text(
                    widget.xAxisTitle!,
                    style: AppStyles.caption,
                  )
                else
                  const SizedBox.shrink(),
              ],
            ),
          ),

        // مقياس الألوان
        if (widget.showColorScale)
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: _buildColorScale(),
          ),
      ],
    );
  }

  /// بناء رأس المخطط
  Widget _buildHeader() {
    // استخدام مكون UnifiedFilterExportWidget بدلاً من الأكواد المكررة
    return UnifiedFilterExportWidget(
      title: widget.title ?? 'خريطة حرارية',
      chartKey: 'heatmap_chart',
      onFilterChanged: (startDate, endDate, filterType, chartKey) {
        if (widget.onFilterChanged != null) {
          widget.onFilterChanged!(startDate, endDate, filterType);
        }
      },
      onExport: (format, title) {
        if (widget.onExport != null) {
          widget.onExport!(format);
        }
      },
      onChartTypeChanged: widget.onChartTypeChanged != null
          ? (chartType, chartKey) {
              widget.onChartTypeChanged!(chartType);
            }
          : null,
      showFilter: widget.showFilterOptions,
      showExport: widget.showExportOptions,
      showChartTypeSelector: widget.showChartTypeOptions,
      supportedChartTypes: widget.supportedChartTypes,
      filterType: TimeFilterType.month,
      startDate: DateTime.now().subtract(const Duration(days: 30)),
      endDate: DateTime.now(),
      currentChartType: ChartType.heatmap,
      chartType: widget.chartType,
      advancedFilterOptions: widget.advancedFilterOptions,
    );
  }

  // تم تعليق هذه الدوال لأنها لم تعد مستخدمة بعد استخدام UnifiedFilterExportWidget

  /*
  /// بناء أزرار تغيير نوع المخطط
  Widget _buildChartTypeButtons() {
    // استخدام أزرار مباشرة بدلاً من مكون ChartTypeSelector لتجنب التكرار
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      // child: Row(
      //   mainAxisAlignment: MainAxisAlignment.center,
      //   children: widget.supportedChartTypes.map((type) {
      //     final isSelected = type == ChartType.heatmap;
      //     return Padding(
      //       padding: const EdgeInsets.symmetric(horizontal: 2.0),
      //       child: ChoiceChip(
      //         label: Icon(
      //           ChartTypeUtils.getChartTypeIcon(type),
      //           size: 20,
      //           color: isSelected ? Colors.white : Colors.grey.shade700,
      //         ),
      //         selected: isSelected,
      //         onSelected: (selected) {
      //           if (selected) {
      //             widget.onChartTypeChanged!(type);
      //           }
      //         },
      //         backgroundColor: Colors.grey.shade200,
      //         selectedColor: AppColors.primary,
      //         tooltip: ChartTypeUtils.getChartTypeLabel(type),
      //       ),
      //     );
      //   }).toList(),
      // ),
    );
  }

  /// عرض مربع حوار التصفية
  void _showFilterDialog() {
    // يمكن هنا عرض مربع حوار للتصفية
  }
  */

  /// بناء الخريطة الحرارية
  Widget _buildHeatmap() {
    // تحديد القيم الدنيا والقصوى
    double minValue = widget.minValue ?? double.infinity;
    double maxValue = widget.maxValue ?? -double.infinity;

    if (minValue == double.infinity || maxValue == -double.infinity) {
      for (final row in widget.data) {
        for (final cell in row) {
          if (cell.value < minValue) minValue = cell.value;
          if (cell.value > maxValue) maxValue = cell.value;
        }
      }
    }

    // عدد الصفوف والأعمدة
    final rowCount = widget.data.length;
    final colCount = rowCount > 0 ? widget.data[0].length : 0;

    return Column(
      children: [
        // تسميات الأعمدة (المحور س)
        if (widget.xLabels != null)
          SizedBox(
            height: 40,
            child: Row(
              children: [
                // مساحة فارغة للزاوية العلوية اليسرى
                SizedBox(
                  width: widget.yLabels != null ? 80 : 0,
                ),
                // تسميات الأعمدة
                Expanded(
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: colCount,
                    itemBuilder: (context, colIndex) {
                      return SizedBox(
                        width: 60,
                        child: Center(
                          child: Text(
                            colIndex < widget.xLabels!.length
                                ? widget.xLabels![colIndex]
                                : '',
                            style: AppStyles.caption,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),

        // الخريطة الحرارية والتسميات الجانبية
        Expanded(
          child: Row(
            children: [
              // تسميات الصفوف (المحور ص)
              if (widget.yLabels != null)
                SizedBox(
                  width: 80,
                  child: ListView.builder(
                    itemCount: rowCount,
                    itemBuilder: (context, rowIndex) {
                      return SizedBox(
                        height: 60,
                        child: Center(
                          child: Text(
                            rowIndex < widget.yLabels!.length
                                ? widget.yLabels![rowIndex]
                                : '',
                            style: AppStyles.caption,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      );
                    },
                  ),
                ),

              // الخريطة الحرارية
              Expanded(
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: colCount,
                    childAspectRatio: 1.0,
                  ),
                  itemCount: rowCount * colCount,
                  itemBuilder: (context, index) {
                    final rowIndex = index ~/ colCount;
                    final colIndex = index % colCount;

                    if (rowIndex >= widget.data.length ||
                        colIndex >= widget.data[rowIndex].length) {
                      return const SizedBox.shrink();
                    }

                    final cell = widget.data[rowIndex][colIndex];
                    final normalizedValue =
                        (cell.value - minValue) / (maxValue - minValue);

                    // تحديد لون الخلية
                    Color cellColor;
                    if (cell.color != null) {
                      cellColor = cell.color!;
                    } else {
                      if (normalizedValue <= 0.5) {
                        // تدرج من اللون الأدنى إلى اللون المتوسط
                        final t = normalizedValue * 2;
                        cellColor =
                            Color.lerp(widget.lowColor, widget.midColor, t)!;
                      } else {
                        // تدرج من اللون المتوسط إلى اللون الأعلى
                        final t = (normalizedValue - 0.5) * 2;
                        cellColor =
                            Color.lerp(widget.midColor, widget.highColor, t)!;
                      }
                    }

                    // تحديد لون النص
                    final brightness =
                        ThemeData.estimateBrightnessForColor(cellColor);
                    final textColor = brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black;

                    return Container(
                      margin: EdgeInsets.all(widget.showBorders ? 1 : 0),
                      decoration: BoxDecoration(
                        color: cellColor,
                        border: widget.showBorders
                            ? Border.all(
                                color: Colors.grey.shade300, width: 0.5)
                            : null,
                      ),
                      child: Center(
                        child: widget.showValues
                            ? Text(
                                cell.label ?? cell.value.toStringAsFixed(1),
                                style: TextStyle(
                                  color: textColor,
                                  fontSize: 12,
                                ),
                              )
                            : null,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء مقياس الألوان
  Widget _buildColorScale() {
    return Row(
      children: [
        Text('الأدنى', style: AppStyles.caption),
        const SizedBox(width: 8),
        Expanded(
          child: Container(
            height: 16,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [widget.lowColor, widget.midColor, widget.highColor],
                stops: const [0.0, 0.5, 1.0],
              ),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text('الأعلى', style: AppStyles.caption),
      ],
    );
  }
}
