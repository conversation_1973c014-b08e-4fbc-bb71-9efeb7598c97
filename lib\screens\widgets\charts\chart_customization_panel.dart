import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/chart_enums.dart';

/// مكون لوحة تخصيص المخططات
///
/// يوفر هذا المكون واجهة مستخدم لتخصيص مظهر وسلوك المخططات
class ChartCustomizationPanel extends StatefulWidget {
  /// نوع المخطط
  final ChartType chartType;

  /// ألوان المخطط الحالية
  final Map<String, Color> currentColors;

  /// خيارات العرض الحالية
  final Map<String, bool> currentDisplayOptions;

  /// دالة استدعاء عند تغيير الألوان
  final Function(Map<String, Color> colors) onColorsChanged;

  /// دالة استدعاء عند تغيير خيارات العرض
  final Function(Map<String, bool> options) onDisplayOptionsChanged;

  /// إنشاء مكون لوحة تخصيص المخططات
  const ChartCustomizationPanel({
    super.key,
    required this.chartType,
    required this.currentColors,
    required this.currentDisplayOptions,
    required this.onColorsChanged,
    required this.onDisplayOptionsChanged,
  });

  @override
  State<ChartCustomizationPanel> createState() => _ChartCustomizationPanelState();
}

class _ChartCustomizationPanelState extends State<ChartCustomizationPanel> {
  late Map<String, Color> _colors;
  late Map<String, bool> _displayOptions;

  @override
  void initState() {
    super.initState();
    _colors = Map.from(widget.currentColors);
    _displayOptions = Map.from(widget.currentDisplayOptions);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'تخصيص المخطط',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // قسم الألوان
          const Text(
            'الألوان',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildColorSection(),
          const SizedBox(height: 16),
          
          // قسم خيارات العرض
          const Text(
            'خيارات العرض',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildDisplayOptionsSection(),
          const SizedBox(height: 24),
          
          // أزرار الإجراءات
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('إلغاء'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () {
                  widget.onColorsChanged(_colors);
                  widget.onDisplayOptionsChanged(_displayOptions);
                  Navigator.of(context).pop();
                },
                child: const Text('تطبيق'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قسم الألوان
  Widget _buildColorSection() {
    final List<Widget> colorItems = [];
    
    switch (widget.chartType) {
      case ChartType.funnel:
        // ألوان مخطط القمع (المراحل)
        final stageColors = {
          'جديدة': Colors.blue,
          'قيد التنفيذ': Colors.orange,
          'مراجعة': Colors.purple,
          'مكتملة': Colors.green,
          'ملغاة': Colors.red,
          'معلقة': Colors.amber,
        };
        
        stageColors.forEach((stage, defaultColor) {
          colorItems.add(_buildColorItem(stage, _colors[stage] ?? defaultColor, (color) {
            setState(() {
              _colors[stage] = color;
            });
          }));
        });
        break;
        
      case ChartType.gantt:
        // ألوان مخطط جانت (حالات المهام)
        final taskStatusColors = {
          'جديدة': Colors.blue,
          'قيد التنفيذ': Colors.orange,
          'مراجعة': Colors.purple,
          'مكتملة': Colors.green,
          'ملغاة': Colors.red,
          'معلقة': Colors.amber,
        };
        
        taskStatusColors.forEach((status, defaultColor) {
          colorItems.add(_buildColorItem(status, _colors[status] ?? defaultColor, (color) {
            setState(() {
              _colors[status] = color;
            });
          }));
        });
        break;
        
      case ChartType.sankey:
        // ألوان مخطط سانكي (الأقسام)
        final departmentColors = {
          'الإدارة': Colors.blue,
          'المبيعات': Colors.green,
          'التسويق': Colors.orange,
          'تطوير المنتجات': Colors.purple,
          'خدمة العملاء': Colors.teal,
          'الموارد البشرية': Colors.amber,
        };
        
        departmentColors.forEach((department, defaultColor) {
          colorItems.add(_buildColorItem(department, _colors[department] ?? defaultColor, (color) {
            setState(() {
              _colors[department] = color;
            });
          }));
        });
        break;
        
      default:
        // ألوان افتراضية للمخططات الأخرى
        colorItems.add(const Text('لا توجد ألوان قابلة للتخصيص لهذا النوع من المخططات'));
    }
    
    return Wrap(
      spacing: 16,
      runSpacing: 16,
      children: colorItems,
    );
  }

  /// بناء قسم خيارات العرض
  Widget _buildDisplayOptionsSection() {
    final List<Widget> optionItems = [];
    
    switch (widget.chartType) {
      case ChartType.funnel:
        // خيارات عرض مخطط القمع
        optionItems.add(_buildSwitchItem(
          'عرض القيم', 
          _displayOptions['showValues'] ?? true, 
          (value) {
            setState(() {
              _displayOptions['showValues'] = value;
            });
          }
        ));
        
        optionItems.add(_buildSwitchItem(
          'عرض النسب المئوية', 
          _displayOptions['showPercentages'] ?? true, 
          (value) {
            setState(() {
              _displayOptions['showPercentages'] = value;
            });
          }
        ));
        break;
        
      case ChartType.gantt:
        // خيارات عرض مخطط جانت
        optionItems.add(_buildSwitchItem(
          'عرض نسبة الإكمال', 
          _displayOptions['showCompletionPercentage'] ?? true, 
          (value) {
            setState(() {
              _displayOptions['showCompletionPercentage'] = value;
            });
          }
        ));
        
        optionItems.add(_buildSwitchItem(
          'عرض التواريخ', 
          _displayOptions['showDates'] ?? true, 
          (value) {
            setState(() {
              _displayOptions['showDates'] = value;
            });
          }
        ));
        break;
        
      case ChartType.sankey:
        // خيارات عرض مخطط سانكي
        optionItems.add(_buildSwitchItem(
          'عرض القيم على الروابط', 
          _displayOptions['showLinkValues'] ?? false, 
          (value) {
            setState(() {
              _displayOptions['showLinkValues'] = value;
            });
          }
        ));
        
        optionItems.add(_buildSwitchItem(
          'عرض أسماء العقد', 
          _displayOptions['showNodeNames'] ?? true, 
          (value) {
            setState(() {
              _displayOptions['showNodeNames'] = value;
            });
          }
        ));
        break;
        
      default:
        // خيارات افتراضية للمخططات الأخرى
        optionItems.add(const Text('لا توجد خيارات عرض قابلة للتخصيص لهذا النوع من المخططات'));
    }
    
    return Column(
      children: optionItems,
    );
  }

  /// بناء عنصر اختيار اللون
  Widget _buildColorItem(String label, Color color, Function(Color) onColorChanged) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(label),
        const SizedBox(width: 8),
        GestureDetector(
          onTap: () {
            _showColorPicker(color, onColorChanged);
          },
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.grey),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء عنصر مفتاح التبديل
  Widget _buildSwitchItem(String label, bool value, Function(bool) onChanged) {
    return Row(
      children: [
        Expanded(child: Text(label)),
        Switch(
          value: value,
          onChanged: onChanged,
        ),
      ],
    );
  }

  /// عرض منتقي الألوان
  void _showColorPicker(Color initialColor, Function(Color) onColorSelected) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر لونًا'),
        content: SingleChildScrollView(
          child: _ColorPicker(
            initialColor: initialColor,
            onColorSelected: onColorSelected,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }
}

/// مكون منتقي الألوان المبسط
class _ColorPicker extends StatelessWidget {
  final Color initialColor;
  final Function(Color) onColorSelected;

  const _ColorPicker({
    required this.initialColor,
    required this.onColorSelected,
  });

  @override
  Widget build(BuildContext context) {
    // قائمة الألوان المتاحة
    final List<Color> colors = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.deepPurple,
      Colors.indigo,
      Colors.blue,
      Colors.lightBlue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lightGreen,
      Colors.lime,
      Colors.yellow,
      Colors.amber,
      Colors.orange,
      Colors.deepOrange,
      Colors.brown,
      Colors.grey,
      Colors.blueGrey,
      Colors.black,
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: colors.map((color) {
        return GestureDetector(
          onTap: () {
            onColorSelected(color);
            Navigator.of(context).pop();
          },
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: color == initialColor ? Colors.white : Colors.transparent,
                width: 2,
              ),
              boxShadow: [
                if (color == initialColor)
                  BoxShadow(
                    color: Colors.black.withAlpha(128),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}
