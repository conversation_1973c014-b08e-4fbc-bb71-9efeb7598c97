-- إضافة صلاحية عرض مهام الإدارة والأقسام التابعة لها
-- تاريخ الإنشاء: 2025-01-12
-- الهدف: إضافة صلاحية tasks.view_tasks_in_department لعرض مهام الإدارة وجميع الأقسام التابعة لها

USE [databasetasks]
GO

PRINT '🏢 بدء إضافة صلاحية عرض مهام الإدارة والأقسام التابعة...'
PRINT '=================================================='

-- التحقق من وجود الصلاحية مسبقاً
IF EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.view_tasks_in_department')
BEGIN
    PRINT '⚠️ صلاحية tasks.view_tasks_in_department موجودة مسبقاً'
    
    -- عرض تفاصيل الصلاحية الموجودة
    SELECT 
        [name] as 'اسم الصلاحية',
        [description] as 'الوصف',
        [permission_group] as 'المجموعة',
        [category] as 'الفئة',
        [level] as 'المستوى',
        [is_active] as 'نشطة',
        [created_at] as 'تاريخ الإنشاء'
    FROM [dbo].[permissions] 
    WHERE [name] = 'tasks.view_tasks_in_department'
END
ELSE
BEGIN
    PRINT '✅ إضافة صلاحية جديدة: tasks.view_tasks_in_department'
    
    -- إضافة الصلاحية الجديدة
    INSERT INTO [dbo].[permissions] (
        [name], 
        [description], 
        [permission_group], 
        [category], 
        [level], 
        [icon], 
        [color], 
        [is_default], 
        [is_active], 
        [created_at], 
        [updated_at]
    )
    VALUES (
        'tasks.view_tasks_in_department',
        N'عرض جميع مهام الإدارة والأقسام التابعة لها (هرمية)',
        'tasks',
        N'إدارة المهام',
        2,
        'business',
        '#2196F3',
        0,
        1,
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()),
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
    )
    
    PRINT '✅ تم إضافة صلاحية tasks.view_tasks_in_department بنجاح'
END

PRINT ''
PRINT '📊 ملخص صلاحيات المهام المتعلقة بالعرض:'
SELECT 
    [name] as 'اسم الصلاحية',
    [description] as 'الوصف',
    [level] as 'المستوى',
    [is_active] as 'نشطة'
FROM [dbo].[permissions] 
WHERE [permission_group] = 'tasks' 
AND ([name] LIKE '%view%' OR [name] LIKE '%access%')
ORDER BY [level], [name]

PRINT ''
PRINT '🎯 مقارنة صلاحيات العرض:'
PRINT '- tasks.view: الوصول الأساسي للمهام (مهام المستخدم فقط)'
PRINT '- tasks.view_tasks_in_department: عرض مهام الإدارة والأقسام التابعة (هرمية)'
PRINT '- tasks.view_all: عرض جميع المهام في النظام'

PRINT ''
PRINT '🔧 الاستخدام المقترح:'
PRINT '- مديري الأقسام: tasks.view_tasks_in_department'
PRINT '- المديرين العامين: tasks.view_all'
PRINT '- الموظفين العاديين: tasks.view'

PRINT ''
PRINT '📝 ملاحظات مهمة:'
PRINT '- هذه الصلاحية تعمل بشكل هرمي (الإدارة + جميع الأقسام التابعة)'
PRINT '- مناسبة لمديري الإدارات الذين يحتاجون رؤية شاملة لمهام إدارتهم'
PRINT '- أقل شمولية من tasks.view_all ولكن أكثر من tasks.view'

PRINT ''
PRINT '🎉 تم الانتهاء من إضافة صلاحية عرض مهام الإدارة بنجاح!'
PRINT '=================================================='

GO
