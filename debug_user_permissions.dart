// import 'package:flutter_application_2/models/user_permission_model.dart';

// /// ملف تشخيص مشاكل UserPermissions
// /// يساعد في فهم سبب فشل إضافة الصلاحيات

// void debugUserPermissions() {
//   print('🔍 بدء تشخيص مشاكل UserPermissions...');
  
//   // اختبار 1: إنشاء UserPermission صحيح
//   testUserPermissionCreation();
  
//   // اختبار 2: تحليل JSON
//   testJsonParsing();
  
//   // اختبار 3: التحقق من البيانات المرسلة
//   testDataValidation();
// }

// void testUserPermissionCreation() {
//   print('\n🧪 اختبار 1: إنشاء UserPermission...');
  
//   try {
//     final userPermission = UserPermission(
//       id: 0,
//       userId: 20,
//       permissionId: 82,
//       grantedBy: 21,
//       grantedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
//       isActive: true,
//       isDeleted: false,
//     );
    
//     print('✅ تم إنشاء UserPermission بنجاح');
//     print('📋 البيانات: ${userPermission.toJson()}');
    
//     // التحقق من صحة البيانات
//     final json = userPermission.toJson();
//     validateUserPermissionData(json);
    
//   } catch (e) {
//     print('❌ خطأ في إنشاء UserPermission: $e');
//   }
// }

// void testJsonParsing() {
//   print('\n🧪 اختبار 2: تحليل JSON...');
  
//   try {
//     // JSON صحيح
//     final validJson = {
//       'id': 1,
//       'userId': 20,
//       'permissionId': 82,
//       'grantedBy': 21,
//       'grantedAt': 1751753396,
//       'isActive': true,
//       'expiresAt': null,
//       'isDeleted': false,
//     };
    
//     final userPermission = UserPermission.fromJson(validJson);
//     print('✅ تم تحليل JSON صحيح بنجاح');
//     print('📋 البيانات المحللة: ${userPermission.toJson()}');
    
//     // JSON مع قيم null
//     final jsonWithNulls = {
//       'id': 1,
//       'userId': 20,
//       'permissionId': 82,
//       'grantedBy': 21,
//       'grantedAt': 1751753396,
//       'isActive': true,
//       'expiresAt': null,
//       'isDeleted': false,
//       'user': null,
//       'permission': null,
//       'grantedByNavigation': null,
//     };
    
//     final userPermission2 = UserPermission.fromJson(jsonWithNulls);
//     print('✅ تم تحليل JSON مع null بنجاح');
//     print('📋 البيانات المحللة: ${userPermission2.toJson()}');
    
//   } catch (e) {
//     print('❌ خطأ في تحليل JSON: $e');
//   }
// }

// void testDataValidation() {
//   print('\n🧪 اختبار 3: التحقق من صحة البيانات...');
  
//   // البيانات المرسلة في الخطأ الأصلي
//   final problematicData = {
//     'id': 0,
//     'userId': 20,
//     'permissionId': 82,
//     'grantedBy': 21,
//     'grantedAt': 1751753396,
//     'isActive': true,
//     'expiresAt': null,
//     'isDeleted': false,
//   };
  
//   print('📋 البيانات المشكوك فيها: $problematicData');
  
//   try {
//     validateUserPermissionData(problematicData);
//     print('✅ البيانات صحيحة');
//   } catch (e) {
//     print('❌ البيانات غير صحيحة: $e');
//   }
  
//   // اختبار بيانات مختلفة
//   final testCases = [
//     // حالة userId = 0
//     {'id': 0, 'userId': 0, 'permissionId': 82, 'grantedBy': 21, 'grantedAt': 1751753396, 'isActive': true, 'isDeleted': false},
//     // حالة permissionId = 0
//     {'id': 0, 'userId': 20, 'permissionId': 0, 'grantedBy': 21, 'grantedAt': 1751753396, 'isActive': true, 'isDeleted': false},
//     // حالة grantedBy = 0
//     {'id': 0, 'userId': 20, 'permissionId': 82, 'grantedBy': 0, 'grantedAt': 1751753396, 'isActive': true, 'isDeleted': false},
//     // حالة grantedAt = 0
//     {'id': 0, 'userId': 20, 'permissionId': 82, 'grantedBy': 21, 'grantedAt': 0, 'isActive': true, 'isDeleted': false},
//   ];
  
//   for (int i = 0; i < testCases.length; i++) {
//     print('\n🔍 اختبار حالة ${i + 1}: ${testCases[i]}');
//     try {
//       validateUserPermissionData(testCases[i]);
//       print('✅ الحالة ${i + 1} صحيحة');
//     } catch (e) {
//       print('❌ الحالة ${i + 1} غير صحيحة: $e');
//     }
//   }
// }

// void validateUserPermissionData(Map<String, dynamic> data) {
//   // التحقق من الحقول المطلوبة
//   if (data['userId'] == null || data['userId'] <= 0) {
//     throw Exception('userId مطلوب ويجب أن يكون أكبر من 0');
//   }
  
//   if (data['permissionId'] == null || data['permissionId'] <= 0) {
//     throw Exception('permissionId مطلوب ويجب أن يكون أكبر من 0');
//   }
  
//   if (data['grantedBy'] == null || data['grantedBy'] <= 0) {
//     throw Exception('grantedBy مطلوب ويجب أن يكون أكبر من 0');
//   }
  
//   if (data['grantedAt'] == null || data['grantedAt'] <= 0) {
//     throw Exception('grantedAt مطلوب ويجب أن يكون أكبر من 0');
//   }
  
//   if (data['isActive'] == null) {
//     throw Exception('isActive مطلوب');
//   }
  
//   if (data['isDeleted'] == null) {
//     throw Exception('isDeleted مطلوب');
//   }
// }

// /// دالة لطباعة معلومات تشخيصية مفصلة
// void printDiagnosticInfo() {
//   print('\n📊 معلومات تشخيصية:');
//   print('- الوقت الحالي (Unix): ${DateTime.now().millisecondsSinceEpoch ~/ 1000}');
//   print('- الوقت الحالي (DateTime): ${DateTime.now()}');
//   print('- معرف المستخدم في الخطأ: 20');
//   print('- معرف الصلاحية في الخطأ: 82');
//   print('- معرف المانح في الخطأ: 21');
//   print('- الوقت في الخطأ: 1751753396');
//   print('- الوقت في الخطأ (DateTime): ${DateTime.fromMillisecondsSinceEpoch(1751753396 * 1000)}');
// }

// void main() {
//   debugUserPermissions();
//   printDiagnosticInfo();
// }
