-- الجزء الثاني من سكريبت إضافة الصلاحيات المفقودة
-- يجب تشغيل هذا السكريبت بعد تشغيل AddAllMissingPermissions_Complete.sql

USE [databasetasks]
GO

PRINT '🚀 بدء إضافة الجزء الثاني من الصلاحيات المفقودة...'

DECLARE @CurrentTime BIGINT = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())

-- ===== 8. صلاحيات إدارة المهام المتقدمة =====
PRINT '📍 إضافة صلاحيات إدارة المهام المتقدمة...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tasks.transfer')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('tasks.transfer', N'تحويل المهام', N'Tasks', N'تحويل', 2, 'swap_horiz', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية tasks.transfer'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tasks.duplicate')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('tasks.duplicate', N'تكرار المهام', N'Tasks', N'تكرار', 1, 'content_copy', '#4CAF50', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية tasks.duplicate'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tasks.archive')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('tasks.archive', N'أرشفة المهام', N'Tasks', N'أرشفة', 2, 'archive', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية tasks.archive'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tasks.restore')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('tasks.restore', N'استعادة المهام المؤرشفة', N'Tasks', N'استعادة', 2, 'unarchive', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية tasks.restore'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tasks.export')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('tasks.export', N'تصدير المهام', N'Tasks', N'تصدير', 2, 'file_download', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية tasks.export'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tasks.import')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('tasks.import', N'استيراد المهام', N'Tasks', N'استيراد', 3, 'file_upload', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية tasks.import'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tasks.bulk_edit')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('tasks.bulk_edit', N'التعديل المجمع للمهام', N'Tasks', N'تعديل مجمع', 3, 'edit', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية tasks.bulk_edit'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tasks.bulk_delete')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('tasks.bulk_delete', N'الحذف المجمع للمهام', N'Tasks', N'حذف مجمع', 4, 'delete_sweep', '#F44336', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية tasks.bulk_delete'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tasks.gantt_view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('tasks.gantt_view', N'عرض مخطط جانت', N'Tasks', N'عرض متقدم', 2, 'timeline', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية tasks.gantt_view'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tasks.board_view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('tasks.board_view', N'عرض لوحة المهام', N'Tasks', N'عرض متقدم', 1, 'view_kanban', '#4CAF50', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية tasks.board_view'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tasks.timeline_view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('tasks.timeline_view', N'عرض الجدول الزمني', N'Tasks', N'عرض متقدم', 2, 'schedule', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية tasks.timeline_view'
END

-- ===== 9. صلاحيات التعليقات والمساهمات =====
PRINT '📍 إضافة صلاحيات التعليقات والمساهمات...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'comments.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('comments.view', N'عرض التعليقات', N'Comments', N'عرض', 1, 'comment', '#FF9800', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية comments.view'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'comments.create')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('comments.create', N'إنشاء تعليقات', N'Comments', N'إنشاء', 1, 'add_comment', '#FF9800', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية comments.create'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'comments.edit')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('comments.edit', N'تعديل التعليقات', N'Comments', N'تعديل', 2, 'edit', '#FF9800', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية comments.edit'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'comments.delete')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('comments.delete', N'حذف التعليقات', N'Comments', N'حذف', 3, 'delete', '#F44336', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية comments.delete'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'comments.reply')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('comments.reply', N'الرد على التعليقات', N'Comments', N'رد', 1, 'reply', '#FF9800', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية comments.reply'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'comments.moderate')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('comments.moderate', N'إدارة التعليقات', N'Comments', N'إدارة', 3, 'gavel', '#FF9800', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية comments.moderate'
END

-- ===== 10. صلاحيات الإشعارات المتقدمة =====
PRINT '📍 إضافة صلاحيات الإشعارات المتقدمة...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'notifications.create')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('notifications.create', N'إنشاء إشعارات', N'Notifications', N'إنشاء', 3, 'add_alert', '#FF9800', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية notifications.create'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'notifications.edit')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('notifications.edit', N'تعديل الإشعارات', N'Notifications', N'تعديل', 3, 'edit', '#FF9800', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية notifications.edit'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'notifications.delete')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('notifications.delete', N'حذف الإشعارات', N'Notifications', N'حذف', 3, 'delete', '#F44336', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية notifications.delete'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'notifications.broadcast')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('notifications.broadcast', N'بث الإشعارات', N'Notifications', N'بث', 4, 'campaign', '#FF9800', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية notifications.broadcast'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'notifications.schedule')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('notifications.schedule', N'جدولة الإشعارات', N'Notifications', N'جدولة', 3, 'schedule', '#FF9800', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية notifications.schedule'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'notifications.settings')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('notifications.settings', N'إعدادات الإشعارات', N'Notifications', N'إعدادات', 2, 'settings', '#FF9800', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية notifications.settings'
END

-- ===== 11. صلاحيات Power BI المتقدمة =====
PRINT '📍 إضافة صلاحيات Power BI المتقدمة...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'powerbi.create')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('powerbi.create', N'إنشاء تقارير Power BI', N'PowerBI', N'إنشاء', 3, 'add_chart', '#FFD700', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية powerbi.create'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'powerbi.edit')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('powerbi.edit', N'تعديل تقارير Power BI', N'PowerBI', N'تعديل', 3, 'edit', '#FFD700', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية powerbi.edit'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'powerbi.delete')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('powerbi.delete', N'حذف تقارير Power BI', N'PowerBI', N'حذف', 4, 'delete', '#F44336', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية powerbi.delete'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'powerbi.share')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('powerbi.share', N'مشاركة تقارير Power BI', N'PowerBI', N'مشاركة', 2, 'share', '#FFD700', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية powerbi.share'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'powerbi.embed')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('powerbi.embed', N'تضمين تقارير Power BI', N'PowerBI', N'تضمين', 3, 'code', '#FFD700', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية powerbi.embed'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'powerbi.refresh')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('powerbi.refresh', N'تحديث بيانات Power BI', N'PowerBI', N'تحديث', 2, 'refresh', '#FFD700', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية powerbi.refresh'
END

-- ===== 12. صلاحيات الإعدادات والتكوين =====
PRINT '📍 إضافة صلاحيات الإعدادات والتكوين...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'settings.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('settings.view', N'عرض الإعدادات', N'Settings', N'عرض', 2, 'settings', '#9E9E9E', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية settings.view'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'settings.edit')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('settings.edit', N'تعديل الإعدادات', N'Settings', N'تعديل', 3, 'tune', '#9E9E9E', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية settings.edit'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'settings.system')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('settings.system', N'إعدادات النظام', N'Settings', N'نظام', 4, 'computer', '#9E9E9E', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية settings.system'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'settings.user')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('settings.user', N'إعدادات المستخدم', N'Settings', N'مستخدم', 1, 'account_circle', '#9E9E9E', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية settings.user'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'settings.theme')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('settings.theme', N'إعدادات المظهر', N'Settings', N'مظهر', 1, 'palette', '#9E9E9E', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية settings.theme'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'settings.language')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('settings.language', N'إعدادات اللغة', N'Settings', N'لغة', 1, 'language', '#9E9E9E', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية settings.language'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'settings.sync')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('settings.sync', N'إعدادات التزامن', N'Settings', N'تزامن', 2, 'sync', '#9E9E9E', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية settings.sync'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'settings.privacy')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('settings.privacy', N'إعدادات الخصوصية', N'Settings', N'خصوصية', 2, 'privacy_tip', '#9E9E9E', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية settings.privacy'
END

PRINT '✅ تم الانتهاء من إضافة الجزء الثاني من الصلاحيات المفقودة!'
PRINT '📊 تم إضافة 40 صلاحية إضافية في هذا الجزء'

-- عرض إحصائيات الصلاحيات الجديدة
PRINT ''
PRINT '📊 إحصائيات الصلاحيات الجديدة:'
SELECT 
    permission_group as [المجموعة],
    COUNT(*) as [عدد الصلاحيات الجديدة]
FROM permissions 
WHERE created_at >= @CurrentTime
GROUP BY permission_group
ORDER BY permission_group

PRINT ''
PRINT '📋 إجمالي الصلاحيات في النظام بعد التحديث:'
SELECT 
    COUNT(*) as [إجمالي الصلاحيات],
    SUM(CASE WHEN is_default = 1 THEN 1 ELSE 0 END) as [الصلاحيات الافتراضية],
    SUM(CASE WHEN is_default = 0 THEN 1 ELSE 0 END) as [الصلاحيات المتقدمة]
FROM permissions

PRINT ''
PRINT '🎉 تم تحديث نظام الصلاحيات بنجاح!'
PRINT 'يمكنك الآن استخدام الصلاحيات الجديدة في التطبيق.'

GO
