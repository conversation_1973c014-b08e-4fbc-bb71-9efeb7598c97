import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../utils/mouse_event_handler.dart';
import '../../../services/unified_permission_service.dart';

// ملاحظة: تم استبدال هذا المخطط بـ EnhancedGanttChart
// يرجى استخدام EnhancedGanttChart بدلاً من هذا المخطط

/// نموذج مهمة لمخطط Gantt
class GanttTask {
  /// معرف المهمة
  final String id;

  /// عنوان المهمة
  final String title;

  /// تاريخ بداية المهمة
  final DateTime startDate;

  /// تاريخ نهاية المهمة
  final DateTime endDate;

  /// نسبة إكمال المهمة (0-100)
  final double completionPercentage;

  /// لون المهمة (اختياري)
  final Color? color;

  /// معرف المهمة الأب (اختياري)
  final String? parentId;

  /// بيانات إضافية (اختياري)
  final Map<String, dynamic>? additionalData;

  /// إنشاء مهمة لمخطط Gantt
  const GanttTask({
    required this.id,
    required this.title,
    required this.startDate,
    required this.endDate,
    this.completionPercentage = 0,
    this.color,
    this.parentId,
    this.additionalData,
  });
}

/// مكون مخطط Gantt
///
/// يعرض مخطط Gantt للمهام مع دعم للتفاعل
///
/// ملاحظة: تم استبدال هذا المخطط بـ EnhancedGanttChart
/// يرجى استخدام EnhancedGanttChart بدلاً من هذا المخطط للحصول على ميزات أكثر تقدمًا
/// @deprecated استخدم EnhancedGanttChart بدلاً من ذلك
class GanttChart extends StatefulWidget {
  /// قائمة المهام
  final List<GanttTask> tasks;

  /// عنوان المخطط (اختياري)
  final String? title;

  /// تاريخ بداية المخطط (اختياري)
  /// إذا لم يتم تحديده، سيتم استخدام أقدم تاريخ بداية للمهام
  final DateTime? startDate;

  /// تاريخ نهاية المخطط (اختياري)
  /// إذا لم يتم تحديده، سيتم استخدام أحدث تاريخ نهاية للمهام
  final DateTime? endDate;

  /// عرض عمود العنوان (اختياري)
  final double titleColumnWidth;

  /// ارتفاع صف المهمة (اختياري)
  final double rowHeight;

  /// إظهار نسبة الإكمال (اختياري)
  final bool showCompletionPercentage;

  /// إظهار التواريخ (اختياري)
  final bool showDates;

  /// تنسيق التاريخ (اختياري)
  final String dateFormat;

  /// دالة يتم استدعاؤها عند النقر على مهمة (اختياري)
  final Function(GanttTask)? onTaskTap;

  /// إنشاء مكون مخطط Gantt
  const GanttChart({
    super.key,
    required this.tasks,
    this.title,
    this.startDate,
    this.endDate,
    this.titleColumnWidth = 200,
    this.rowHeight = 50,
    this.showCompletionPercentage = true,
    this.showDates = true,
    this.dateFormat = 'yyyy-MM-dd',
    this.onTaskTap,
  });

  @override
  State<GanttChart> createState() => _GanttChartState();
}

class _GanttChartState extends State<GanttChart> {
  late DateTime _startDate;
  late DateTime _endDate;
  late int _totalDays;
  late ScrollController _horizontalScrollController;
  late ScrollController _verticalScrollController;
  int? _hoveredTaskIndex;

  @override
  void initState() {
    super.initState();
    _calculateDateRange();
    _horizontalScrollController = ScrollController();
    _verticalScrollController = ScrollController();
  }

  @override
  void didUpdateWidget(GanttChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tasks != widget.tasks ||
        oldWidget.startDate != widget.startDate ||
        oldWidget.endDate != widget.endDate) {
      _calculateDateRange();
    }
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    _verticalScrollController.dispose();
    super.dispose();
  }

  void _calculateDateRange() {
    if (widget.tasks.isEmpty) {
      _startDate = widget.startDate ?? DateTime.now();
      _endDate = widget.endDate ?? _startDate.add(const Duration(days: 30));
    } else {
      DateTime minStartDate = widget.tasks.first.startDate;
      DateTime maxEndDate = widget.tasks.first.endDate;

      for (final task in widget.tasks) {
        if (task.startDate.isBefore(minStartDate)) {
          minStartDate = task.startDate;
        }
        if (task.endDate.isAfter(maxEndDate)) {
          maxEndDate = task.endDate;
        }
      }

      // إضافة هامش
      _startDate =
          widget.startDate ?? minStartDate.subtract(const Duration(days: 1));
      _endDate = widget.endDate ?? maxEndDate.add(const Duration(days: 1));
    }

    // حساب إجمالي الأيام
    _totalDays = _endDate.difference(_startDate).inDays + 1;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.tasks.isEmpty) {
      return const Center(
        child: Text('لا توجد مهام للعرض'),
      );
    }

    return Column(
      children: [
        if (widget.title != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: Text(
              widget.title!,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        Expanded(
          child: Row(
            children: [
              // عمود العناوين
              SizedBox(
                width: widget.titleColumnWidth,
                child: Column(
                  children: [
                    // رأس العمود
                    Container(
                      height: 50,
                      decoration: BoxDecoration(
                        color: Get.isDarkMode
                            ? Colors.grey[800]
                            : Colors.grey[200],
                        border: Border(
                          bottom: BorderSide(
                            color: Get.isDarkMode
                                ? Colors.grey[700]!
                                : Colors.grey[300]!,
                          ),
                        ),
                      ),
                      alignment: Alignment.centerRight,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: const Text(
                        'المهام',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    // قائمة المهام
                    Expanded(
                      child: ListView.builder(
                        controller: _verticalScrollController,
                        itemCount: widget.tasks.length,
                        itemBuilder: (context, index) {
                          final task = widget.tasks[index];
                          return _buildTaskTitleRow(task, index);
                        },
                      ),
                    ),
                  ],
                ),
              ),
              // مخطط Gantt
              Expanded(
                child: SingleChildScrollView(
                  controller: _horizontalScrollController,
                  scrollDirection: Axis.horizontal,
                  child: SizedBox(
                    width: _totalDays * 40, // عرض كل يوم 40 بكسل
                    child: Column(
                      children: [
                        // رأس المخطط (التواريخ)
                        SizedBox(
                          height: 50,
                          child: _buildHeaderRow(),
                        ),
                        // صفوف المهام
                        Expanded(
                          child: ListView.builder(
                            controller: _verticalScrollController,
                            itemCount: widget.tasks.length,
                            itemBuilder: (context, index) {
                              final task = widget.tasks[index];
                              return _buildTaskRow(task, index);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderRow() {
    return Row(
      children: List.generate(_totalDays, (index) {
        final date = _startDate.add(Duration(days: index));
        final isWeekend =
            date.weekday == 5 || date.weekday == 6; // الجمعة والسبت

        return Container(
          width: 40,
          decoration: BoxDecoration(
            color: isWeekend
                ? (Get.isDarkMode ? Colors.grey[900] : Colors.grey[300])
                : (Get.isDarkMode ? Colors.grey[800] : Colors.grey[200]),
            border: Border(
              bottom: BorderSide(
                color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
              ),
              left: BorderSide(
                color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
                width: 0.5,
              ),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                DateFormat('d').format(date),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight:
                      date.day == 1 ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              if (date.day == 1 || index == 0)
                Text(
                  DateFormat('MMM').format(date),
                  style: const TextStyle(
                    fontSize: 10,
                  ),
                ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildTaskTitleRow(GanttTask task, int index) {
    return SafeMouseRegion(
      onEnter: (_) => setState(() => _hoveredTaskIndex = index),
      onExit: (_) => setState(() => _hoveredTaskIndex = null),
      child: Container(
        height: widget.rowHeight,
        decoration: BoxDecoration(
          color: _hoveredTaskIndex == index
              ? (Get.isDarkMode ? Colors.grey[800] : Colors.grey[200])
              : null,
          border: Border(
            bottom: BorderSide(
              color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
            ),
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        alignment: Alignment.centerRight,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // رقم المهمة
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.blue.shade200, width: 1),
              ),
              child: Text(
                '#${task.id}',
                style: TextStyle(
                  color: Colors.blue.shade700,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                task.title,
                style: const TextStyle(
                  fontSize: 14,
                ),
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.right,
              ),
            ),
            if (widget.showDates) ...[
              const SizedBox(width: 8),
              Text(
                DateFormat(widget.dateFormat).format(task.startDate),
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.grey,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTaskRow(GanttTask task, int index) {
    final int startDayOffset = task.startDate.difference(_startDate).inDays;
    final int taskDuration = task.endDate.difference(task.startDate).inDays + 1;

    // التأكد من أن المهمة تقع ضمن نطاق المخطط
    if (startDayOffset + taskDuration < 0 || startDayOffset >= _totalDays) {
      return SizedBox(height: widget.rowHeight);
    }

    // تعديل الموضع والمدة إذا كانت المهمة تتجاوز حدود المخطط
    final int adjustedStartOffset = startDayOffset < 0 ? 0 : startDayOffset;
    final int adjustedDuration = startDayOffset < 0
        ? taskDuration + startDayOffset
        : (adjustedStartOffset + taskDuration > _totalDays
            ? _totalDays - adjustedStartOffset
            : taskDuration);

    return SafeMouseRegion(
      onEnter: (_) => setState(() => _hoveredTaskIndex = index),
      onExit: (_) => setState(() => _hoveredTaskIndex = null),
      child: GestureDetector(
        onTap: () {
          if (widget.onTaskTap != null) {
            widget.onTaskTap!(task);
          }
        },
        child: Container(
          height: widget.rowHeight,
          decoration: BoxDecoration(
            color: _hoveredTaskIndex == index
                ? (Get.isDarkMode ? Colors.grey[800] : Colors.grey[200])
                : null,
            border: Border(
              bottom: BorderSide(
                color: Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!,
              ),
            ),
          ),
          child: Stack(
            children: [
              // خلفية الصف مع خطوط الشبكة
              Row(
                children: List.generate(_totalDays, (dayIndex) {
                  final date = _startDate.add(Duration(days: dayIndex));
                  final isWeekend =
                      date.weekday == 5 || date.weekday == 6; // الجمعة والسبت

                  return Container(
                    width: 40,
                    decoration: BoxDecoration(
                      color: isWeekend
                          ? (Get.isDarkMode
                              ? Colors.grey[900]!.withAlpha(76)
                              : Colors.grey[300]!.withAlpha(76))
                          : null,
                      border: Border(
                        left: BorderSide(
                          color: Get.isDarkMode
                              ? Colors.grey[700]!
                              : Colors.grey[300]!,
                          width: 0.5,
                        ),
                      ),
                    ),
                  );
                }),
              ),
              // شريط المهمة
              Positioned(
                right: adjustedStartOffset * 40.0,
                top: widget.rowHeight * 0.2,
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  onEnter: (_) {
                    // عرض تلميح عند تمرير المؤشر فوق المهمة
                    final tooltip = '${task.title}\n'
                        'البداية: ${DateFormat(widget.dateFormat).format(task.startDate)}\n'
                        'النهاية: ${DateFormat(widget.dateFormat).format(task.endDate)}\n'
                        'الإنجاز: ${task.completionPercentage.toInt()}%';

                    // يمكن إضافة منطق لعرض التلميح هنا
                    debugPrint(tooltip);
                  },
                  child: UnifiedPermissionService().canEditTask()
                      ? Draggable<GanttTask>(
                          data: task,
                          feedback: Material(
                            elevation: 4.0,
                            borderRadius: BorderRadius.circular(4),
                            child: Container(
                              width: adjustedDuration * 40.0,
                              height: widget.rowHeight * 0.6,
                              decoration: BoxDecoration(
                                color: task.color != null
                                    ? task.color!.withAlpha(204) // 0.8 * 255 = 204
                                    : Colors.blue.withAlpha(204),
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.white, width: 2),
                              ),
                              child: Center(
                                child: Text(
                                  task.title,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                          ),
                          childWhenDragging: Opacity(
                            opacity: 0.3,
                            child: Container(
                              width: adjustedDuration * 40.0,
                              height: widget.rowHeight * 0.6,
                              decoration: BoxDecoration(
                                color: task.color ?? Colors.blue,
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(
                                    color: Colors.white.withAlpha(128), width: 1),
                              ),
                            ),
                          ),
                          onDragEnd: (details) {
                            // يمكن إضافة منطق لتحديث تاريخ المهمة بناءً على موقع الإفلات
                            if (widget.onTaskTap != null) {
                              widget.onTaskTap!(task);
                            }
                          },
                          child: Container(
                            width: adjustedDuration * 40.0,
                            height: widget.rowHeight * 0.6,
                            decoration: BoxDecoration(
                              color: task.color ?? Colors.blue,
                              borderRadius: BorderRadius.circular(4),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withAlpha(26),
                                  blurRadius: 2,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: widget.showCompletionPercentage
                                ? Row(
                                    children: [
                                      Container(
                                        width: (adjustedDuration * 40.0) *
                                            (task.completionPercentage / 100),
                                        decoration: BoxDecoration(
                                          color: Colors.green.withAlpha(179),
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                      ),
                                      Expanded(
                                        child: Center(
                                          child: Text(
                                            '${task.completionPercentage.toInt()}%',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  )
                                : Center(
                                    child: Text(
                                      task.title,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                          ),
                        )
                      : Container(
                          width: adjustedDuration * 40.0,
                          height: widget.rowHeight * 0.6,
                          decoration: BoxDecoration(
                            color: task.color != null
                                ? task.color!.withAlpha(179)
                                : Colors.blue.withAlpha(179),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Center(
                            child: Text(
                              task.title,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
