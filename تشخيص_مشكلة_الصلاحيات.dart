// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import '../services/unified_permission_service.dart';
// import '../controllers/auth_controller.dart';

// /// أداة تشخيص مشاكل الصلاحيات
// class PermissionDiagnosticTool {
//   static final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
//   static final AuthController _authController = Get.find<AuthController>();

//   /// تشخيص شامل لمشكلة عدم ظهور زر تحويل المهمة
//   static void diagnoseTransferButtonIssue() {
//     debugPrint('🔍 === تشخيص مشكلة زر تحويل المهمة ===');
    
//     // 1. فحص المستخدم الحالي
//     final currentUser = _authController.currentUser.value;
//     if (currentUser == null) {
//       debugPrint('❌ لا يوجد مستخدم مسجل دخول');
//       return;
//     }
    
//     debugPrint('👤 المستخدم الحالي: ${currentUser.name} (ID: ${currentUser.id})');
//     debugPrint('🎭 الدور: ${currentUser.role}');
    
//     // 2. فحص الصلاحيات المحملة
//     final userPermissions = _permissionService.userPermissions;
//     debugPrint('📊 إجمالي الصلاحيات المحملة: ${userPermissions.length}');
    
//     // 3. فحص صلاحية تحويل المهمة تحديداً
//     final canTransfer = _permissionService.canTransferTask();
//     debugPrint('🔑 canTransferTask(): $canTransfer');
    
//     // 4. فحص الصلاحية الخام
//     final hasTransferPermission = _permissionService.hasPermission('tasks.transfer');
//     debugPrint('🔑 hasPermission("tasks.transfer"): $hasTransferPermission');
    
//     // 5. البحث في جميع الصلاحيات عن كلمة transfer
//     debugPrint('🔍 البحث عن صلاحيات التحويل:');
//     userPermissions.forEach((key, value) {
//       if (key.toLowerCase().contains('transfer')) {
//         debugPrint('  ✅ $key: $value');
//       }
//     });
    
//     // 6. فحص صلاحية عرض تفاصيل المهمة
//     final canViewDetails = _permissionService.canViewTaskDetails();
//     debugPrint('🔑 canViewTaskDetails(): $canViewDetails');
    
//     final hasViewDetailsPermission = _permissionService.hasPermission('tasks.view_details');
//     debugPrint('🔑 hasPermission("tasks.view_details"): $hasViewDetailsPermission');
    
//     // 7. عرض النتيجة النهائية
//     debugPrint('📋 === النتيجة النهائية ===');
//     if (canTransfer && canViewDetails) {
//       debugPrint('✅ جميع الصلاحيات متوفرة - يجب أن تعمل الأزرار');
//     } else {
//       debugPrint('❌ توجد مشكلة في الصلاحيات:');
//       if (!canTransfer) debugPrint('  - صلاحية تحويل المهمة مفقودة');
//       if (!canViewDetails) debugPrint('  - صلاحية عرض تفاصيل المهمة مفقودة');
//     }
//   }

//   /// إعادة تحميل الصلاحيات
//   static Future<void> reloadPermissions() async {
//     debugPrint('🔄 إعادة تحميل الصلاحيات...');
    
//     final currentUser = _authController.currentUser.value;
//     if (currentUser != null) {
//       await _permissionService.loadUserPermissions(currentUser.id);
//       debugPrint('✅ تم إعادة تحميل الصلاحيات');
      
//       // تشغيل التشخيص مرة أخرى
//       diagnoseTransferButtonIssue();
//     }
//   }

//   /// فحص حالة الشاشة الحالية
//   static void checkCurrentScreenState() {
//     debugPrint('🔍 === فحص حالة الشاشة الحالية ===');
    
//     // فحص المسار الحالي
//     final currentRoute = Get.currentRoute;
//     debugPrint('📍 المسار الحالي: $currentRoute');
    
//     // فحص الـ arguments
//     final arguments = Get.arguments;
//     debugPrint('📋 المعاملات: $arguments');
    
//     // فحص حالة TaskController
//     if (Get.isRegistered<TaskController>()) {
//       final taskController = Get.find<TaskController>();
//       final currentTask = taskController.currentTask;
//       debugPrint('📝 المهمة الحالية: ${currentTask?.title ?? 'لا توجد مهمة'}');
//     }
//   }

//   /// اختبار الصلاحيات عبر API
//   static Future<void> testPermissionsViaAPI() async {
//     debugPrint('🔍 === اختبار الصلاحيات عبر API ===');
    
//     final currentUser = _authController.currentUser.value;
//     if (currentUser == null) {
//       debugPrint('❌ لا يوجد مستخدم مسجل دخول');
//       return;
//     }
    
//     // اختبار صلاحية تحويل المهمة
//     final canTransferAPI = await _permissionService.checkPermissionAsync('tasks.transfer');
//     debugPrint('🌐 API - tasks.transfer: $canTransferAPI');
    
//     // اختبار صلاحية عرض تفاصيل المهمة
//     final canViewDetailsAPI = await _permissionService.checkPermissionAsync('tasks.view_details');
//     debugPrint('🌐 API - tasks.view_details: $canViewDetailsAPI');
    
//     // مقارنة النتائج
//     final localTransfer = _permissionService.canTransferTask();
//     final localViewDetails = _permissionService.canViewTaskDetails();
    
//     debugPrint('📊 === مقارنة النتائج ===');
//     debugPrint('تحويل المهمة - محلي: $localTransfer, API: $canTransferAPI');
//     debugPrint('عرض التفاصيل - محلي: $localViewDetails, API: $canViewDetailsAPI');
    
//     if (localTransfer != canTransferAPI || localViewDetails != canViewDetailsAPI) {
//       debugPrint('⚠️ يوجد تضارب بين النتائج المحلية و API');
//       debugPrint('💡 يُنصح بإعادة تحميل الصلاحيات');
//     }
//   }

//   /// عرض جميع الصلاحيات المحملة
//   static void showAllPermissions() {
//     debugPrint('📋 === جميع الصلاحيات المحملة ===');
    
//     final userPermissions = _permissionService.userPermissions;
//     if (userPermissions.isEmpty) {
//       debugPrint('❌ لا توجد صلاحيات محملة');
//       return;
//     }
    
//     // ترتيب الصلاحيات أبجدياً
//     final sortedPermissions = userPermissions.entries.toList()
//       ..sort((a, b) => a.key.compareTo(b.key));
    
//     for (final entry in sortedPermissions) {
//       final status = entry.value ? '✅' : '❌';
//       debugPrint('$status ${entry.key}');
//     }
    
//     debugPrint('📊 إجمالي: ${userPermissions.length} صلاحية');
//   }
// }
