-- إضافة صلاحية مدير الأقسام الإدارية
-- تاريخ الإنشاء: 2025-01-12
-- الهدف: إضافة صلاحية departments.admin للإدارة الكاملة للأقسام

USE [databasetasks]
GO

PRINT '🏢 بدء إضافة صلاحية مدير الأقسام الإدارية...'
PRINT '=================================================='

-- التحقق من وجود الصلاحية مسبقاً
IF EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'departments.admin')
BEGIN
    PRINT '⚠️ صلاحية departments.admin موجودة مسبقاً'
    
    -- عرض تفاصيل الصلاحية الموجودة
    SELECT 
        [name] as 'اسم الصلاحية',
        [description] as 'الوصف',
        [permission_group] as 'المجموعة',
        [category] as 'الفئة',
        [level] as 'المستوى',
        [is_active] as 'نشطة',
        [created_at] as 'تاريخ الإنشاء'
    FROM [dbo].[permissions] 
    WHERE [name] = 'departments.admin'
END
ELSE
BEGIN
    PRINT '✅ إضافة صلاحية جديدة: departments.admin'
    
    -- إضافة الصلاحية الجديدة
    INSERT INTO [dbo].[permissions] (
        [name], 
        [description], 
        [permission_group], 
        [category], 
        [level], 
        [icon], 
        [color], 
        [is_default], 
        [is_active], 
        [created_at], 
        [updated_at]
    )
    VALUES (
        'departments.admin',
        N'إدارة شاملة للأقسام (مدير أقسام إداري)',
        'departments',
        N'إدارة الأقسام',
        4,
        'admin_panel_settings',
        '#FF5722',
        0,
        1,
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()),
        DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
    )
    
    PRINT '✅ تم إضافة صلاحية departments.admin بنجاح'
END

PRINT ''
PRINT '📊 ملخص صلاحيات الأقسام:'
SELECT 
    [name] as 'اسم الصلاحية',
    [description] as 'الوصف',
    [level] as 'المستوى',
    [is_active] as 'نشطة'
FROM [dbo].[permissions] 
WHERE [permission_group] = 'departments'
ORDER BY [level], [name]

PRINT ''
PRINT '🎯 مقارنة صلاحيات الأقسام:'
PRINT '- departments.view (مستوى 1): عرض الأقسام فقط'
PRINT '- departments.manage (مستوى 2): إدارة الأقسام العادية'
PRINT '- departments.admin (مستوى 4): إدارة شاملة للأقسام (مدير إداري)'

PRINT ''
PRINT '🔧 الاستخدام المقترح:'
PRINT '- الموظفين العاديين: departments.view'
PRINT '- مديري الأقسام: departments.manage'
PRINT '- مديري الأقسام الإداريين: departments.admin'
PRINT '- المديرين العامين: جميع الصلاحيات'

PRINT ''
PRINT '📝 ملاحظات مهمة:'
PRINT '- departments.admin تتضمن جميع صلاحيات departments.manage'
PRINT '- مناسبة لمديري الأقسام الذين يحتاجون صلاحيات إدارية متقدمة'
PRINT '- أعلى مستوى من صلاحيات الأقسام (مستوى 4)'

PRINT ''
PRINT '🎉 تم الانتهاء من إضافة صلاحية مدير الأقسام الإدارية بنجاح!'
PRINT '=================================================='

GO
