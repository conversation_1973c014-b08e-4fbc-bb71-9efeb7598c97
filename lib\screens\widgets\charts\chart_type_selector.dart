import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/chart_enums.dart';

/// مكون اختيار نوع المخطط
///
/// تم نقل وظيفة هذا المكون إلى UnifiedFilterExportWidget في dashboard_tab.dart
/// لتجنب تكرار أيقونات اختيار نوع المخطط
class ChartTypeSelector extends StatelessWidget {
  /// نوع المخطط الحالي
  final ChartType currentType;

  /// أنواع المخططات المتاحة
  final List<ChartType> availableTypes;

  /// دالة يتم استدعاؤها عند تغيير نوع المخطط
  final Function(ChartType, String) onTypeChanged;

  /// مفتاح المخطط
  final String chartKey;

  /// لون العنصر المحدد
  final Color? selectedColor;

  /// حجم الأيقونة
  final double iconSize;

  const ChartTypeSelector({
    super.key,
    required this.currentType,
    required this.availableTypes,
    required this.onTypeChanged,
    required this.chartKey,
    this.selectedColor,
    this.iconSize = 16,
  });

  @override
  Widget build(BuildContext context) {
    // إرجاع عنصر فارغ لتجنب تكرار أيقونات اختيار نوع المخطط
    // يتم استخدام UnifiedFilterExportWidget في dashboard_tab.dart بدلاً من ذلك
    return const SizedBox.shrink();
  }
}
