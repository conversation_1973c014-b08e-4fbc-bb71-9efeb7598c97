import 'package:flutter/material.dart';
import 'package:flutter_application_2/screens/home/<USER>';
import 'package:flutter_application_2/screens/home/<USER>';
import 'package:flutter_application_2/screens/widgets/documents/advanced_quill_editor_widget.dart';
import 'package:flutter_application_2/services/unified_permission_service.dart';
import 'package:get/get.dart';
import '../screens/home/<USER>';
import '../screens/admin/admin_dashboard_new.dart';
import '../screens/user/user_dashboard_screen.dart';
import '../screens/tasks/task_detail_screen.dart';
import '../screens/tasks/create_task_screen.dart';
import '../screens/tasks/task_reminders_screen.dart';
import '../screens/documents/task_document_editor_screen.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';
import '../screens/chat/chat_list_screen.dart';
import '../screens/chat/unified_chat_detail_screen.dart';
import '../screens/chat/unified_chat_list_screen.dart';
import '../screens/diagnostics_screen.dart';
import '../screens/widgets/dashboard/fullscreen_chart_view.dart';
import '../screens/chat/create_group_chat_screen.dart';
import '../screens/chat/add_members_screen.dart';
import '../screens/chat/search_conversation_screen.dart';
import '../screens/chat/mute_notifications_screen.dart';
import '../screens/notifications/notifications_screen.dart';

import '../screens/dashboard/customizable_dashboard_screen.dart';
import '../screens/dashboard/new_dashboard_screen.dart';
import '../screens/dashboard/monday_dashboard_screen.dart';
import '../screens/reports/reports_screen.dart';
import '../screens/reports/custom_report_builder_screen.dart';
import '../screens/reports/report_scheduler_screen.dart';
import '../screens/reports/static_reports_screen.dart';
import '../screens/reports/enhanced_reports_screen.dart';
import '../screens/reports/enhanced_charts_screen.dart';
import '../screens/reports/monday_style_reports_screen.dart';
import '../screens/reports/monday_style_report_viewer_screen.dart';
import '../screens/reports/report_builder_screen.dart';
import '../screens/reports/exported_reports_screen.dart';
import '../screens/reports/report_viewer_screen.dart';
import '../screens/reports/comprehensive_report_test_screen.dart';

import '../screens/settings/database_repair_screen.dart';
import '../screens/calendar/calendar_screen.dart';
import '../screens/power_bi/power_bi_screen.dart';
import '../screens/power_bi/dynamic_power_bi_report_screen.dart';
import '../screens/archive/archive_home_screen.dart';

import '../screens/archive/document_upload_screen.dart';
import '../screens/archive/category_management_screen.dart';
import '../screens/archive/tag_management_screen.dart';
import '../screens/archive/document_version_history_screen.dart';
import '../screens/archive/document_browser_screen.dart';
import '../screens/calendar/calendar_event_details.dart';
import '../screens/archive/edit_document_screen.dart';
import '../screens/settings/archive_system_repair_screen.dart';
import '../screens/test/file_storage_test_screen.dart';
import '../screens/test/permissions_test_screen.dart';

// import '../screens/admin/permission_test_screen.dart'; // ملف غير موجود
// إضافة استيراد شاشة البحث الموحد
import '../screens/search/unified_search_screen.dart';
// إضافة استيراد شاشة اختبار المستخدمين
// إضافة استيراد نظام التقارير الاحترافية
import '../professional_reports/screens/reports_dashboard_screen.dart';
// إضافة استيراد نماذج التقارير

// إضافة استيراد روابط لوحة المعلومات
import '../bindings/dashboard_binding.dart';
import '../bindings/search_binding.dart';

import '../bindings/text_document_binding.dart';
import '../bindings/chat_binding.dart';
// إضافة استيراد شاشات المستندات النصية


// إضافة استيراد الوسطاء
import '../middleware/unified_permission_middleware.dart';



/// تعريف جميع مسارات التطبيق
/// يتم استخدام هذا الملف لتوحيد طريقة التنقل بين الشاشات
class AppRoutes {
  // المسارات الرئيسية
  static const String home = '/home';
  static const String dashboard = '/dashboard'; // لوحة التحكم الرئيسية الجديدة
  static const String login = '/login';
  static const String register = '/register';
  static const String tasks = '/tasks';
  static const String admin = '/admin';
  static const String enhancedAdmin = '/admin/enhanced'; // المسار الجديد للوحة التحكم المحسنة
  static const String testEnhancedAdmin = '/admin/test-enhanced'; // مسار اختبار لوحة التحكم المحسنة

  static const String userDashboard = '/user-dashboard';
  static const String departmentDashboard = '/Departments';

  // مسارات المهام
  static const String taskDetail = '/task/detail';
  static const String createTask = '/task/create';
  static const String enhancedTaskBoard = '/task/board/enhanced';
  static const String taskStats = '/task/stats';
  static const String taskDocumentEditor = '/task/document/editor';
  static const String taskReminders = '/task-reminders';

  // مسارات المحادثات
  static const String chatList = '/chat/list';
  static const String chatDetail = '/chat/detail';
  static const String unifiedChatDetail = '/chat/unified-detail';
  static const String unifiedChatList = '/chat/unified-list';
  static const String createGroupChat = '/chat/create-group';
  static const String addMembers = '/chat/add-members';
  static const String searchConversation = '/chat/search';
  static const String muteNotifications = '/chat/mute-notifications';

  // مسارات التقارير
  static const String reports = '/reports';
  static const String reportDetails = '/report/details';
  static const String createReport = '/report/create';
  static const String reportScheduler = '/report/scheduler';
  static const String staticReports = '/report/static';
  static const String enhancedReports = '/report/enhanced';
  static const String comprehensiveReportTest = '/report/comprehensive-test';
  static const String enhancedCharts = '/report/charts';
  static const String powerBI = '/power-bi';
  static const String dynamicPowerBI = '/power-bi/dynamic';

  // مسارات نظام التقارير الجديد
  static const String reportingDashboard = '/reporting/dashboard';
  static const String reportBuilder = '/reporting/builder';
  static const String reportViewer = '/reporting/viewer';
  static const String exportedReports = '/reporting/exported';

  // مسارات نظام التقارير بتصميم Monday.com
  static const String mondayStyleReports = '/reports/monday-style';
  static const String mondayStyleReportViewer = '/report/monday-style/viewer';

  // الاشعارات notifications
  static const String notifications = '/notifications';

  // مسارات الإعدادات
  static const String syncSettings = '/settings/sync';
  static const String databaseRepair = '/settings/database-repair';
  static const String archiveSystemRepair = '/settings/archive-system-repair';
  static const String diagnostics = '/settings/diagnostics';

  // مسارات التقويم
  static const String calendar = '/calendar';
  static const String calendarEventDetails = '/CalendarEventDetails';

  // مسارات لوحة المعلومات
  static const String customizableDashboard = '/dashboard/customizable';
  static const String newDashboard = '/dashboard/new';
  static const String mondayDashboard = '/dashboard/monday';
  static const String fullscreenChartView = '/dashboard/chart/fullscreen';

  // مسارات نظام الأرشفة الإلكترونية
  static const String archiveHome = '/archive';
  static const String documentBrowser = '/archive/documents';
  static const String documentUpload = '/archive/upload';
  static const String categoryManagement = '/archive/categories';
  static const String tagManagement = '/archive/tags';
  static const String documentVersionHistory = '/archive/document/versions';
  static const String editDocument = '/archive/document/edit';

  // مسارات الأدوار والصلاحيات
  static const String roles = '/admin2/roles';
  static const String permissionTest = '/admin/permission-test';
  static const String enhancedPermissionsManagement = '/admin/enhanced-permissions';
  static const String userPermissionsViewer = '/admin/user-permissions';

  // مسارات البحث
  static const String unifiedSearch = '/search';

  // مسارات المستندات النصية
  static const String textDocumentsList = '/documents';
  static const String textDocumentEditor = '/documents/editor';
  static const String textDocumentViewer = '/documents/viewer';
  static const String advancedTextDocumentEditor = '/documents/advanced-editor';

  // مسارات المساعدة
  static const String help = '/help';

  // مسارات الاختبار
  static const String testMenu = '/test';

  static const String fileStorageTest = '/test/file-storage';

  static const String permissionsTest = '/permissions-test';

  // مسارات نظام التقارير الاحترافية
  static const String professionalReports = '/professional-reports';



  /// قائمة بجميع صفحات التطبيق
  static final List<GetPage> pages = [
    // لوحة التحكم الرئيسية الجديدة
    // GetPage(
    //   name: dashboard,
    //   page: () => const MainDashboardScreen(),
    // ),

    // المسارات الرئيسية
    GetPage(
      name: home,
      page: () => const HomeScreen(),
      binding: DashboardBinding(),
      
    ),
    GetPage(
      name: departmentDashboard,
      page: () => _ProtectedDepartmentsTab(),

    ),
    GetPage(
      name: notifications,
      page: () => const NotificationsScreen(),
      
    ),

    GetPage(name: login, page: () => const LoginScreen()),
    GetPage(name: register, page: () => const RegisterScreen()),
    // تم إزالة مسار المهام المنفصل لتجنب التضارب مع التبويبات
    // المهام متاحة عبر الشاشة الرئيسية والتبويب رقم 1
    GetPage(
      name: admin,
      page: () => const AdminDashboardScreen(),
      
    ),

    
   
    GetPage(
      name: userDashboard,
      page: () => const UserDashboardScreen(),
      
    ),

    // مسارات المهام
    GetPage(
      name: taskDetail,
      page: () {
        // التعامل مع كلا الحالتين: dictionary أو string مباشرة
        String taskId;
        int? initialTabIndex;

        if (Get.arguments is Map) {
          final args = Get.arguments as Map;
          taskId = args['taskId'].toString();
          initialTabIndex = args['initialTabIndex'] as int?;
        } else {
          taskId = Get.arguments.toString();
        }

        return TaskDetailScreen(
          taskId: taskId,
          initialTabIndex: initialTabIndex,
        );
      },
      middlewares: [UnifiedPermissionMiddleware()], // 🔒 حماية المسار
    ),
    GetPage(
      name: createTask,
      page: () => const CreateTaskScreen(),
      middlewares: [UnifiedPermissionMiddleware()], // 🔒 حماية المسار
    ),
    GetPage(
      name: taskReminders,
      page: () => const TaskRemindersScreen(),
    ),
    GetPage(
      name: tasks,
      page: () => const TasksTab(),
      // 
    ),
    GetPage(
      name: taskDocumentEditor,
      page: () => TaskDocumentEditorScreen(
        // document: Get.arguments?['document'],
        taskId: Get.arguments['taskId'],
        initialType: Get.arguments?['initialType'],
      ),
      
    ),

    // مسارات المحادثات
    GetPage(
      name: chatList,
      page: () => const ChatListScreen(),
      
    ),
    GetPage(
      name: unifiedChatList,
      page: () => const UnifiedChatListScreen(),
      binding: ChatBinding(),
      
    ),
    GetPage(
      name: chatDetail,
      page: () => UnifiedChatDetailScreen(
        chatGroup: Get.arguments['chatGroup'],
      ),
      
    ),
    GetPage(
      name: unifiedChatDetail,
      page: () => UnifiedChatDetailScreen(
        chatGroup: Get.arguments['chatGroup'],
      ),
      binding: ChatBinding(),
      
    ),
    GetPage(
      name: createGroupChat,
      page: () => const CreateGroupChatScreen(),
      binding: ChatBinding(),
      
    ),
    GetPage(
      name: addMembers,
      page: () => AddMembersScreen(
        chatGroup: Get.arguments['chatGroup'],
      ),
      
    ),
    GetPage(
      name: searchConversation,
      page: () => SearchConversationScreen(
        chatGroup: Get.arguments['chatGroup'],
      ),
      
    ),
    GetPage(
      name: muteNotifications,
      page: () => MuteNotificationsScreen(
        chatGroup: Get.arguments['chatGroup'],
      ),
      
    ),

    // مسارات التقارير
    GetPage(
      name: reports,
      page: () => const ReportsScreen(),
      
    ),
  
    GetPage(
      name: createReport,
      page: () {
        // التحقق من وجود المعلومات المطلوبة وتوفير قيم افتراضية إذا لم تكن موجودة
        final args = Get.arguments ?? {};
        final title = args['title'] as String? ?? 'تقرير جديد';
        final description = args['description'] as String?;
        final reportType = args['reportType'] as String? ?? 'custom';
        final reportId = args['reportId'] as int?;

        return CustomReportBuilderScreen(
          title: title,
          description: description,
          reportType: reportType,
          reportId: reportId,
        );
      },
      
    ),
    GetPage(
      name: reportScheduler,
      page: () => ReportSchedulerScreen(
        reportId: Get.arguments['reportId'],
      ),
      
    ),
    GetPage(
      name: staticReports,
      page: () => const StaticReportsScreen(),
      
    ),
    GetPage(
      name: enhancedReports,
      page: () => const EnhancedReportsScreen(),
      
    ),
    GetPage(
      name: comprehensiveReportTest,
      page: () => const ComprehensiveReportTestScreen(),
      
    ),
    GetPage(
      name: enhancedCharts,
      page: () => const EnhancedChartsScreen(),
      
    ),
    GetPage(
      name: powerBI,
      page: () => const PowerBIScreen(),
      
    ),
    GetPage(
      name: dynamicPowerBI,
      page: () => const DynamicPowerBIReportScreen(),
      
    ),

    
    GetPage(
      name: reportBuilder,
      page: () => ReportBuilderScreen(
        report: Get.arguments?['report'],
      ),
      
    ),
    GetPage(
      name: reportViewer,
      page: () => ReportViewerScreen(
        reportId: Get.arguments['reportId'],
      ),
      
    ),
    GetPage(
      name: exportedReports,
      page: () => const ExportedReportsScreen(),
      
    ),

    // مسارات الإعدادات
    GetPage(
      name: syncSettings,
      page: () {
        // توجيه المستخدم إلى لوحة التحكم الإدارية
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // تأخير التنفيذ حتى يتم بناء الواجهة
          try {
            final tabController =
                Get.find<TabController>(tag: 'admin_tab_controller');
            tabController
                .animateTo(6); // تبويب إعدادات التزامن هو السابع (index 6)
          } catch (e) {
            debugPrint('خطأ في التوجيه إلى تبويب إعدادات التزامن: $e');
          }
        });
        return const AdminDashboardScreen();
      },
      
    ),
    GetPage(
      name: databaseRepair,
      page: () => const DatabaseRepairScreen(),
      
    ),
    GetPage(
      name: archiveSystemRepair,
      page: () => const ArchiveSystemRepairScreen(),
      
    ),

    // مسارات التقويم
    GetPage(
      name: calendar,
      page: () => const CalendarScreen(),
      
    ),
    GetPage(
      name: calendarEventDetails,
      page: () => CalendarEventDetails(
        event: Get.arguments['event'],
        onEventUpdated: Get.arguments['onEventUpdated'],
        onEventDeleted: Get.arguments['onEventDeleted'],
      ),
      
    ),

    // مسارات لوحة المعلومات
    GetPage(
      name: customizableDashboard,
      page: () => const CustomizableDashboardScreen(),
      binding: DashboardBinding(),
      
    ),
    GetPage(
      name: newDashboard,
      page: () => const NewDashboardScreen(),
      binding: DashboardBinding(),
      
    ),
    GetPage(
      name: mondayDashboard,
      page: () => const MondayDashboardScreen(),
      binding: DashboardBinding(),
      
    ),

    // مسار عرض المخطط بملء الشاشة
    GetPage(
      name: fullscreenChartView,
      page: () => FullscreenChartView(
        widget: Get.arguments['widget'],
        settings: Get.arguments['settings'] ?? {},
        onSettingsUpdated: Get.arguments['onSettingsUpdated'],
        title: Get.arguments['title'],
        chartKey: Get.arguments['chartKey'],
        chartType: Get.arguments['chartType'],
        startDate: Get.arguments['startDate'],
        endDate: Get.arguments['endDate'],
        filterType: Get.arguments['filterType'],
        onChartTypeChanged: Get.arguments['onChartTypeChanged'],
        onFilterChanged: Get.arguments['onFilterChanged'],
        chartContent: Get.arguments['chartContent'],
      ),
      
    ),

    // مسارات نظام التقارير بتصميم Monday.com
    GetPage(
      name: mondayStyleReports,
      page: () => const MondayStyleReportsScreen(),
      
    ),
    GetPage(
      name: mondayStyleReportViewer,
      page: () => MondayStyleReportViewerScreen(
        reportId: Get.arguments?['reportId'],
      ),
      
    ),

    // مسارات نظام الأرشفة الإلكترونية
    GetPage(
      name: archiveHome,
      page: () => const ArchiveHomeScreen(),
      
    ),
    GetPage(
      name: documentBrowser,
      page: () => const DocumentBrowserScreen(),
      
    ),
    GetPage(
      name: documentUpload,
      page: () => const DocumentUploadScreen(),
      
    ),
    GetPage(
      name: categoryManagement,
      page: () => const CategoryManagementScreen(),
      
    ),
    GetPage(
      name: tagManagement,
      page: () => const TagManagementScreen(),
      
    ),
    GetPage(
      name: documentVersionHistory,
      page: () => DocumentVersionHistoryScreen(
        documentId: Get.arguments['documentId'],
        documentTitle: Get.arguments['documentTitle'],
      ),
      
    ),
    GetPage(
      name: editDocument,
      page: () => EditDocumentScreen(
        document: Get.arguments['document'],
      ),
      
    ),

    // مسارات الأدوار والصلاحيات
    GetPage(
      name: roles,
      page: () {
        // توجيه المستخدم إلى لوحة التحكم الإدارية - تبويب إدارة الأدوار
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // تأخير التنفيذ حتى يتم بناء الواجهة
          try {
            final tabController =
                Get.find<TabController>(tag: 'admin_tab_controller');
            tabController
                .animateTo(2); // تبويب إدارة الأدوار هو الثالث (index 2)
          } catch (e) {
            debugPrint('خطأ في التوجيه إلى تبويب إدارة الأدوار: $e');
          }
        });
        return const AdminDashboardScreen();
      },
      
    ),
    // GetPage(
    //   name: permissionTest,
    //   page: () => const PermissionTestScreen(), // ملف غير موجود
    //   
    // ),

    
    // مسارات البحث
    GetPage(
      name: unifiedSearch,
      page: () => const UnifiedSearchScreen(),
      binding: SearchBinding(),
      
    ),

    // مسارات المستندات النصية
    GetPage(
      name: textDocumentsList,
      page: () => const AdvancedQuillEditorWidget(),
      binding: TextDocumentBinding(),
      
    ),
    GetPage(
      name: textDocumentEditor,
      page: () => TaskDocumentEditorScreen(
        // documentId: Get.arguments?['documentId'],
        taskId: Get.arguments?['taskId'],
        // defaultTitle: Get.arguments?['defaultTitle'],
        // defaultType: Get.arguments?['defaultType'],
      ),
    //   binding: TextDocumentBinding(),
    //   
     ),
   
    // مسارات الاختبار
    // GetPage(
    //   name: testMenu,
    //   // page: () => const TestMenuScreen(),
    //   // لا نحتاج middleware للاختبار - يمكن الوصول بدون تسجيل دخول
    // ),
   
    GetPage(
      name: fileStorageTest,
      page: () => const FileStorageTestScreen(taskId: 29,),
      // لا نحتاج middleware للاختبار - يمكن الوصول بدون تسجيل دخول
    ),

    GetPage(
      name: permissionsTest,
      page: () => const PermissionsTestScreen(),
      
    ),

    // مسارات نظام التقارير الاحترافية
    GetPage(
      name: professionalReports,
      page: () => const ReportsDashboardScreen(),
      
    ),

    // مسار تشخيص المشاكل
    GetPage(
      name: diagnostics,
      page: () => const DiagnosticsScreen(),
      
    ),

    // مسارات إدارة الصلاحيات المحسنة
    // GetPage(
    //   name: enhancedPermissionsManagement,
    //   page: () => const EnhancedPermissionsManagementScreen(),
    //   binding: EnhancedPermissionsBinding(),
    //   
    // ),
    // GetPage(
    //   name: userPermissionsViewer,
    //   page: () => UserPermissionsViewerScreen(
    //     userId: Get.arguments?['userId'],
    //   ),
    //   binding: EnhancedPermissionsBinding(),
    //   
    // ),
  ];
}

/// Widget محمي لشاشة الأقسام
class _ProtectedDepartmentsTab extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final permissionService = Get.find<UnifiedPermissionService>();

    // 🔒 التحقق من صلاحية الوصول لشاشة الأقسام
    if (!permissionService.canViewDepartments()) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('غير مسموح'),
          backgroundColor: Colors.red[700],
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.block, size: 64, color: Colors.red),
              SizedBox(height: 16),
              Text(
                'ليس لديك صلاحية لعرض الأقسام',
                style: TextStyle(fontSize: 18),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16),
              Text(
                'يرجى التواصل مع المدير للحصول على الصلاحيات المطلوبة',
                style: TextStyle(fontSize: 14, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // إذا كان لديه صلاحية، عرض شاشة الأقسام
    return const DepartmentsTab();
  }
}
