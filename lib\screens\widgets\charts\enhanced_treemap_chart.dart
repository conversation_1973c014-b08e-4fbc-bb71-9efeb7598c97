import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/advanced_filter_options.dart';
import 'package:flutter_application_2/models/chart_enums.dart';


import 'unified_filter_export_widget.dart';

/// نموذج بيانات الخريطة الشجرية
class TreemapItem {
  /// اسم العنصر
  final String label;

  /// قيمة العنصر
  final double value;

  /// لون العنصر (اختياري)
  final Color? color;

  /// العناصر الفرعية (اختياري)
  final List<TreemapItem>? children;

  /// إنشاء عنصر خريطة شجرية
  const TreemapItem({
    required this.label,
    required this.value,
    this.color,
    this.children,
  });
}

/// مكون مخطط الخريطة الشجرية المحسن
///
/// يوفر هذا المكون مخططًا للخريطة الشجرية مع دعم للتصفية والتصدير
class EnhancedTreemapChart extends StatefulWidget {
  /// بيانات المخطط
  final Map<String, double> data;

  /// عنوان المخطط
  final String? title;

  /// ألوان العناصر (اختياري)
  final Map<String, Color>? itemColors;

  /// هل يتم عرض القيم
  final bool showValues;

  /// هل يتم عرض النسب المئوية
  final bool showPercentages;

  /// دالة استدعاء عند تغيير التصفية
  final Function(DateTime?, DateTime?, TimeFilterType)? onFilterChanged;

  /// دالة استدعاء عند التصدير
  final Function(String)? onExport;

  /// دالة استدعاء عند تغيير نوع المخطط
  final Function(ChartType)? onChartTypeChanged;

  /// هل يتم عرض خيارات التصفية
  final bool showFilterOptions;

  /// هل يتم عرض خيارات التصدير
  final bool showExportOptions;

  /// هل يتم عرض خيارات نوع المخطط
  final bool showChartTypeOptions;

  /// تنسيقات التصدير المدعومة
  final List<String> supportedExportFormats;

  /// أنواع المخططات المدعومة
  final List<ChartType> supportedChartTypes;

  /// نوع المخطط
  final ChartType chartType;

  /// خيارات التصفية المتقدمة
  final AdvancedFilterOptions advancedFilterOptions;

  /// إنشاء مكون مخطط الخريطة الشجرية المحسن
  const EnhancedTreemapChart({
    super.key,
    required this.data,
    this.title,
    this.itemColors,
    this.showValues = true,
    this.showPercentages = true,
    this.onFilterChanged,
    this.onExport,
    this.onChartTypeChanged,
    this.showFilterOptions = false,
    this.showExportOptions = false,
    this.showChartTypeOptions = false,
    this.supportedExportFormats = const ['pdf', 'excel', 'csv', 'png'],
    this.supportedChartTypes = const [
      ChartType.treemap,
      ChartType.pie,
      ChartType.bar,
    ],
    required this.chartType,
    required this.advancedFilterOptions,
  });

  @override
  State<EnhancedTreemapChart> createState() => _EnhancedTreemapChartState();
}

class _EnhancedTreemapChartState extends State<EnhancedTreemapChart> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // التحقق من وجود بيانات
    if (widget.data.isEmpty) {
      return UnifiedFilterExportWidget.buildNoDataMessage(
        context,
        message: 'لا توجد بيانات للعرض.\nقد يكون ذلك بسبب الفلتر المطبق.',
        onCancelFilter: () {
          // إلغاء الفلتر وإعادة تعيينه إلى الكل
          if (widget.onFilterChanged != null) {
            widget.onFilterChanged!(null, null, TimeFilterType.all);
          }
        },
      );
    }

    return Column(
      children: [
        // مكون التصفية والتصدير (يمكن إضافته لاحقًا)

        // مخطط الخريطة الشجرية
        Expanded(
          child: _buildTreemap(),
        ),
      ],
    );
  }

  /// بناء مخطط الخريطة الشجرية
  Widget _buildTreemap() {
    // حساب إجمالي القيم
    final double totalValue =
        widget.data.values.fold(0, (sum, value) => sum + value);

    // تحويل البيانات إلى قائمة مرتبة تنازليًا
    final items = widget.data.entries.map((entry) {
      return TreemapItem(
        label: entry.key,
        value: entry.value,
        color: widget.itemColors?[entry.key] ?? _getRandomColor(entry.key),
      );
    }).toList();

    // ترتيب العناصر تنازليًا حسب القيمة
    items.sort((a, b) => b.value.compareTo(a.value));

    return LayoutBuilder(
      builder: (context, constraints) {
        return _TreemapLayout(
          items: items,
          totalValue: totalValue,
          showValues: widget.showValues,
          showPercentages: widget.showPercentages,
          width: constraints.maxWidth,
          height: constraints.maxHeight,
        );
      },
    );
  }

  /// الحصول على لون عشوائي بناءً على النص
  Color _getRandomColor(String text) {
    // استخدام قيمة هاش النص لإنشاء لون ثابت لنفس النص
    final int hash = text.hashCode;

    // إنشاء لون من قيمة الهاش
    return Color.fromARGB(
      255,
      (hash & 0xFF0000) >> 16,
      (hash & 0x00FF00) >> 8,
      hash & 0x0000FF,
    );
  }
}

/// مكون تخطيط الخريطة الشجرية
class _TreemapLayout extends StatelessWidget {
  final List<TreemapItem> items;
  final double totalValue;
  final bool showValues;
  final bool showPercentages;
  final double width;
  final double height;

  const _TreemapLayout({
    required this.items,
    required this.totalValue,
    required this.showValues,
    required this.showPercentages,
    required this.width,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(width, height),
      painter: _TreemapPainter(
        items: items,
        totalValue: totalValue,
        showValues: showValues,
        showPercentages: showPercentages,
      ),
    );
  }
}

/// رسام الخريطة الشجرية
class _TreemapPainter extends CustomPainter {
  final List<TreemapItem> items;
  final double totalValue;
  final bool showValues;
  final bool showPercentages;

  _TreemapPainter({
    required this.items,
    required this.totalValue,
    required this.showValues,
    required this.showPercentages,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // تنفيذ خوارزمية "squarified treemap"
    _drawTreemap(canvas, Rect.fromLTWH(0, 0, size.width, size.height), items);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;

  /// رسم الخريطة الشجرية
  void _drawTreemap(Canvas canvas, Rect rect, List<TreemapItem> items) {
    if (items.isEmpty || rect.isEmpty) return;

    // حساب إجمالي القيم للعناصر المتبقية
    final remainingTotal = items.fold(0.0, (sum, item) => sum + item.value);

    // تحديد الاتجاه (أفقي أو رأسي) بناءً على نسبة العرض إلى الارتفاع
    final isHorizontal = rect.width > rect.height;

    // حساب المساحة المتاحة
    final availableArea = rect.width * rect.height;

    // رسم العنصر الأول
    final item = items.first;
    final ratio = item.value / remainingTotal;
    final itemArea = availableArea * ratio;

    Rect itemRect;
    Rect remainingRect;

    if (isHorizontal) {
      // تقسيم أفقي
      final itemWidth = itemArea / rect.height;
      itemRect = Rect.fromLTWH(rect.left, rect.top, itemWidth, rect.height);
      remainingRect = Rect.fromLTWH(
          rect.left + itemWidth, rect.top, rect.width - itemWidth, rect.height);
    } else {
      // تقسيم رأسي
      final itemHeight = itemArea / rect.width;
      itemRect = Rect.fromLTWH(rect.left, rect.top, rect.width, itemHeight);
      remainingRect = Rect.fromLTWH(rect.left, rect.top + itemHeight,
          rect.width, rect.height - itemHeight);
    }

    // رسم العنصر الحالي
    _drawItem(canvas, itemRect, item);

    // رسم العناصر المتبقية
    if (items.length > 1) {
      _drawTreemap(canvas, remainingRect, items.sublist(1));
    }
  }

  /// رسم عنصر واحد
  void _drawItem(Canvas canvas, Rect rect, TreemapItem item) {
    // رسم المستطيل
    final paint = Paint()
      ..color = item.color ?? Colors.blue
      ..style = PaintingStyle.fill;

    canvas.drawRect(rect, paint);

    // رسم الحدود
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    canvas.drawRect(rect, borderPaint);

    // رسم النص
    final textPainter = TextPainter(
      text: TextSpan(
        text: item.label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.rtl,
    );

    textPainter.layout(maxWidth: rect.width - 8);

    // حساب موقع النص
    final textX = rect.left + 4;
    final textY = rect.top + 4;

    // رسم النص
    textPainter.paint(canvas, Offset(textX, textY));

    // رسم القيمة والنسبة المئوية
    if (showValues || showPercentages) {
      String valueText = '';

      if (showValues) {
        valueText = item.value.toStringAsFixed(0);
      }

      if (showPercentages) {
        final percentage = (item.value / totalValue * 100).toStringAsFixed(1);
        if (valueText.isNotEmpty) {
          valueText += ' ($percentage%)';
        } else {
          valueText = '$percentage%';
        }
      }

      final valueTextPainter = TextPainter(
        text: TextSpan(
          text: valueText,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
          ),
        ),
        textDirection: TextDirection.rtl,
      );

      valueTextPainter.layout(maxWidth: rect.width - 8);

      // حساب موقع النص
      final valueTextX = rect.left + 4;
      final valueTextY = textY + textPainter.height + 2;

      // رسم النص
      valueTextPainter.paint(canvas, Offset(valueTextX, valueTextY));
    }
  }
}
