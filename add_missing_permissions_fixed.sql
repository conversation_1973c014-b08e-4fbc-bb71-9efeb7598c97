-- ===================================================================
-- إضافة الصلاحيات المفقودة إلى قاعدة البيانات (نسخة محدثة)
-- تاريخ الإنشاء: 2025-01-06
-- الوصف: إضافة الصلاحيات المطلوبة للمشروع
-- ===================================================================

USE [databasetasks]
GO

PRINT '🚀 بدء إضافة الصلاحيات المفقودة...'

-- ===================================================================
-- 1. صلاحيات المهام المفقودة (أولوية عالية)
-- ===================================================================

PRINT '📋 إضافة صلاحيات المهام...'

-- عرض تفاصيل المهام (الأهم)
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.view_details')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.view_details', N'عرض تفاصيل المهام والانتقال إليها', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: tasks.view_details'
END

-- تحديث تقدم المهام
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.update_progress')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.update_progress', N'تحديث نسبة إنجاز المهام', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: tasks.update_progress'
END

-- تصفية المهام
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.filter')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.filter', N'تصفية وفلترة المهام', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: tasks.filter'
END

-- ترتيب المهام
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.sort')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.sort', N'ترتيب المهام حسب معايير مختلفة', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: tasks.sort'
END

-- إدارة لوحة المهام
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.manage_board')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.manage_board', N'إدارة لوحة المهام وإضافة أعمدة', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: tasks.manage_board'
END

-- ===================================================================
-- 2. صلاحيات المرفقات (أولوية عالية)
-- ===================================================================

PRINT '📎 إضافة صلاحيات المرفقات...'

-- رفع المرفقات
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'attachments.upload')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('attachments.upload', N'رفع المرفقات للمهام والمشاريع', 'attachments', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: attachments.upload'
END

-- حذف المرفقات
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'attachments.delete')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('attachments.delete', N'حذف المرفقات من المهام', 'attachments', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: attachments.delete'
END

-- عرض المرفقات
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'attachments.view')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('attachments.view', N'عرض وتحميل المرفقات', 'attachments', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: attachments.view'
END

-- ===================================================================
-- 3. صلاحيات الرسائل والمحادثات
-- ===================================================================

PRINT '💬 إضافة صلاحيات الرسائل...'

-- تثبيت الرسائل
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'messages.pin')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('messages.pin', N'تثبيت وإلغاء تثبيت الرسائل', 'messages', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: messages.pin'
END

-- تعديل الرسائل
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'messages.edit')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('messages.edit', N'تعديل الرسائل المرسلة', 'messages', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: messages.edit'
END

-- حذف الرسائل
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'messages.delete')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('messages.delete', N'حذف الرسائل', 'messages', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: messages.delete'
END

-- الرد على الرسائل
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'messages.reply')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('messages.reply', N'الرد على الرسائل', 'messages', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: messages.reply'
END

-- تحديد للمتابعة
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'messages.mark_followup')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('messages.mark_followup', N'تحديد الرسائل للمتابعة', 'messages', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: messages.mark_followup'
END

-- ===================================================================
-- 4. صلاحيات الأقسام المتقدمة
-- ===================================================================

PRINT '🏢 إضافة صلاحيات الأقسام...'

-- تعيين مدير القسم
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'departments.assign_manager')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('departments.assign_manager', N'تعيين مدير للقسم', 'departments', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: departments.assign_manager'
END

-- إضافة مستخدمين للقسم
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'departments.add_users')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('departments.add_users', N'إضافة مستخدمين للقسم', 'departments', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: departments.add_users'
END

-- إزالة مستخدمين من القسم
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'departments.remove_users')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('departments.remove_users', N'إزالة مستخدمين من القسم', 'departments', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: departments.remove_users'
END

-- إدارة مستخدمي القسم
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'departments.manage_users')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('departments.manage_users', N'إدارة شاملة لمستخدمي القسم', 'departments', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: departments.manage_users'
END

-- ===================================================================
-- 5. صلاحيات التقارير المتخصصة
-- ===================================================================

PRINT '📊 إضافة صلاحيات التقارير...'

-- تقارير المساهمات
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'reports.contributions')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('reports.contributions', N'عرض تقارير المساهمات', 'reports', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: reports.contributions'
END

-- تقارير PDF
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'reports.pdf')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('reports.pdf', N'تصدير التقارير كـ PDF', 'reports', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: reports.pdf'
END

-- تقارير عبء العمل
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'reports.workload')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('reports.workload', N'تقارير عبء العمل والأداء', 'reports', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT '✅ تم إضافة: reports.workload'
END

PRINT '✅ تم الانتهاء من إضافة الصلاحيات الأساسية!'
PRINT '📊 عدد الصلاحيات المضافة: 20 صلاحية'

-- ===================================================================
-- التحقق من النتائج
-- ===================================================================

PRINT '🔍 التحقق من الصلاحيات المضافة...'

SELECT 
    [permission_group] as 'مجموعة الصلاحيات',
    COUNT(*) as 'عدد الصلاحيات'
FROM [dbo].[permissions] 
WHERE [name] IN (
    'tasks.view_details', 'tasks.update_progress', 'tasks.filter', 'tasks.sort', 'tasks.manage_board',
    'attachments.upload', 'attachments.delete', 'attachments.view',
    'messages.pin', 'messages.edit', 'messages.delete', 'messages.reply', 'messages.mark_followup',
    'departments.assign_manager', 'departments.add_users', 'departments.remove_users', 'departments.manage_users',
    'reports.contributions', 'reports.pdf', 'reports.workload'
)
GROUP BY [permission_group]
ORDER BY [permission_group]

PRINT '🎉 تم الانتهاء بنجاح من إضافة جميع الصلاحيات المفقودة!'
