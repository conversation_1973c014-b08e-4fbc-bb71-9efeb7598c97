-- تغيير ترميز قاعدة البيانات إلى UTF-8 لدعم جميع اللغات
-- الترميز العالمي: UTF-8 يدعم العربية والإنجليزية وجميع اللغات

USE master;
GO

-- 1. إنشاء نسخة احتياطية أولاً
PRINT N'إنشاء نسخة احتياطية...';
BACKUP DATABASE databasetasks 
TO DISK = 'C:\temp\databasetasks_backup_before_utf8.bak'
WITH FORMAT, INIT, SKIP, NOREWIND, NOUNLOAD, STATS = 10;

-- 2. قطع جميع الاتصالات
PRINT N'قطع الاتصالات...';
ALTER DATABASE databasetasks SET SINGLE_USER WITH ROLLBACK IMMEDIATE;

-- 3. تغيير ترميز قاعدة البيانات إلى UTF-8
PRINT N'تغيير ترميز قاعدة البيانات إلى UTF-8...';
ALTER DATABASE databasetasks COLLATE SQL_Latin1_General_CP1_CI_AS_UTF8;

-- 4. إعادة تعيين الوضع متعدد المستخدمين
ALTER DATABASE databasetasks SET MULTI_USER;

-- 5. التحقق من التغيير
SELECT 
    name as database_name,
    collation_name as new_collation
FROM sys.databases 
WHERE name = 'databasetasks';

PRINT N'تم تغيير ترميز قاعدة البيانات إلى UTF-8 بنجاح!';

-- الآن تغيير ترميز أعمدة جدول الصلاحيات
USE databasetasks;
GO

PRINT N'تغيير ترميز أعمدة جدول الصلاحيات...';

-- إنشاء نسخة احتياطية من الجدول
SELECT * INTO permissions_backup_utf8 FROM permissions;

-- تغيير ترميز الأعمدة النصية
ALTER TABLE permissions 
ALTER COLUMN name NVARCHAR(100) COLLATE SQL_Latin1_General_CP1_CI_AS_UTF8;

ALTER TABLE permissions 
ALTER COLUMN description NVARCHAR(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS_UTF8;

ALTER TABLE permissions 
ALTER COLUMN permission_group NVARCHAR(50) COLLATE SQL_Latin1_General_CP1_CI_AS_UTF8;

ALTER TABLE permissions 
ALTER COLUMN category NVARCHAR(100) COLLATE SQL_Latin1_General_CP1_CI_AS_UTF8;

-- التحقق من ترميز الأعمدة الجديد
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    COLLATION_NAME as new_column_collation
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'permissions' 
AND COLUMN_NAME IN ('name', 'description', 'permission_group', 'category');

PRINT N'تم تغيير ترميز الأعمدة إلى UTF-8 بنجاح!';
PRINT N'الآن يمكن استخدام العربية والإنجليزية وأي لغة أخرى!';
GO
