# 🎯 خطة تطبيق الصلاحيات الشاملة على المشروع

## 📋 نظرة عامة
تطبيق نظام الصلاحيات على **جميع** نقاط التنقل والأزرار في المشروع بدون استثناء.

## 🔍 تحليل الشاشات والمكونات

### 1. 🏠 الشاشة الرئيسية والتنقل
**الملفات:**
- `lib/screens/home/<USER>
- `lib/screens/widgets/app_drawer.dart` - القائمة الجانبية
- `lib/widgets/notifications_button.dart` - زر الإشعارات

**نقاط التطبيق:**
- ✅ التبويبات الرئيسية (مطبق جزئياً)
- ❌ أزرار شريط التطبيق (المساعدة، البحث، السمة)
- ❌ عناصر القائمة الجانبية
- ❌ زر الإشعارات

### 2. 📋 شاشات المهام
**الملفات:**
- `lib/screens/home/<USER>
- `lib/screens/tasks/create_task_screen.dart` - إنشاء مهمة
- `lib/screens/tasks/task_detail_screen.dart` - تفاصيل المهمة
- `lib/screens/tasks/inline_edit_task_screen.dart` - تعديل المهمة
- `lib/screens/tasks/task_board_screen.dart` - لوحة المهام
- `lib/screens/tasks/task_gantt_chart_screen.dart` - مخطط جانت

**نقاط التطبيق:**
- ❌ زر إنشاء مهمة جديدة (FloatingActionButton)
- ❌ أزرار تعديل/حذف المهام
- ❌ أزرار تغيير حالة المهمة
- ❌ أزرار تعيين المهام
- ❌ أزرار التصدير والطباعة
- ❌ أزرار العرض المختلفة (جانت، لوحة، قائمة)

### 3. 👥 شاشات المستخدمين والإدارة
**الملفات:**
- `lib/screens/admin/admin_dashboard_new.dart` - لوحة الإدارة
- `lib/screens/admin/users/user_management_screen.dart` - إدارة المستخدمين
- `lib/screens/admin/roles/role_management_screen.dart` - إدارة الأدوار
- `lib/screens/admin/permissions/permission_management_screen.dart` - إدارة الصلاحيات

**نقاط التطبيق:**
- ❌ زر إضافة مستخدم جديد
- ❌ أزرار تعديل/حذف المستخدمين
- ❌ أزرار إدارة الأدوار
- ❌ أزرار منح/إلغاء الصلاحيات
- ❌ أزرار النسخ الاحتياطي والاستعادة

### 4. 📊 شاشات التقارير والإحصائيات
**الملفات:**
- `lib/screens/reports/` - جميع شاشات التقارير
- `lib/screens/power_bi/` - شاشات Power BI
- `lib/professional_reports/` - التقارير الاحترافية

**نقاط التطبيق:**
- ❌ أزرار إنشاء التقارير
- ❌ أزرار تصدير التقارير
- ❌ أزرار جدولة التقارير
- ❌ أزرار مشاركة التقارير
- ❌ أزرار Power BI

### 5. 📁 شاشات الأرشيف والملفات
**الملفات:**
- `lib/screens/archive/` - جميع شاشات الأرشيف
- `lib/screens/files/` - شاشات الملفات
- `lib/screens/documents/` - شاشات المستندات

**نقاط التطبيق:**
- ❌ أزرار رفع الملفات
- ❌ أزرار تحميل الملفات
- ❌ أزرار حذف الملفات
- ❌ أزرار إدارة التصنيفات
- ❌ أزرار إدارة الوسوم

### 6. 💬 شاشات المحادثات والتواصل
**الملفات:**
- `lib/screens/chat/` - جميع شاشات المحادثات
- `lib/screens/notifications/` - شاشات الإشعارات
- `lib/screens/calendar/` - شاشات التقويم

**نقاط التطبيق:**
- ❌ أزرار إرسال الرسائل
- ❌ أزرار إنشاء المجموعات
- ❌ أزرار إدارة الأعضاء
- ❌ أزرار إنشاء الأحداث
- ❌ أزرار إدارة الإشعارات

### 7. ⚙️ شاشات الإعدادات والنظام
**الملفات:**
- `lib/screens/settings/` - جميع شاشات الإعدادات
- `lib/screens/admin/system/` - إعدادات النظام

**نقاط التطبيق:**
- ❌ أزرار تغيير كلمة المرور
- ❌ أزرار إعدادات السمة
- ❌ أزرار إعدادات اللغة
- ❌ أزرار إعدادات النظام
- ❌ أزرار إدارة قاعدة البيانات

## 🛠️ استراتيجية التطبيق

### 1. إنشاء مكون مساعد للصلاحيات
```dart
class PermissionWrapper extends StatelessWidget {
  final String permission;
  final Widget child;
  final Widget? fallback;
  
  const PermissionWrapper({
    required this.permission,
    required this.child,
    this.fallback,
  });
}
```

### 2. إنشاء دوال مساعدة للأزرار
```dart
class PermissionButton {
  static Widget elevatedButton({
    required String permission,
    required VoidCallback onPressed,
    required Widget child,
  });
  
  static Widget iconButton({
    required String permission,
    required VoidCallback onPressed,
    required Widget icon,
  });
}
```

### 3. تطبيق نمط موحد
- التحقق من الصلاحية قبل عرض الزر
- إخفاء الزر إذا لم تكن الصلاحية متاحة
- عرض رسالة مناسبة عند عدم وجود صلاحية

## 📝 قائمة المهام التفصيلية

### المرحلة 1: إنشاء المكونات المساعدة
- [ ] إنشاء `PermissionWrapper`
- [ ] إنشاء `PermissionButton`
- [ ] إنشاء `PermissionIconButton`
- [ ] إنشاء `PermissionFloatingActionButton`

### المرحلة 2: تطبيق على الشاشة الرئيسية
- [ ] تطبيق على أزرار شريط التطبيق
- [ ] تطبيق على عناصر القائمة الجانبية
- [ ] تطبيق على زر الإشعارات

### المرحلة 3: تطبيق على شاشات المهام
- [ ] تطبيق على أزرار إنشاء المهام
- [ ] تطبيق على أزرار تعديل المهام
- [ ] تطبيق على أزرار حذف المهام
- [ ] تطبيق على أزرار تغيير الحالة

### المرحلة 4: تطبيق على باقي الشاشات
- [ ] شاشات المستخدمين والإدارة
- [ ] شاشات التقارير والإحصائيات
- [ ] شاشات الأرشيف والملفات
- [ ] شاشات المحادثات والتواصل
- [ ] شاشات الإعدادات والنظام

## 🎯 الهدف النهائي
تطبيق نظام صلاحيات شامل وموحد على **جميع** نقاط التفاعل في التطبيق بحيث:
- لا يظهر أي زر أو رابط بدون التحقق من الصلاحية
- تكون تجربة المستخدم سلسة ومتسقة
- يكون الكود منظم وقابل للصيانة
