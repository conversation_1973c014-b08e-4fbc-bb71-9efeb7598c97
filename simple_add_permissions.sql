-- إضافة الصلاحيات المفقودة الأساسية
USE [databasetasks]
GO

-- إضافة صلاحية عرض تفاصيل المهام
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.view_details')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.view_details', N'عرض تفاصيل المهام والانتقال إليها', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT 'تم إضافة: tasks.view_details'
END

-- إضافة صلاحية تحديث تقدم المهام
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.update_progress')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.update_progress', N'تحديث نسبة إنجاز المهام', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT 'تم إضافة: tasks.update_progress'
END

-- إضافة صلاحية تصفية المهام
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.filter')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.filter', N'تصفية وفلترة المهام', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT 'تم إضافة: tasks.filter'
END

-- إضافة صلاحية ترتيب المهام
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.sort')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.sort', N'ترتيب المهام حسب معايير مختلفة', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT 'تم إضافة: tasks.sort'
END

-- إضافة صلاحية إدارة لوحة المهام
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.manage_board')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.manage_board', N'إدارة لوحة المهام وإضافة أعمدة', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT 'تم إضافة: tasks.manage_board'
END

-- إضافة صلاحية تغيير حالة المهام
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.change_status')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.change_status', N'تغيير حالة المهام عبر السحب والإفلات', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT 'تم إضافة: tasks.change_status'
END

-- إضافة صلاحية تغيير أولوية المهام
IF NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.change_priority')
BEGIN
    INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
    VALUES ('tasks.change_priority', N'تغيير أولوية المهام', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE()))
    PRINT 'تم إضافة: tasks.change_priority'
END

-- التحقق من النتائج
SELECT COUNT(*) as 'عدد الصلاحيات المضافة' FROM [dbo].[permissions] 
WHERE [name] IN ('tasks.view_details', 'tasks.update_progress', 'tasks.filter', 'tasks.sort', 'tasks.manage_board', 'tasks.change_status', 'tasks.change_priority')

PRINT 'تم الانتهاء من إضافة الصلاحيات الأساسية'
