using System.ComponentModel.DataAnnotations;

namespace webApi.Models
{
    /// <summary>
    /// نموذج قالب الصلاحيات
    /// </summary>
    public class PermissionTemplate
    {
        /// <summary>
        /// معرف القالب
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// اسم القالب
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = null!;

        /// <summary>
        /// وصف القالب
        /// </summary>
        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// نوع القالب (admin, manager, employee, viewer, custom)
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string Type { get; set; } = null!;

        /// <summary>
        /// لون القالب (للعرض في الواجهة)
        /// </summary>
        [MaxLength(20)]
        public string? Color { get; set; }

        /// <summary>
        /// أيقونة القالب
        /// </summary>
        [MaxLength(50)]
        public string? Icon { get; set; }

        /// <summary>
        /// هل القالب نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// هل القالب افتراضي (لا يمكن حذفه)
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// معرف المستخدم الذي أنشأ القالب
        /// </summary>
        public int CreatedBy { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public long CreatedAt { get; set; }

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public long? UpdatedAt { get; set; }

        /// <summary>
        /// معرف المستخدم الذي قام بآخر تحديث
        /// </summary>
        public int? UpdatedBy { get; set; }

        // Navigation properties
        public virtual User CreatedByNavigation { get; set; } = null!;
        public virtual User? UpdatedByNavigation { get; set; }
        public virtual ICollection<PermissionTemplateItem> TemplateItems { get; set; } = new List<PermissionTemplateItem>();
    }

    /// <summary>
    /// نموذج عنصر قالب الصلاحيات
    /// </summary>
    public class PermissionTemplateItem
    {
        /// <summary>
        /// معرف العنصر
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// معرف القالب
        /// </summary>
        public int TemplateId { get; set; }

        /// <summary>
        /// معرف الصلاحية
        /// </summary>
        public int PermissionId { get; set; }

        /// <summary>
        /// هل الصلاحية مفعلة في القالب
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        // Navigation properties
        public virtual PermissionTemplate Template { get; set; } = null!;
        public virtual Permission Permission { get; set; } = null!;
    }

    /// <summary>
    /// نموذج طلب إنشاء قالب صلاحيات
    /// </summary>
    public class CreatePermissionTemplateRequest
    {
        /// <summary>
        /// اسم القالب
        /// </summary>
        [Required]
        public string Name { get; set; } = null!;

        /// <summary>
        /// وصف القالب
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// نوع القالب
        /// </summary>
        [Required]
        public string Type { get; set; } = null!;

        /// <summary>
        /// لون القالب
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// أيقونة القالب
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// قائمة معرفات الصلاحيات
        /// </summary>
        [Required]
        public List<int> PermissionIds { get; set; } = new List<int>();

        /// <summary>
        /// معرف المستخدم المنشئ
        /// </summary>
        [Required]
        public int CreatedBy { get; set; }
    }

    /// <summary>
    /// نموذج تحديث قالب الصلاحيات
    /// </summary>
    public class UpdatePermissionTemplateRequest
    {
        /// <summary>
        /// اسم القالب
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// وصف القالب
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// لون القالب
        /// </summary>
        public string? Color { get; set; }

        /// <summary>
        /// أيقونة القالب
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// هل القالب نشط
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// قائمة معرفات الصلاحيات الجديدة
        /// </summary>
        public List<int>? PermissionIds { get; set; }

        /// <summary>
        /// معرف المستخدم المحدث
        /// </summary>
        [Required]
        public int UpdatedBy { get; set; }
    }

    /// <summary>
    /// نموذج تطبيق قالب على مستخدم
    /// </summary>
    public class ApplyTemplateToUserRequest
    {
        /// <summary>
        /// معرف القالب
        /// </summary>
        [Required]
        public int TemplateId { get; set; }

        /// <summary>
        /// معرف المستخدم
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// هل يتم استبدال الصلاحيات الحالية أم إضافة إليها
        /// </summary>
        public bool ReplaceExisting { get; set; } = true;

        /// <summary>
        /// معرف المستخدم الذي يطبق القالب
        /// </summary>
        [Required]
        public int AppliedBy { get; set; }
    }
}
