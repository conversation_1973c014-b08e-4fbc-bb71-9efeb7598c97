import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/chart_enums.dart';


import 'unified_filter_export_widget.dart';

/// نموذج بيانات القمع
class FunnelItem {
  /// اسم المرحلة
  final String label;

  /// قيمة المرحلة
  final double value;

  /// لون المرحلة (اختياري)
  final Color? color;

  /// إنشاء عنصر قمع
  const FunnelItem({
    required this.label,
    required this.value,
    this.color,
  });
}

/// مكون مخطط القمع المحسن
///
/// يوفر هذا المكون مخططًا قمعيًا مع دعم للتصفية والتصدير
class EnhancedFunnelChart extends StatefulWidget {
  /// بيانات المخطط
  final Map<String, double> data;

  /// عنوان المخطط
  final String? title;

  /// ألوان العناصر (اختياري)
  final Map<String, Color>? itemColors;

  /// هل يتم عرض القيم
  final bool showValues;

  /// هل يتم عرض النسب المئوية
  final bool showPercentages;

  /// دالة استدعاء عند تغيير التصفية
  final Function(DateTime?, DateTime?, TimeFilterType)? onFilterChanged;

  /// دالة استدعاء عند التصدير
  final Function(String)? onExport;

  /// دالة استدعاء عند تغيير نوع المخطط
  final Function(ChartType)? onChartTypeChanged;

  /// هل يتم عرض خيارات التصفية
  final bool showFilterOptions;

  /// هل يتم عرض خيارات التصدير
  final bool showExportOptions;

  /// هل يتم عرض خيارات نوع المخطط
  final bool showChartTypeOptions;

  /// تنسيقات التصدير المدعومة
  final List<String> supportedExportFormats;

  /// أنواع المخططات المدعومة
  final List<ChartType> supportedChartTypes;

  /// إنشاء مكون مخطط القمع المحسن
  const EnhancedFunnelChart({
    super.key,
    required this.data,
    this.title,
    this.itemColors,
    this.showValues = true,
    this.showPercentages = true,
    this.onFilterChanged,
    this.onExport,
    this.onChartTypeChanged,
    this.showFilterOptions = false,
    this.showExportOptions = false,
    this.showChartTypeOptions = false,
    this.supportedExportFormats = const ['pdf', 'excel', 'csv', 'png'],
    this.supportedChartTypes = const [
      ChartType.funnel,
      ChartType.pie,
      ChartType.bar,
    ],
  });

  @override
  State<EnhancedFunnelChart> createState() => _EnhancedFunnelChartState();
}

class _EnhancedFunnelChartState extends State<EnhancedFunnelChart> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // التحقق من وجود بيانات
    if (widget.data.isEmpty) {
      return UnifiedFilterExportWidget.buildNoDataMessage(
        context,
        message: 'لا توجد بيانات للعرض.\nقد يكون ذلك بسبب الفلتر المطبق.',
        onCancelFilter: () {
          // إلغاء الفلتر وإعادة تعيينه إلى الكل
          if (widget.onFilterChanged != null) {
            widget.onFilterChanged!(null, null, TimeFilterType.all);
          }
        },
      );
    }

    return Column(
      children: [
        // مكون التصفية والتصدير (يمكن إضافته لاحقًا)

        // مخطط القمع
        Expanded(
          child: _buildFunnelChart(),
        ),
      ],
    );
  }

  /// بناء مخطط القمع
  Widget _buildFunnelChart() {
    // حساب إجمالي القيم
    final double totalValue =
        widget.data.values.fold(0, (sum, value) => sum + value);

    // تحويل البيانات إلى قائمة مرتبة تنازليًا
    final items = widget.data.entries.map((entry) {
      return FunnelItem(
        label: entry.key,
        value: entry.value,
        color: widget.itemColors?[entry.key] ?? _getRandomColor(entry.key),
      );
    }).toList();

    // ترتيب العناصر تنازليًا حسب القيمة
    items.sort((a, b) => b.value.compareTo(a.value));

    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          children: [
            // إضافة مفتاح الألوان (legend)
            if (items.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Wrap(
                  spacing: 16,
                  runSpacing: 8,
                  alignment: WrapAlignment.center,
                  children: items.map((item) {
                    final percentage =
                        (item.value / totalValue * 100).toStringAsFixed(1);
                    return Tooltip(
                      message:
                          '${item.label}: ${item.value.toStringAsFixed(0)} ($percentage%)',
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: item.color,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            item.label,
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),

            // مخطط القمع
            Expanded(
              child: _FunnelLayout(
                items: items,
                totalValue: totalValue,
                showValues: widget.showValues,
                showPercentages: widget.showPercentages,
                width: constraints.maxWidth,
                height: constraints.maxHeight - (items.isEmpty ? 0 : 40),
                onItemTap: (item) {
                  // عرض معلومات إضافية عند النقر على عنصر
                  final percentage =
                      (item.value / totalValue * 100).toStringAsFixed(1);
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: Text(item.label),
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('القيمة: ${item.value.toStringAsFixed(0)}'),
                          const SizedBox(height: 8),
                          Text('النسبة المئوية: $percentage%'),
                        ],
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('إغلاق'),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  /// الحصول على لون عشوائي بناءً على النص
  Color _getRandomColor(String text) {
    // استخدام قيمة هاش النص لإنشاء لون ثابت لنفس النص
    final int hash = text.hashCode;

    // إنشاء لون من قيمة الهاش
    return Color.fromARGB(
      255,
      (hash & 0xFF0000) >> 16,
      (hash & 0x00FF00) >> 8,
      hash & 0x0000FF,
    );
  }
}

/// مكون تخطيط القمع
class _FunnelLayout extends StatelessWidget {
  final List<FunnelItem> items;
  final double totalValue;
  final bool showValues;
  final bool showPercentages;
  final double width;
  final double height;
  final Function(FunnelItem)? onItemTap;

  const _FunnelLayout({
    required this.items,
    required this.totalValue,
    required this.showValues,
    required this.showPercentages,
    required this.width,
    required this.height,
    this.onItemTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapUp: (details) {
        if (onItemTap != null) {
          // تحديد العنصر المنقور عليه
          final tappedItem = _findTappedItem(details.localPosition);
          if (tappedItem != null) {
            onItemTap!(tappedItem);
          }
        }
      },
      child: CustomPaint(
        size: Size(width, height),
        painter: _FunnelPainter(
          items: items,
          totalValue: totalValue,
          showValues: showValues,
          showPercentages: showPercentages,
        ),
      ),
    );
  }

  /// البحث عن العنصر المنقور عليه
  FunnelItem? _findTappedItem(Offset position) {
    if (items.isEmpty) return null;

    final double itemHeight = height / items.length;
    final int index = (position.dy / itemHeight).floor();

    if (index >= 0 && index < items.length) {
      return items[index];
    }

    return null;
  }
}

/// رسام القمع
class _FunnelPainter extends CustomPainter {
  final List<FunnelItem> items;
  final double totalValue;
  final bool showValues;
  final bool showPercentages;

  _FunnelPainter({
    required this.items,
    required this.totalValue,
    required this.showValues,
    required this.showPercentages,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (items.isEmpty) return;

    final double maxWidth = size.width * 0.8; // عرض أعلى القمع
    final double minWidth = size.width * 0.3; // عرض أسفل القمع
    final double itemHeight = size.height / items.length;
    final double widthDiff = maxWidth - minWidth;

    // رسم كل عنصر في القمع
    for (int i = 0; i < items.length; i++) {
      final item = items[i];
      final double topWidth = maxWidth - (widthDiff * i / items.length);
      final double bottomWidth =
          maxWidth - (widthDiff * (i + 1) / items.length);
      final double top = i * itemHeight;
      final double bottom = (i + 1) * itemHeight;

      // رسم شكل القمع
      final path = Path();
      path.moveTo((size.width - topWidth) / 2, top);
      path.lineTo((size.width + topWidth) / 2, top);
      path.lineTo((size.width + bottomWidth) / 2, bottom);
      path.lineTo((size.width - bottomWidth) / 2, bottom);
      path.close();

      // رسم العنصر
      final paint = Paint()
        ..color = item.color ?? Colors.blue
        ..style = PaintingStyle.fill;

      canvas.drawPath(path, paint);

      // رسم الحدود
      final borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.0;

      canvas.drawPath(path, borderPaint);

      // رسم النص
      _drawText(
        canvas,
        item.label,
        Rect.fromLTRB(
          (size.width - topWidth) / 2,
          top,
          (size.width + topWidth) / 2,
          bottom,
        ),
        item.value,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;

  /// رسم النص داخل العنصر
  void _drawText(Canvas canvas, String label, Rect rect, double value) {
    // رسم التسمية
    final textPainter = TextPainter(
      text: TextSpan(
        text: label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.rtl,
    );

    textPainter.layout(maxWidth: rect.width - 16);

    // حساب موقع النص
    final textX = rect.center.dx - textPainter.width / 2;
    final textY = rect.center.dy - textPainter.height;

    // رسم النص
    textPainter.paint(canvas, Offset(textX, textY));

    // رسم القيمة والنسبة المئوية
    if (showValues || showPercentages) {
      String valueText = '';

      if (showValues) {
        valueText = value.toStringAsFixed(0);
      }

      if (showPercentages) {
        final percentage = (value / totalValue * 100).toStringAsFixed(1);
        if (valueText.isNotEmpty) {
          valueText += ' ($percentage%)';
        } else {
          valueText = '$percentage%';
        }
      }

      final valueTextPainter = TextPainter(
        text: TextSpan(
          text: valueText,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
        ),
        textDirection: TextDirection.rtl,
      );

      valueTextPainter.layout(maxWidth: rect.width - 16);

      // حساب موقع النص
      final valueTextX = rect.center.dx - valueTextPainter.width / 2;
      final valueTextY = rect.center.dy;

      // رسم النص
      valueTextPainter.paint(canvas, Offset(valueTextX, valueTextY));
    }
  }
}
