-- ==========================================
-- سكريبت إضافة الصلاحيات من ملف CSV (محسن)
-- تاريخ الإنشاء: 2025-01-12
-- الهدف: إضافة الصلاحيات الجديدة فقط مع تجنب التكرار التام
-- متوافق مع هيكل جدول permissions الحالي
-- ==========================================

USE databasetasks;
GO

SET NOCOUNT ON;

-- التحقق من وجود جدول الصلاحيات
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'permissions')
BEGIN
    PRINT '❌ خطأ: جدول permissions غير موجود!'
    RETURN
END

PRINT '🚀 بدء تحليل وإضافة الصلاحيات من ملف CSV...'
PRINT '=================================================='

-- إنشاء جدول مؤقت للصلاحيات من ملف CSV
IF OBJECT_ID('tempdb..#CsvPermissions') IS NOT NULL
    DROP TABLE #CsvPermissions

CREATE TABLE #CsvPermissions (
    csv_id INT,
    name NVARCHAR(100),
    description NVARCHAR(MAX),
    permission_group NVARCHAR(50),
    created_at BIGINT,
    updated_at BIGINT,
    category NVARCHAR(100),
    level INT,
    icon NVARCHAR(50),
    color NVARCHAR(20),
    is_default BIT,
    is_active BIT,
    screen_id INT,
    action_id INT
)

-- إدراج البيانات من ملف CSV (الجزء الأول - الصلاحيات الأساسية)
INSERT INTO #CsvPermissions VALUES
-- مجموعة Dashboard
(40, 'dashboard.admin', N'إدارة لوحة المعلومات', N'Dashboard', 1750176586, 1751832925, N'إدارة', 4, NULL, NULL, 1, 1, NULL, NULL),
(1123, 'dashboard.edit', N'تعديل لوحة المعلومات', N'Dashboard', **********, NULL, N'تعديل', 3, 'edit', '#3F51B5', 0, 1, NULL, NULL),
(1124, 'dashboard.customize', N'تخصيص لوحة المعلومات', N'Dashboard', **********, NULL, N'تخصيص', 2, 'tune', '#3F51B5', 0, 1, NULL, NULL),

-- مجموعة Tasks
(41, 'tasks.view', N'عرض المهام', N'Tasks', 1750176586, 1751832925, N'عرض', 1, NULL, NULL, 1, 1, NULL, NULL),
(42, 'tasks.create', N'إنشاء مهام جديدة', N'Tasks', 1750176586, 1751832925, N'إنشاء', 1, NULL, NULL, 1, 1, NULL, NULL),
(43, 'tasks.edit', N'تعديل المهام', N'Tasks', 1750176586, 1751832925, N'تعديل', 1, NULL, NULL, 1, 1, NULL, NULL),
(44, 'tasks.delete', N'حذف المهام', N'Tasks', 1750176586, 1751832925, N'حذف', 1, NULL, NULL, 1, 1, NULL, NULL),
(45, 'tasks.assign', N'تعيين المهام للمستخدمين', N'Tasks', 1750176586, 1751832925, N'تعيين', 1, NULL, NULL, 1, 1, NULL, NULL),
(46, 'tasks.update_own', N'تحديث المهام الخاصة', N'Tasks', 1750176586, 1751832925, N'تحديث', 1, NULL, NULL, 1, 1, NULL, NULL),
(71, 'tasks.view_all', N'عرض جميع المهام', N'Tasks', 1750176586, 1751832925, N'عرض متقدم', 5, NULL, NULL, 0, 1, NULL, NULL),
(1129, 'tasks.transfer', N'تحويل المهام', N'Tasks', **********, NULL, N'تحويل', 2, 'swap_horiz', '#4CAF50', 0, 1, NULL, NULL),
(1130, 'tasks.duplicate', N'تكرار المهام', N'Tasks', **********, NULL, N'تكرار', 1, 'content_copy', '#4CAF50', 1, 1, NULL, NULL),

-- مجموعة Users
(47, 'users.view', N'عرض المستخدمين', N'Users', 1750176586, 1751832925, N'عرض', 1, NULL, NULL, 1, 1, NULL, NULL),
(48, 'users.create', N'إنشاء مستخدمين جدد', N'Users', 1750176586, 1751832925, N'إنشاء', 3, NULL, NULL, 1, 1, NULL, NULL),
(49, 'users.edit', N'تعديل بيانات المستخدمين', N'Users', 1750176586, 1751832925, N'تعديل', 3, NULL, NULL, 1, 1, NULL, NULL),
(51, 'users.manage_roles', N'إدارة أدوار المستخدمين', N'Users', 1750176586, 1751832925, N'إدارة', 4, NULL, NULL, 1, 1, NULL, NULL),
(72, 'users.manage_permissions', N'إدارة صلاحيات المستخدمين', N'Users', 1751646909, NULL, N'إدارة متقدمة', 4, 'security', '#E91E63', 0, 1, NULL, NULL),

-- مجموعة Reports
(52, 'reports.view', N'عرض التقارير', N'Reports', 1750176586, 1751832925, N'عرض', 2, NULL, NULL, 1, 1, NULL, NULL),
(53, 'reports.create', N'إنشاء تقارير جديدة', N'Reports', 1750176586, 1751832925, N'إنشاء', 3, NULL, NULL, 1, 1, NULL, NULL),
(54, 'reports.export', N'تصدير التقارير', N'Reports', 1750176586, 1751832925, N'تصدير', 2, NULL, NULL, 1, 1, NULL, NULL),
(1095, 'reports.edit', N'تعديل التقارير', N'Reports', **********, NULL, N'تعديل', 2, 'edit', '#795548', 0, 1, NULL, NULL),
(1096, 'reports.delete', N'حذف التقارير', N'Reports', **********, NULL, N'حذف', 3, 'delete', '#F44336', 0, 1, NULL, NULL),

-- مجموعة System
(55, 'system.manage', N'إدارة النظام', N'System', 1750176586, 1751832925, N'إدارة', 5, NULL, NULL, 1, 1, NULL, NULL),
(56, 'system.backup', N'إنشاء نسخ احتياطية', N'System', 1750176586, 1751832925, N'نسخ احتياطي', 5, NULL, NULL, 1, 1, NULL, NULL),
(57, 'system.restore', N'استعادة النسخ الاحتياطية', N'System', 1750176586, 1751832925, N'استعادة', 5, NULL, NULL, 1, 1, NULL, NULL),
(58, 'database.manage', N'إدارة قاعدة البيانات', N'System', 1750176586, 1751832925, N'إدارة', 5, NULL, NULL, 1, 1, NULL, NULL),
(83, 'system.logs', N'عرض سجلات النظام', N'System', 1751646909, NULL, N'مراقبة', 4, 'description', '#9E9E9E', 0, 1, NULL, NULL),

-- مجموعة Chat
(73, 'chat.view', N'الوصول للمحادثات', N'Chat', 1751646909, NULL, N'عرض', 1, 'chat', '#00BCD4', 1, 1, NULL, NULL),
(74, 'chat.send', N'إرسال الرسائل', N'Chat', 1751646909, NULL, N'إرسال', 1, 'send', '#00BCD4', 1, 1, NULL, NULL),
(75, 'chat.delete_messages', N'حذف الرسائل', N'Chat', 1751646909, NULL, N'حذف', 3, 'delete', '#F44336', 0, 1, NULL, NULL),
(1103, 'chat.create_group', N'إنشاء مجموعات محادثة', N'Chat', **********, NULL, N'إنشاء', 2, 'group_add', '#00BCD4', 0, 1, NULL, NULL),

-- مجموعة Archive
(77, 'archive.view', N'عرض الأرشيف', N'Archive', 1751646909, NULL, N'عرض', 1, 'archive', '#607D8B', 1, 1, NULL, NULL),
(78, 'archive.upload', N'رفع المستندات للأرشيف', N'Archive', 1751646909, NULL, N'رفع', 2, 'cloud_upload', '#607D8B', 0, 1, NULL, NULL),
(79, 'archive.download', N'تحميل المستندات من الأرشيف', N'Archive', 1751646909, NULL, N'تحميل', 1, 'cloud_download', '#607D8B', 1, 1, NULL, NULL),
(80, 'archive.delete', N'حذف المستندات من الأرشيف', N'Archive', 1751646909, NULL, N'حذف', 3, 'delete_forever', '#F44336', 0, 1, NULL, NULL),

-- مجموعة Search
(1072, 'search.view', N'عرض شاشة البحث الموحد', N'Search', **********, NULL, N'عرض', 1, 'search', '#2196F3', 1, 1, NULL, NULL),
(1073, 'search.advanced', N'البحث المتقدم', N'Search', **********, NULL, N'بحث متقدم', 2, 'manage_search', '#2196F3', 0, 1, NULL, NULL),

-- مجموعة Files
(1081, 'files.view', N'عرض الملفات', N'Files', **********, NULL, N'عرض', 1, 'folder', '#FF9800', 1, 1, NULL, NULL),
(1082, 'files.upload', N'رفع الملفات', N'Files', **********, NULL, N'رفع', 2, 'file_upload', '#FF9800', 0, 1, NULL, NULL),
(1083, 'files.download', N'تحميل الملفات', N'Files', **********, NULL, N'تحميل', 1, 'file_download', '#FF9800', 1, 1, NULL, NULL),

-- مجموعة Documents
(1088, 'documents.view', N'عرض المستندات النصية', N'Documents', **********, NULL, N'عرض', 1, 'description', '#795548', 1, 1, NULL, NULL),
(1089, 'documents.create', N'إنشاء مستندات نصية', N'Documents', **********, NULL, N'إنشاء', 2, 'note_add', '#795548', 0, 1, NULL, NULL),
(1090, 'documents.edit', N'تحرير المستندات النصية', N'Documents', **********, NULL, N'تحرير', 2, 'edit_note', '#795548', 0, 1, NULL, NULL),

-- مجموعة Notifications
(61, 'notifications.view', N'عرض الإشعارات', N'Notifications', 1750176586, 1751832925, N'عرض', 1, NULL, NULL, 1, 1, NULL, NULL),
(62, 'notifications.manage', N'إدارة الإشعارات', N'Notifications', 1750176586, 1751832925, N'إدارة', 3, NULL, NULL, 1, 1, NULL, NULL),
(85, 'notifications.send', N'إرسال إشعارات للمستخدمين', N'Notifications', 1751646909, NULL, N'إرسال', 3, 'send', '#FF9800', 0, 1, NULL, NULL),

-- مجموعة Calendar
(64, 'calendar.manage', N'إدارة التقويم', N'Calendar', 1750176586, 1751832925, N'إدارة', 2, NULL, NULL, 1, 1, NULL, NULL),
(76, 'calendar.create_events', N'إنشاء أحداث في التقويم', N'Calendar', 1751646909, NULL, N'إنشاء', 2, 'event', '#FF5722', 0, 1, NULL, NULL),

-- مجموعة Profile
(59, 'profile.view', N'عرض الملف الشخصي', N'Profile', 1750176586, 1751832925, N'عرض', 1, NULL, NULL, 1, 1, NULL, NULL),
(60, 'profile.edit', N'تعديل الملف الشخصي', N'Profile', 1750176586, 1751832925, N'تعديل', 1, NULL, NULL, 1, 1, NULL, NULL),

-- مجموعة Departments
(65, 'departments.view', N'عرض الأقسام', N'Departments', 1750176586, 1751832925, N'عرض', 3, NULL, NULL, 1, 1, NULL, NULL),
(66, 'departments.manage', N'إدارة الأقسام', N'Departments', 1750176586, 1751832925, N'إدارة', 4, NULL, NULL, 1, 1, NULL, NULL),

-- مجموعة Admin
(82, 'admin.view', N'الوصول للوحة الإدارة', N'Admin', 1751646909, NULL, N'وصول', 4, 'admin_panel_settings', '#9E9E9E', 0, 1, NULL, NULL),

-- مجموعة PowerBI
(84, 'powerbi.view', N'عرض تقارير Power BI', N'PowerBI', 1751646909, NULL, N'عرض', 2, 'analytics', '#FFD700', 0, 1, NULL, NULL),
(1152, 'powerbi.create', N'إنشاء تقارير Power BI', N'PowerBI', **********, NULL, N'إنشاء', 3, 'add_chart', '#FFD700', 0, 1, NULL, NULL),
(1153, 'powerbi.edit', N'تعديل تقارير Power BI', N'PowerBI', **********, NULL, N'تعديل', 3, 'edit', '#FFD700', 0, 1, NULL, NULL),

-- مجموعة Settings
(1158, 'settings.view', N'عرض الإعدادات', N'Settings', **********, NULL, N'عرض', 2, 'settings', '#9E9E9E', 0, 1, NULL, NULL),
(1159, 'settings.edit', N'تعديل الإعدادات', N'Settings', **********, NULL, N'تعديل', 3, 'tune', '#9E9E9E', 0, 1, NULL, NULL),
(1161, 'settings.user', N'إعدادات المستخدم', N'Settings', **********, NULL, N'مستخدم', 1, 'account_circle', '#9E9E9E', 1, 1, NULL, NULL),

-- مجموعة Comments
(1140, 'comments.view', N'عرض التعليقات', N'Comments', **********, NULL, N'عرض', 1, 'comment', '#FF9800', 1, 1, NULL, NULL),
(1141, 'comments.create', N'إنشاء تعليقات', N'Comments', **********, NULL, N'إنشاء', 1, 'add_comment', '#FF9800', 1, 1, NULL, NULL),
(1142, 'comments.edit', N'تعديل التعليقات', N'Comments', **********, NULL, N'تعديل', 2, 'edit', '#FF9800', 0, 1, NULL, NULL),
(1143, 'comments.delete', N'حذف التعليقات', N'Comments', **********, NULL, N'حذف', 3, 'delete', '#F44336', 0, 1, NULL, NULL),

-- مجموعة Attachments
(1076, 'attachments.view', N'عرض المرفقات', N'Attachments', **********, NULL, N'عرض', 1, 'attachment', '#607D8B', 1, 1, NULL, NULL),
(1077, 'attachments.upload', N'رفع المرفقات', N'Attachments', **********, NULL, N'رفع', 2, 'cloud_upload', '#607D8B', 0, 1, NULL, NULL),
(1078, 'attachments.download', N'تحميل المرفقات', N'Attachments', **********, NULL, N'تحميل', 1, 'cloud_download', '#607D8B', 1, 1, NULL, NULL),
(1079, 'attachments.delete', N'حذف المرفقات', N'Attachments', **********, NULL, N'حذف', 3, 'delete', '#F44336', 0, 1, NULL, NULL),

-- مجموعة Security
(1172, 'security.view_logs', N'عرض سجلات الأمان', N'Security', 1751817282, NULL, N'سجلات', 4, 'security', '#E91E63', 0, 1, NULL, NULL),
(1173, 'security.audit', N'مراجعة الأمان', N'Security', 1751817282, NULL, N'مراجعة', 5, 'fact_check', '#E91E63', 0, 1, NULL, NULL),
(1174, 'security.manage_sessions', N'إدارة الجلسات', N'Security', 1751817282, NULL, N'جلسات', 4, 'login', '#E91E63', 0, 1, NULL, NULL),

-- مجموعة Data
(1166, 'data.export', N'تصدير البيانات', N'Data', 1751817282, NULL, N'تصدير', 3, 'file_download', '#4CAF50', 0, 1, NULL, NULL),
(1167, 'data.import', N'استيراد البيانات', N'Data', 1751817282, NULL, N'استيراد', 4, 'file_upload', '#4CAF50', 0, 1, NULL, NULL),
(1168, 'data.backup', N'نسخ احتياطي للبيانات', N'Data', 1751817282, NULL, N'نسخ احتياطي', 4, 'backup', '#4CAF50', 0, 1, NULL, NULL),

-- مجموعة Print
(1180, 'print.documents', N'طباعة المستندات', N'Print', 1751817282, NULL, N'مستندات', 1, 'print', '#607D8B', 1, 1, NULL, NULL),
(1181, 'print.reports', N'طباعة التقارير', N'Print', 1751817282, NULL, N'تقارير', 2, 'print', '#607D8B', 0, 1, NULL, NULL),
(1182, 'print.tasks', N'طباعة المهام', N'Print', 1751817282, NULL, N'مهام', 1, 'print', '#607D8B', 1, 1, NULL, NULL),

-- مجموعة Share
(1184, 'share.documents', N'مشاركة المستندات', N'Share', 1751817282, NULL, N'مستندات', 2, 'share', '#2196F3', 0, 1, NULL, NULL),
(1185, 'share.reports', N'مشاركة التقارير', N'Share', 1751817282, NULL, N'تقارير', 2, 'share', '#2196F3', 0, 1, NULL, NULL),
(1186, 'share.tasks', N'مشاركة المهام', N'Share', 1751817282, NULL, N'مهام', 2, 'share', '#2196F3', 0, 1, NULL, NULL),

-- مجموعة Statistics
(1189, 'statistics.view', N'عرض الإحصائيات والتحليلات', N'Statistics', 1751817282, NULL, N'عرض', 2, 'analytics', '#2196F3', 0, 1, NULL, NULL),
(1190, 'statistics.advanced', N'الإحصائيات المتقدمة', N'Statistics', 1751817282, NULL, N'متقدم', 3, 'insights', '#2196F3', 0, 1, NULL, NULL),

-- مجموعة Tags
(1191, 'tags.view', N'عرض الوسوم', N'Tags', 1751817282, NULL, N'عرض', 1, 'local_offer', '#9C27B0', 1, 1, NULL, NULL),
(1192, 'tags.manage', N'إدارة الوسوم', N'Tags', 1751817282, NULL, N'إدارة', 2, 'sell', '#9C27B0', 0, 1, NULL, NULL),

-- مجموعة Testing
(1195, 'testing.access', N'الوصول لأدوات الاختبار', N'Testing', 1751817282, NULL, N'وصول', 4, 'bug_report', '#FF5722', 0, 1, NULL, NULL),

-- مجموعة API
(1196, 'api.access', N'الوصول لواجهة برمجة التطبيقات', N'API', 1751817282, NULL, N'وصول', 4, 'api', '#607D8B', 0, 1, NULL, NULL),

-- مجموعة Help
(1197, 'help.view', N'عرض المساعدة والدعم', N'Help', 1751817282, NULL, N'عرض', 1, 'help', '#009688', 1, 1, NULL, NULL),

-- مجموعة Support
(1198, 'support.contact', N'التواصل مع الدعم الفني', N'Support', 1751817282, NULL, N'تواصل', 1, 'support_agent', '#009688', 1, 1, NULL, NULL),

-- صلاحيات إضافية من الملف
(2205, 'tasks.change_status', N'تغيير حالة المهام عبر السحب والإفلات', N'Tasks', 1751899408, 1751832925, N'تحكم', 1, NULL, NULL, 0, 1, NULL, NULL),
(2206, 'tasks.change_priority', N'تغيير أولوية المهام عبر الأزرار والسحب والإفلات', N'Tasks', 1751899408, 1751832925, N'تحكم', 1, NULL, NULL, 0, 1, NULL, NULL),
(2208, 'users.delete', N'حذف المستخدمين من النظام', N'Users', 1751899408, 1751832925, N'حذف', 1, NULL, NULL, 0, 1, NULL, NULL)

PRINT '📊 تم تحميل ' + CAST(@@ROWCOUNT AS NVARCHAR(10)) + ' صلاحية من ملف CSV'

-- بدء المعاملة
BEGIN TRANSACTION

DECLARE @AddedCount INT = 0
DECLARE @SkippedCount INT = 0
DECLARE @CurrentTime BIGINT = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())

PRINT '🔍 تحليل الصلاحيات الموجودة والجديدة...'

-- حساب الصلاحيات المتجاهلة (الموجودة مسبقاً)
SELECT @SkippedCount = COUNT(*)
FROM #CsvPermissions csv
WHERE EXISTS (
    SELECT 1 
    FROM permissions p 
    WHERE p.name = csv.name
)

PRINT '⚠️ سيتم تجاهل ' + CAST(@SkippedCount AS NVARCHAR(10)) + ' صلاحية موجودة مسبقاً'

-- إضافة الصلاحيات الجديدة فقط
INSERT INTO permissions (
    name, 
    description, 
    permission_group, 
    category, 
    level, 
    icon, 
    color, 
    is_default, 
    is_active,
    created_at,
    updated_at,
    screen_id,
    action_id
)
SELECT 
    csv.name,
    csv.description,
    csv.permission_group,
    csv.category,
    ISNULL(csv.level, 1),
    csv.icon,
    csv.color,
    ISNULL(csv.is_default, 0),
    ISNULL(csv.is_active, 1),
    @CurrentTime,
    NULL,
    csv.screen_id,
    csv.action_id
FROM #CsvPermissions csv
WHERE NOT EXISTS (
    SELECT 1 
    FROM permissions p 
    WHERE p.name = csv.name
)

SET @AddedCount = @@ROWCOUNT

-- عرض النتائج النهائية
PRINT '=================================================='
PRINT '✅ تم إضافة ' + CAST(@AddedCount AS NVARCHAR(10)) + ' صلاحية جديدة بنجاح'
PRINT '⚠️ تم تجاهل ' + CAST(@SkippedCount AS NVARCHAR(10)) + ' صلاحية موجودة مسبقاً'
PRINT '📈 إجمالي الصلاحيات في النظام: ' + CAST((SELECT COUNT(*) FROM permissions) AS NVARCHAR(10))

-- تأكيد المعاملة
COMMIT TRANSACTION

-- تنظيف الجدول المؤقت
DROP TABLE #CsvPermissions

PRINT '🎉 تمت العملية بنجاح!'
PRINT '=================================================='

-- عرض عينة من الصلاحيات المضافة حديثاً (إن وجدت)
IF @AddedCount > 0
BEGIN
    PRINT '📋 عينة من الصلاحيات المضافة:'
    SELECT TOP 10
        id,
        name,
        description,
        permission_group,
        category,
        level,
        is_default,
        is_active
    FROM permissions 
    WHERE created_at = @CurrentTime
    ORDER BY id DESC
END

-- عرض إحصائيات حسب المجموعات
PRINT '📊 إحصائيات الصلاحيات حسب المجموعات:'
SELECT 
    permission_group,
    COUNT(*) as total_permissions,
    SUM(CASE WHEN is_default = 1 THEN 1 ELSE 0 END) as default_permissions,
    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_permissions
FROM permissions 
GROUP BY permission_group
ORDER BY total_permissions DESC

SET NOCOUNT OFF;
GO
