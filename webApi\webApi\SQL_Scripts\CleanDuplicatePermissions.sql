-- سكريبت تنظيف التكرارات في جدول الصلاحيات
-- يجب تشغيل هذا السكريبت قبل إضافة أي صلاحيات جديدة

USE [databasetasks]
GO

PRINT '🧹 بدء تنظيف التكرارات في جدول الصلاحيات...'

-- التحقق من وجود الجدول
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'permissions')
BEGIN
    PRINT '❌ جدول permissions غير موجود.'
    RETURN
END

-- عرض إحصائيات قبل التنظيف
PRINT ''
PRINT '📊 إحصائيات قبل التنظيف:'
SELECT 
    COUNT(*) as [إجمالي الصلاحيات],
    COUNT(DISTINCT name) as [الصلاحيات الفريدة],
    COUNT(*) - COUNT(DISTINCT name) as [عدد التكرارات]
FROM permissions

-- إنشاء نسخة احتياطية
PRINT ''
PRINT '💾 إنشاء نسخة احتياطية...'
SELECT * INTO permissions_backup_before_cleanup FROM permissions
PRINT '✅ تم إنشاء نسخة احتياطية: permissions_backup_before_cleanup'

-- عرض التكرارات المكتشفة
PRINT ''
PRINT '🔍 التكرارات المكتشفة:'
SELECT 
    name as [اسم الصلاحية],
    COUNT(*) as [عدد التكرارات],
    STRING_AGG(CAST(id AS NVARCHAR(10)), ', ') as [معرفات التكرارات]
FROM permissions 
GROUP BY name 
HAVING COUNT(*) > 1
ORDER BY COUNT(*) DESC, name

-- حذف التكرارات مع الاحتفاظ بأحدث نسخة (أعلى ID)
PRINT ''
PRINT '🗑️ حذف التكرارات...';

-- استخدام CTE لتحديد التكرارات
WITH DuplicatePermissions AS (
    SELECT
        id,
        name,
        ROW_NUMBER() OVER (PARTITION BY name ORDER BY id DESC) as rn
    FROM permissions
)
DELETE FROM permissions
WHERE id IN (
    SELECT id
    FROM DuplicatePermissions
    WHERE rn > 1
);

DECLARE @DeletedCount INT = @@ROWCOUNT
PRINT '✅ تم حذف ' + CAST(@DeletedCount AS NVARCHAR(10)) + ' صلاحية مكررة'

-- تنظيف البيانات الغريبة
PRINT ''
PRINT '🧽 تنظيف البيانات الغريبة...'

-- حذف الصلاحيات ذات الأوصاف الغريبة
DELETE FROM permissions
WHERE description = 'q'
   OR description IS NULL
   OR description = ''
   OR permission_group = 'CustomRoles';

DECLARE @CleanedCount INT = @@ROWCOUNT
IF @CleanedCount > 0
BEGIN
    PRINT '✅ تم حذف ' + CAST(@CleanedCount AS NVARCHAR(10)) + ' صلاحية ذات بيانات غريبة'
END
ELSE
BEGIN
    PRINT '✅ لا توجد بيانات غريبة للحذف'
END

-- إصلاح مشاكل الترميز (إذا كانت موجودة)
PRINT ''
PRINT '🔤 فحص مشاكل الترميز...'

DECLARE @EncodingIssues INT
SELECT @EncodingIssues = COUNT(*) 
FROM permissions 
WHERE description LIKE '%Ø%' OR description LIKE '%Ù%'

IF @EncodingIssues > 0
BEGIN
    PRINT '⚠️ تم اكتشاف ' + CAST(@EncodingIssues AS NVARCHAR(10)) + ' صلاحية بمشاكل ترميز'
    PRINT '💡 يُنصح بإصلاح الترميز يدوياً أو إعادة إدراج البيانات بترميز UTF-8'
END
ELSE
BEGIN
    PRINT '✅ لا توجد مشاكل ترميز واضحة'
END

-- تحديث الفهارس
PRINT ''
PRINT '🔧 تحديث الفهارس...'

-- إنشاء فهرس فريد لمنع التكرار المستقبلي
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_permissions_name_unique')
BEGIN
    CREATE UNIQUE INDEX IX_permissions_name_unique ON permissions (name)
    PRINT '✅ تم إنشاء فهرس فريد لمنع التكرار المستقبلي'
END
ELSE
BEGIN
    PRINT '✅ الفهرس الفريد موجود بالفعل'
END

-- عرض إحصائيات بعد التنظيف
PRINT ''
PRINT '📊 إحصائيات بعد التنظيف:'
SELECT 
    COUNT(*) as [إجمالي الصلاحيات],
    COUNT(DISTINCT name) as [الصلاحيات الفريدة],
    COUNT(*) - COUNT(DISTINCT name) as [عدد التكرارات المتبقية],
    COUNT(DISTINCT permission_group) as [عدد المجموعات]
FROM permissions

-- عرض توزيع الصلاحيات حسب المجموعات
PRINT ''
PRINT '📋 توزيع الصلاحيات حسب المجموعات:'
SELECT 
    permission_group as [المجموعة],
    COUNT(*) as [عدد الصلاحيات],
    AVG(CAST(level as FLOAT)) as [متوسط المستوى]
FROM permissions 
GROUP BY permission_group 
ORDER BY COUNT(*) DESC

-- فحص التكرارات المتبقية (يجب أن تكون صفر)
PRINT ''
PRINT '🔍 فحص التكرارات المتبقية:'
DECLARE @RemainingDuplicates INT
SELECT @RemainingDuplicates = COUNT(*) 
FROM (
    SELECT name 
    FROM permissions 
    GROUP BY name 
    HAVING COUNT(*) > 1
) as duplicates

IF @RemainingDuplicates = 0
BEGIN
    PRINT '✅ ممتاز! لا توجد تكرارات متبقية'
END
ELSE
BEGIN
    PRINT '⚠️ تحذير: لا تزال هناك ' + CAST(@RemainingDuplicates AS NVARCHAR(10)) + ' صلاحية مكررة'
    
    -- عرض التكرارات المتبقية
    SELECT 
        name as [الصلاحية المكررة],
        COUNT(*) as [عدد التكرارات]
    FROM permissions 
    GROUP BY name 
    HAVING COUNT(*) > 1
END

-- إنشاء تقرير نهائي
PRINT ''
PRINT '📄 تقرير التنظيف النهائي:'
PRINT '================================'
PRINT '• الصلاحيات المحذوفة: ' + CAST(@DeletedCount AS NVARCHAR(10))
PRINT '• البيانات الغريبة المحذوفة: ' + CAST(@CleanedCount AS NVARCHAR(10))
PRINT '• مشاكل الترميز: ' + CAST(@EncodingIssues AS NVARCHAR(10))
PRINT '• التكرارات المتبقية: ' + CAST(@RemainingDuplicates AS NVARCHAR(10))

-- نصائح للمستقبل
PRINT ''
PRINT '💡 نصائح لتجنب التكرار في المستقبل:'
PRINT '1. استخدم دائماً IF NOT EXISTS قبل INSERT'
PRINT '2. تحقق من الفهرس الفريد على عمود name'
PRINT '3. استخدم MERGE بدلاً من INSERT للتحديثات'
PRINT '4. قم بفحص دوري للتكرارات'

PRINT ''
PRINT '🎉 تم الانتهاء من تنظيف جدول الصلاحيات بنجاح!'
PRINT 'يمكنك الآن إضافة صلاحيات جديدة بأمان.'

GO
