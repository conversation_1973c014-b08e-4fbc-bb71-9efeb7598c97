import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:get/get.dart';

import '../../../models/chart_enums.dart';
import '../../../models/advanced_filter_options.dart' as filter_options;
import '../../../utils/chart_fullscreen_helper.dart';
import 'unified_filter_export_widget.dart';

/// كلاس لتمثيل نقطة بيانات في المخطط الشريطي
class BarChartDataPoint {
  final String category;
  final double value;
  
  BarChartDataPoint(this.category, this.value);
}

/// مكون الرسم البياني الشريطي المحسن باستخدام Syncfusion Charts
class EnhancedBarChart extends StatefulWidget {
  /// بيانات المخطط
  final Map<String, double> data;

  /// ألوان الأشرطة (اختياري)
  final Map<String, Color>? barColors;

  /// عنوان المخطط
  final String? title;

  /// عنوان المحور السيني
  final String? xAxisTitle;

  /// عنوان المحور الصادي
  final String? yAxisTitle;

  /// إظهار الشبكة
  final bool showGrid;

  /// الحد الأقصى للمحور الصادي
  final double? maxY;

  /// عرض الأشرطة
  final double barWidth;

  /// معالج تغيير الفلتر
  final Function(DateTime?, DateTime?, TimeFilterType)? onFilterChanged;

  /// معالج التصدير
  final Function(String)? onExport;

  /// إظهار خيارات الفلتر
  final bool showFilterOptions;

  /// إظهار خيارات التصدير
  final bool showExportOptions;

  /// صيغ التصدير المدعومة
  final List<String> supportedExportFormats;

  /// نوع المخطط
  final ChartType chartType;

  /// خيارات الفلتر المتقدمة
  final filter_options.AdvancedFilterOptions advancedFilterOptions;

  /// إظهار القيم على المخطط
  final bool showValues;

  const EnhancedBarChart({
    super.key,
    required this.data,
    this.barColors,
    this.title,
    this.xAxisTitle,
    this.yAxisTitle,
    this.showGrid = true,
    this.maxY,
    this.barWidth = 0.8,
    this.onFilterChanged,
    this.onExport,
    this.showFilterOptions = false,
    this.showExportOptions = false,
    this.supportedExportFormats = const ['pdf', 'excel', 'csv'],
    required this.chartType,
    required this.advancedFilterOptions,
    this.showValues = true,
  });

  @override
  State<EnhancedBarChart> createState() => _EnhancedBarChartState();
}

class _EnhancedBarChartState extends State<EnhancedBarChart> {
  DateTime? _startDate;
  DateTime? _endDate;
  TimeFilterType _filterType = TimeFilterType.month;

  @override
  void initState() {
    super.initState();
  }

  void _handleFilterChanged(DateTime? startDate, DateTime? endDate, TimeFilterType filterType) {
    setState(() {
      _startDate = startDate;
      _endDate = endDate;
      _filterType = filterType;
    });
    
    if (widget.onFilterChanged != null) {
      widget.onFilterChanged!(startDate, endDate, filterType);
    }
  }

  void _handleExport(String format) {
    if (widget.onExport != null) {
      widget.onExport!(format);
    }
  }

  void _handleChartTypeChanged(ChartType newType) {
    // يمكن إضافة منطق تغيير نوع المخطط هنا
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // مكون التصفية والتصدير
        if (widget.showFilterOptions || widget.showExportOptions)
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: UnifiedFilterExportWidget(
              title: widget.title ?? 'رسم بياني شريطي',
              chartKey: 'bar_chart_syncfusion',
              chartType: widget.chartType,
              onFilterChanged: (startDate, endDate, filterType, chartKey) {
                _handleFilterChanged(startDate, endDate, filterType);
              },
              onExport: (format, chartKey) {
                _handleExport(format);
              },
              onChartTypeChanged: (chartType, chartKey) {
                _handleChartTypeChanged(chartType);
              },
              showFilter: widget.showFilterOptions,
              showExport: widget.showExportOptions,
              showChartTypeSelector: true,
              showFullscreenButton: true,
              onFullscreenPressed: () {
                ChartFullscreenHelper.showFullscreenChart(
                  context: context,
                  title: widget.title ?? 'رسم بياني شريطي',
                  chartKey: 'bar_chart_syncfusion',
                  chartContent: _buildChart(),
                );
              },
              supportedChartTypes: const [
                ChartType.bar,
                ChartType.line,
                ChartType.pie,
              ],
              currentChartType: widget.chartType,
              advancedFilterOptions: widget.advancedFilterOptions,
              startDate: _startDate,
              endDate: _endDate,
              filterType: _filterType,
            ),
          ),

        // العنوان
        if (!widget.showFilterOptions && 
            !widget.showExportOptions &&
            widget.title != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: Text(
              widget.title!,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

        // المخطط
        Expanded(
          child: widget.data.isEmpty
              ? UnifiedFilterExportWidget.buildNoDataMessage(
                  context,
                  message: 'لا توجد بيانات صالحة للعرض.\nقد يكون ذلك بسبب الفلتر المطبق.',
                  onCancelFilter: () {
                    if (widget.onFilterChanged != null) {
                      widget.onFilterChanged!(null, null, TimeFilterType.all);
                    }
                  },
                )
              : _buildChart(),
        ),
      ],
    );
  }

  /// الحصول على القيمة القصوى للبيانات
  double _getMaxValue() {
    if (widget.data.isEmpty) return 10.0;
    
    double maxValue = 0.0;
    for (final value in widget.data.values) {
      if (value.isFinite && !value.isNaN && value > maxValue) {
        maxValue = value;
      }
    }
    
    // ضمان قيمة دنيا معقولة
    return maxValue > 0 ? maxValue : 10.0;
  }

  /// حساب فاصل آمن للشبكة
  double _getSafeInterval() {
    final maxValue = _getMaxValue();
    if (maxValue <= 0 || !maxValue.isFinite || maxValue.isNaN) {
      return 1.0; // قيمة افتراضية آمنة
    }
    
    // حساب فاصل معقول (حوالي 5 خطوط شبكة)
    return (maxValue / 5).ceil().toDouble();
  }

  Widget _buildChart() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.shade400, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      width: double.infinity,
      padding: const EdgeInsets.all(8),
      child: SfCartesianChart(
        // تفعيل الميزات التفاعلية المتقدمة من Syncfusion
        enableAxisAnimation: true,
        enableSideBySideSeriesPlacement: true,
        
        // تفعيل Tooltip المتقدم
        tooltipBehavior: TooltipBehavior(
          enable: true,
          format: 'point.x: point.y',
          header: '',
          canShowMarker: false,
          activationMode: ActivationMode.singleTap,
          animationDuration: 500,
          borderColor: Colors.blue,
          borderWidth: 2,
        ),
        
        // تفعيل Trackball المتقدم
        trackballBehavior: TrackballBehavior(
          enable: true,
          activationMode: ActivationMode.singleTap,
          lineType: TrackballLineType.vertical,
          tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
          markerSettings: const TrackballMarkerSettings(
            markerVisibility: TrackballVisibilityMode.visible,
            height: 8,
            width: 8,
            borderWidth: 2,
          ),
          tooltipSettings: const InteractiveTooltip(
            enable: true,
            color: Colors.black87,
            textStyle: TextStyle(color: Colors.white),
          ),
        ),
        
        // تفعيل Crosshair
        crosshairBehavior: CrosshairBehavior(
          enable: true,
          activationMode: ActivationMode.longPress,
          lineType: CrosshairLineType.both,
          lineColor: Colors.blue.withValues(alpha: 0.7),
          lineWidth: 1,
        ),
        
        // تفعيل Zoom والPan
        zoomPanBehavior: ZoomPanBehavior(
          enablePinching: true,
          enablePanning: true,
          enableDoubleTapZooming: true,
          enableMouseWheelZooming: true,
          zoomMode: ZoomMode.xy,
          maximumZoomLevel: 0.01,
        ),
        
        // إعدادات المحاور
        primaryXAxis: CategoryAxis(
          title: AxisTitle(text: widget.xAxisTitle ?? ''),
          majorGridLines: MajorGridLines(
            width: widget.showGrid ? 1 : 0,
            color: Get.isDarkMode ? Colors.white10 : Colors.black12,
          ),
          axisLine: AxisLine(
            color: Get.isDarkMode ? Colors.white70 : Colors.black54,
            width: 1,
          ),
          labelStyle: TextStyle(
            color: Get.isDarkMode ? Colors.white : Colors.black,
            fontSize: 10,
          ),
        ),
        
        primaryYAxis: NumericAxis(
          title: AxisTitle(text: widget.yAxisTitle ?? ''),
          majorGridLines: MajorGridLines(
            width: widget.showGrid ? 1 : 0,
            color: Get.isDarkMode ? Colors.white10 : Colors.black12,
          ),
          axisLine: AxisLine(
            color: Get.isDarkMode ? Colors.white70 : Colors.black54,
            width: 1,
          ),
          labelStyle: TextStyle(
            color: Get.isDarkMode ? Colors.white : Colors.black,
            fontSize: 12,
          ),
          maximum: widget.maxY,
          interval: _getSafeInterval(), // استخدام فاصل آمن
        ),
        
        // إعدادات السلاسل
        series: _buildSyncfusionSeries(),
      ),
    );
  }

  /// بناء سلاسل Syncfusion Charts
  List<CartesianSeries> _buildSyncfusionSeries() {
    final List<Color> defaultColors = [
      Colors.blue, Colors.green, Colors.red, Colors.purple,
      Colors.orange, Colors.teal, Colors.indigo, Colors.amber,
      Colors.cyan, Colors.pink,
    ];

    // تحويل البيانات إلى تنسيق Syncfusion
    final List<BarChartDataPoint> chartData = [];
    for (final entry in widget.data.entries) {
      chartData.add(BarChartDataPoint(entry.key, entry.value));
    }

    return [
      ColumnSeries<BarChartDataPoint, String>(
        dataSource: chartData,
        xValueMapper: (BarChartDataPoint data, _) => data.category,
        yValueMapper: (BarChartDataPoint data, _) => data.value,
        
        // تخصيص الألوان
        pointColorMapper: (BarChartDataPoint data, int index) {
          return widget.barColors?[data.category] ?? 
                 defaultColors[index % defaultColors.length];
        },
        
        // إعدادات العرض
        width: widget.barWidth,
        spacing: 0.1,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(4),
          topRight: Radius.circular(4),
        ),
        
        // إعدادات التسميات
        dataLabelSettings: DataLabelSettings(
          isVisible: widget.showValues,
          labelPosition: ChartDataLabelPosition.outside,
          textStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        
        // إعدادات التحديد
        selectionBehavior: SelectionBehavior(
          enable: true,
          selectedColor: Colors.grey.shade300,
          unselectedColor: Colors.grey.shade600,
          selectedBorderColor: Colors.black,
          selectedBorderWidth: 2,
        ),
        
        // إعدادات التفاعل
        enableTooltip: true,
        animationDuration: 1000,
        
        // معالج النقر
        onPointTap: (ChartPointDetails details) {
          // يمكن إضافة منطق معالجة النقر هنا إذا لزم الأمر
          debugPrint('تم النقر على النقطة: ${details.pointIndex}');
        },
      ),
    ];
  }
}
