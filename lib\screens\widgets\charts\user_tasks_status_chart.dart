import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/advanced_filter_options.dart';
import 'package:flutter_application_2/models/chart_enums.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../controllers/task_controller.dart';
import '../../../controllers/user_controller.dart';
import '../../../models/task_status_enum.dart';
import '../../../models/user_model.dart';
import '../../../constants/app_colors.dart';

/// كلاس لتمثيل بيانات المخطط
class _UserTaskData {
  final String userName;
  final String userId;
  final TaskStatus status;
  final int count;
  final Color color;

  _UserTaskData({
    required this.userName,
    required this.userId,
    required this.status,
    required this.count,
    required this.color,
  });
}

/// مخطط يعرض عدد مهام كل شخص حسب حالاتها الأربع
class UserTasksStatusChart extends StatefulWidget {
  final String title;
  final bool showFilterOptions;
  final bool showExportOptions;
  final Function(DateTime?, DateTime?, TimeFilterType)? onFilterChanged;
  final Function(String)? onExport;
  final ChartType chartType;
  final AdvancedFilterOptions advancedFilterOptions;

  const UserTasksStatusChart({
    super.key,
    this.title = 'توزيع المهام حسب المستخدمين والحالة',
    this.showFilterOptions = true,
    this.showExportOptions = true,
    this.onFilterChanged,
    this.onExport,
    required this.chartType,
    required this.advancedFilterOptions,
  });

  @override
  State<UserTasksStatusChart> createState() => _UserTasksStatusChartState();
}

class _UserTasksStatusChartState extends State<UserTasksStatusChart> {
  final TaskController _taskController = Get.find<TaskController>();
  final UserController _userController = Get.find<UserController>();

  // حالات المهام الأربع الرئيسية
  final List<TaskStatus> _mainStatuses = [
    TaskStatus.pending,
    TaskStatus.inProgress,
    TaskStatus.waitingForInfo,
    TaskStatus.completed,
  ];

  // ألوان الحالات
  final Map<TaskStatus, Color> _statusColors = {
    TaskStatus.pending: AppColors.statusPending,
    TaskStatus.inProgress: AppColors.statusInProgress,
    TaskStatus.waitingForInfo: AppColors.statusWaitingForInfo,
    TaskStatus.completed: AppColors.statusCompleted,
  };

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // التحقق من وجود بيانات
      if (_taskController.isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_taskController.myTasks.isEmpty || _userController.users.isEmpty) {
        return const Center(
          child: Text(
            'لا توجد بيانات متاحة',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        );
      }

      // تحضير البيانات
      final data = _prepareChartData();

      return Column(
        children: [
          if (widget.title.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Text(
                widget.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          Expanded(
            child: _buildChart(data),
          ),
        ],
      );
    });
  }

  /// تحضير بيانات المخطط
  Map<String, Map<TaskStatus, int>> _prepareChartData() {
    final Map<String, Map<TaskStatus, int>> result = {};
    final tasks = _taskController.allTasks;
    final users = _userController.users;

    // تهيئة البيانات لكل مستخدم
    for (final user in users) {
      result[user.id.toString()] = {};
      for (final status in _mainStatuses) {
        result[user.id.toString()]![status] = 0;
      }
    }

    // حساب عدد المهام لكل مستخدم حسب الحالة
    for (final task in tasks) {
      final assigneeIdStr = task.assigneeId?.toString();
      if (assigneeIdStr != null && result.containsKey(assigneeIdStr)) {
        // التأكد من أن الحالة هي واحدة من الحالات الرئيسية
        final taskStatus = TaskStatus.fromString(task.status);
        if (_mainStatuses.contains(taskStatus)) {
          result[assigneeIdStr]![taskStatus] =
              (result[assigneeIdStr]![taskStatus] ?? 0) + 1;
        }
      }
    }

    return result;
  }

  /// بناء المخطط
  Widget _buildChart(Map<String, Map<TaskStatus, int>> data) {
    // ترتيب المستخدمين حسب إجمالي عدد المهام (تنازلياً)
    final sortedUserIds = data.keys.toList()
      ..sort((a, b) {
        final totalA = _calculateTotalTasks(data[a]!);
        final totalB = _calculateTotalTasks(data[b]!);
        return totalB.compareTo(totalA);
      });

    // أخذ أعلى 10 مستخدمين فقط لتجنب ازدحام المخطط
    final topUserIds = sortedUserIds.take(10).toList();

    // تحضير البيانات للمخطط
    final chartData = _prepareChartDataForSyncfusion(data, topUserIds);

    return LayoutBuilder(
      builder: (context, constraints) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: SfCartesianChart(
            // تفعيل الميزات التفاعلية
            enableAxisAnimation: true,
            enableSideBySideSeriesPlacement: true,

            // تفعيل Tooltip المتقدم
            tooltipBehavior: TooltipBehavior(
              enable: true,
              format: 'point.x: point.y مهمة',
              header: '',
              canShowMarker: false,
              activationMode: ActivationMode.singleTap,
              animationDuration: 500,
              borderColor: Colors.blue,
              borderWidth: 2,
            ),

            // إعداد المحاور
            primaryXAxis: CategoryAxis(
              title: AxisTitle(text: 'المستخدمين'),
              majorGridLines: const MajorGridLines(width: 0),
              labelStyle: const TextStyle(fontSize: 12),
              labelRotation: -45,
            ),

            primaryYAxis: NumericAxis(
              title: AxisTitle(text: 'عدد المهام'),
              majorGridLines: MajorGridLines(
                width: 1,
                color: Get.isDarkMode ? Colors.white10 : Colors.black12,
              ),
              labelStyle: const TextStyle(fontSize: 12),
            ),

            // إعداد المفتاح
            legend: Legend(
              isVisible: true,
              position: LegendPosition.bottom,
              overflowMode: LegendItemOverflowMode.wrap,
            ),

            // السلاسل
            series: _createColumnSeries(chartData, topUserIds),
          ),
        );
      },
    );
  }

  /// تحضير البيانات للمخطط Syncfusion
  List<_UserTaskData> _prepareChartDataForSyncfusion(
      Map<String, Map<TaskStatus, int>> data, List<String> userIds) {
    final List<_UserTaskData> chartData = [];

    for (final userId in userIds) {
      final user = _userController.users.firstWhere(
        (u) => u.id.toString() == userId,
        orElse: () => User(
          id: int.tryParse(userId) ?? 0,
          name: 'مستخدم غير معروف',
          email: '',
          password: '',
          role: null,
          createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        ),
      );

      final userTasks = data[userId]!;
      for (final status in _mainStatuses) {
        final count = userTasks[status] ?? 0;
        if (count > 0) {
          chartData.add(_UserTaskData(
            userName: _shortenName(user.name),
            userId: userId,
            status: status,
            count: count,
            color: _statusColors[status]!,
          ));
        }
      }
    }

    return chartData;
  }

  /// إنشاء سلاسل الأعمدة للمخطط
  List<CartesianSeries> _createColumnSeries(
      List<_UserTaskData> chartData, List<String> userIds) {
    final List<CartesianSeries> series = [];

    for (final status in _mainStatuses) {
      final statusData = chartData
          .where((data) => data.status == status)
          .toList();

      if (statusData.isNotEmpty) {
        series.add(
          ColumnSeries<_UserTaskData, String>(
            dataSource: statusData,
            xValueMapper: (_UserTaskData data, _) => data.userName,
            yValueMapper: (_UserTaskData data, _) => data.count.toDouble(),
            name: _getStatusName(status),
            color: _statusColors[status],
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(4),
              topRight: Radius.circular(4),
            ),
            dataLabelSettings: const DataLabelSettings(
              isVisible: true,
              labelPosition: ChartDataLabelPosition.outside,
              textStyle: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
            enableTooltip: true,
            animationDuration: 1000,
          ),
        );
      }
    }

    return series;
  }

  /// حساب إجمالي عدد المهام لمستخدم
  int _calculateTotalTasks(Map<TaskStatus, int> userTasks) {
    int total = 0;
    for (final count in userTasks.values) {
      total += count;
    }
    return total;
  }



  /// الحصول على اسم الحالة بالعربية
  String _getStatusName(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return 'قيد الانتظار';
      case TaskStatus.inProgress:
        return 'قيد التنفيذ';
      case TaskStatus.waitingForInfo:
        return 'بانتظار معلومات';
      case TaskStatus.completed:
        return 'مكتملة';
      default:
        return 'غير معروفة';
    }
  }

  /// اختصار اسم المستخدم للعرض
  String _shortenName(String name) {
    if (name.length <= 10) return name;
    return '${name.substring(0, 8)}...';
  }
}
