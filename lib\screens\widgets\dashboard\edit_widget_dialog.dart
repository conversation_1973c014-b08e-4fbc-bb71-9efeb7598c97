import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/dashboard_models.dart';
import 'package:get/get.dart';
import 'dart:convert';



/// حوار تعديل عنصر لوحة المعلومات بتصميم Monday.com
///
/// يعرض حوار لتعديل خصائص عنصر لوحة المعلومات بتصميم مستوحى من Monday.com
class EditWidgetDialog extends StatefulWidget {
  /// العنصر المراد تعديله
  final DashboardWidget widget;

  const EditWidgetDialog({
    super.key,
    required this.widget,
  });

  @override
  State<EditWidgetDialog> createState() => _EditWidgetDialogState();
}

class _EditWidgetDialogState extends State<EditWidgetDialog> {
  // مفتاح النموذج
  final _formKey = GlobalKey<FormState>();

  // وحدات التحكم بالحقول
  late final TextEditingController _titleController;

  // إعدادات العنصر
  late Map<String, dynamic> _settings;

  // حالة التحميل
  final bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // تهيئة وحدات التحكم بالحقول
    _titleController = TextEditingController(text: widget.widget.title);

    // تحميل الإعدادات
    _loadSettings();
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  /// تحميل إعدادات العنصر
  void _loadSettings() {
    try {
      // settings is already a Map<String, dynamic> in dashboard_model.dart
      _settings = Map<String, dynamic>.from(widget.widget.config != null
          ? jsonDecode(widget.widget.config!)
          : {});
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
      _settings = {};
    }
  }

  /// حفظ العنصر
  void _saveWidget() {
    if (_formKey.currentState!.validate()) {
      // إنشاء نسخة معدلة من العنصر
      final updatedWidget = widget.widget.copyWith(
        title: _titleController.text,
        config: jsonEncode(_settings),
      );

      // إرجاع العنصر المعدل
      Get.back(result: updatedWidget);
    }
  }

  /// بناء حقول الإعدادات حسب نوع العنصر
  Widget _buildSettingsFields() {
    switch (widget.widget.type) {
      case 'taskStatusChart':
        return _buildPieChartSettings();
      case 'taskProgressChart':
        return _buildLineChartSettings();
      case 'userPerformanceChart':
      case 'departmentPerformanceChart':
        return _buildBarChartSettings();
      case 'taskCounters':
        return _buildTaskCountersSettings();
      default:
        return const SizedBox.shrink();
    }
  }

  /// بناء إعدادات مخطط الدائرة
  Widget _buildPieChartSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('إعدادات مخطط الدائرة', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),

        // عرض القيم
        _buildSwitchTile(
          title: 'عرض القيم',
          value: _settings['showValues'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showValues'] = value;
            });
          },
        ),

        // عرض النسب المئوية
        _buildSwitchTile(
          title: 'عرض النسب المئوية',
          value: _settings['showPercentages'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showPercentages'] = value;
            });
          },
        ),

        // عرض المفتاح
        _buildSwitchTile(
          title: 'عرض المفتاح',
          value: _settings['showLegend'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showLegend'] = value;
            });
          },
        ),
      ],
    );
  }

  /// بناء حقل تبديل بتصميم Monday.com
  Widget _buildSwitchTile({
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: SwitchListTile(
        title: Text(title),
        value: value,
        onChanged: onChanged,
        activeColor: Colors.blue,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// بناء إعدادات مخطط الخط
  Widget _buildLineChartSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('إعدادات مخطط الخط', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),

        // عرض الشبكة
        _buildSwitchTile(
          title: 'عرض الشبكة',
          value: _settings['showGrid'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showGrid'] = value;
            });
          },
        ),

        // عرض النقاط
        _buildSwitchTile(
          title: 'عرض النقاط',
          value: _settings['showDots'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showDots'] = value;
            });
          },
        ),

        // عرض المنطقة أسفل الخط
        _buildSwitchTile(
          title: 'عرض المنطقة أسفل الخط',
          value: _settings['showBelowArea'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showBelowArea'] = value;
            });
          },
        ),

        // النطاق الزمني
        _buildDropdownField(
          title: 'النطاق الزمني',
          value: _settings['timeRange'] ?? 'month',
          items: const [
            DropdownMenuItem(value: 'week', child: Text('أسبوع')),
            DropdownMenuItem(value: 'month', child: Text('شهر')),
            DropdownMenuItem(value: 'quarter', child: Text('ربع سنة')),
            DropdownMenuItem(value: 'year', child: Text('سنة')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _settings['timeRange'] = value;
              });
            }
          },
        ),
      ],
    );
  }

  /// بناء حقل قائمة منسدلة بتصميم Monday.com
  Widget _buildDropdownField<T>({
    required String title,
    required T value,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: const TextStyle(fontSize: 14)),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButtonFormField<T>(
              value: value,
              items: items,
              onChanged: onChanged,
              decoration: const InputDecoration(
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                border: InputBorder.none,
              ),
              isExpanded: true,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء إعدادات مخطط الأعمدة
  Widget _buildBarChartSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('إعدادات مخطط الأعمدة', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),

        // عرض الشبكة
        _buildSwitchTile(
          title: 'عرض الشبكة',
          value: _settings['showGrid'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showGrid'] = value;
            });
          },
        ),

        // عرض القيم
        _buildSwitchTile(
          title: 'عرض القيم',
          value: _settings['showValues'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showValues'] = value;
            });
          },
        ),

        // الترتيب حسب
        _buildDropdownField(
          title: 'الترتيب حسب',
          value: _settings['sortBy'] ?? 'completed',
          items: const [
            DropdownMenuItem(value: 'completed', child: Text('المكتملة')),
            DropdownMenuItem(value: 'inProgress', child: Text('قيد التنفيذ')),
            DropdownMenuItem(value: 'delayed', child: Text('المتأخرة')),
            DropdownMenuItem(value: 'total', child: Text('الإجمالي')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _settings['sortBy'] = value;
              });
            }
          },
        ),

        // الحد الأقصى للمستخدمين (فقط لمخطط أداء المستخدمين)
        if (widget.widget.type == 'userPerformanceChart')
          _buildNumberField(
            title: 'الحد الأقصى للمستخدمين',
            value: _settings['maxUsers'] ?? 5,
            onChanged: (value) {
              setState(() {
                _settings['maxUsers'] = value;
              });
            },
          ),
      ],
    );
  }

  /// بناء حقل رقمي بتصميم Monday.com
  Widget _buildNumberField({
    required String title,
    required int value,
    required ValueChanged<int> onChanged,
  }) {
    final controller = TextEditingController(text: value.toString());

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: const TextStyle(fontSize: 14)),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextFormField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                border: InputBorder.none,
              ),
              onChanged: (value) {
                final intValue = int.tryParse(value);
                if (intValue != null) {
                  onChanged(intValue);
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء إعدادات عدادات المهام
  Widget _buildTaskCountersSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('إعدادات عدادات المهام', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
        const SizedBox(height: 16),

        // عرض الإجمالي
        _buildSwitchTile(
          title: 'عرض إجمالي المهام',
          value: _settings['showTotal'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showTotal'] = value;
            });
          },
        ),

        // عرض المكتملة
        _buildSwitchTile(
          title: 'عرض المهام المكتملة',
          value: _settings['showCompleted'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showCompleted'] = value;
            });
          },
        ),

        // عرض قيد التنفيذ
        _buildSwitchTile(
          title: 'عرض المهام قيد التنفيذ',
          value: _settings['showInProgress'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showInProgress'] = value;
            });
          },
        ),

        // عرض المتأخرة
        _buildSwitchTile(
          title: 'عرض المهام المتأخرة',
          value: _settings['showDelayed'] ?? true,
          onChanged: (value) {
            setState(() {
              _settings['showDelayed'] = value;
            });
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 600),
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان الحوار
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'تعديل عنصر',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Get.back(),
                    tooltip: 'إغلاق',
                  ),
                ],
              ),
              const Divider(),
              const SizedBox(height: 16),

              // نموذج تعديل العنصر
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان العنصر
                      Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('عنوان العنصر', style: TextStyle(fontSize: 14)),
                            const SizedBox(height: 8),
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: TextFormField(
                                controller: _titleController,
                                decoration: const InputDecoration(
                                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                  border: InputBorder.none,
                                  hintText: 'أدخل عنوان العنصر',
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'الرجاء إدخال عنوان العنصر';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ],
                        ),
                      ),

                      // إعدادات العنصر
                      _buildSettingsFields(),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _saveWidget,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text('حفظ التغييرات'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
