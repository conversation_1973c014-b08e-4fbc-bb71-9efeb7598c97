import 'package:flutter/material.dart';
import 'dart:math' as math;

import '../../../constants/app_styles.dart';


/// نموذج عقدة الشبكة
class NetworkNode {
  /// معرف العقدة
  final String id;

  /// عنوان العقدة
  final String label;

  /// قيمة العقدة (تؤثر على حجم العقدة)
  final double value;

  /// لون العقدة (اختياري)
  final Color? color;

  /// بيانات إضافية (اختياري)
  final Map<String, dynamic>? data;

  /// موقع العقدة (يتم تحديثه أثناء المحاكاة)
  Offset position;

  /// قوة العقدة (يتم تحديثها أثناء المحاكاة)
  double force = 0;

  /// إنشاء عقدة شبكة
  NetworkNode({
    required this.id,
    required this.label,
    this.value = 1.0,
    this.color,
    this.data,
    this.position = Offset.zero,
  });
}

/// نموذج رابط الشبكة
class NetworkLink {
  /// معرف العقدة المصدر
  final String sourceId;

  /// معرف العقدة الهدف
  final String targetId;

  /// قيمة الرابط (تؤثر على سمك الخط)
  final double value;

  /// عنوان الرابط (اختياري)
  final String? label;

  /// لون الرابط (اختياري)
  final Color? color;

  /// نوع الخط (اختياري)
  final NetworkLinkStyle style;

  /// بيانات إضافية (اختياري)
  final Map<String, dynamic>? data;

  /// إنشاء رابط شبكة
  const NetworkLink({
    required this.sourceId,
    required this.targetId,
    this.value = 1.0,
    this.label,
    this.color,
    this.style = NetworkLinkStyle.solid,
    this.data,
  });
}

/// نمط خط الرابط
enum NetworkLinkStyle {
  /// خط متصل
  solid,

  /// خط متقطع
  dashed,

  /// خط منقط
  dotted,

  /// سهم
  arrow,

  /// سهم مزدوج
  doubleArrow,
}

/// مكون مخطط الشبكة المحسن
///
/// يوفر هذا المكون مخططًا للعلاقات والشبكات مع دعم للتفاعل
/// مفيد لتمثيل العلاقات بين المهام أو الأشخاص أو الكيانات
class EnhancedNetworkChart extends StatefulWidget {
  /// قائمة العقد
  final List<NetworkNode> nodes;

  /// قائمة الروابط
  final List<NetworkLink> links;

  /// عنوان المخطط
  final String? title;

  /// هل يتم عرض التسميات
  final bool showLabels;

  /// هل يتم عرض وسيلة الإيضاح
  final bool showLegend;

  /// هل يتم عرض قيم الروابط
  final bool showLinkValues;

  /// هل يتم عرض قيم العقد
  final bool showNodeValues;

  /// قوة التنافر بين العقد
  final double repulsionForce;

  /// قوة الجذب للروابط
  final double attractionForce;

  /// دالة استدعاء عند النقر على عقدة
  final Function(NetworkNode)? onNodeTap;

  /// دالة استدعاء عند النقر على رابط
  final Function(NetworkLink)? onLinkTap;

  /// دالة استدعاء عند تغيير التصفية
  final Function(DateTime?, DateTime?, dynamic)? onFilterChanged;

  /// دالة استدعاء عند التصدير
  final Function(String)? onExport;

  /// دالة استدعاء عند تغيير نوع المخطط
  final Function(dynamic)? onChartTypeChanged;

  /// هل يتم عرض خيارات التصفية
  final bool showFilterOptions;

  /// هل يتم عرض خيارات التصدير
  final bool showExportOptions;

  /// هل يتم عرض خيارات نوع المخطط
  final bool showChartTypeOptions;

  /// تنسيقات التصدير المدعومة
  final List<String> supportedExportFormats;

  /// أنواع المخططات المدعومة
  final List<dynamic> supportedChartTypes;

  /// إنشاء مكون مخطط الشبكة المحسن
  const EnhancedNetworkChart({
    super.key,
    required this.nodes,
    required this.links,
    this.title,
    this.showLabels = true,
    this.showLegend = true,
    this.showLinkValues = false,
    this.showNodeValues = true,
    this.repulsionForce = 100.0,
    this.attractionForce = 0.1,
    this.onNodeTap,
    this.onLinkTap,
    this.onFilterChanged,
    this.onExport,
    this.onChartTypeChanged,
    this.showFilterOptions = false,
    this.showExportOptions = false,
    this.showChartTypeOptions = false,
    this.supportedExportFormats = const ['pdf', 'excel', 'csv', 'png'],
    this.supportedChartTypes = const [],
  });

  @override
  State<EnhancedNetworkChart> createState() => _EnhancedNetworkChartState();
}

class _EnhancedNetworkChartState extends State<EnhancedNetworkChart> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  List<NetworkNode> _simulatedNodes = [];
  double _scale = 1.0;
  Offset _offset = Offset.zero;
  NetworkNode? _selectedNode;
  NetworkLink? _selectedLink;
  bool _isDragging = false;
  Offset? _dragStartPosition;
  NetworkNode? _draggedNode;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 30),
    )..repeat(reverse: false);
    _animationController.addListener(_updateSimulation);

    // نسخ العقد للمحاكاة
    _initializeSimulation();
  }

  @override
  void didUpdateWidget(EnhancedNetworkChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.nodes != widget.nodes || oldWidget.links != widget.links) {
      _initializeSimulation();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// تهيئة المحاكاة
  void _initializeSimulation() {
    final random = math.Random();

    // حساب عدد الصفوف والأعمدة المناسب لتوزيع العقد
    final nodeCount = widget.nodes.length;
    final gridSize = math.sqrt(nodeCount).ceil();
    final cellSize = 100.0;

    // نسخ العقد مع تعيين مواقع منتظمة مع إضافة عشوائية صغيرة
    _simulatedNodes = [];

    for (int i = 0; i < widget.nodes.length; i++) {
      final node = widget.nodes[i];

      // حساب موقع الخلية في الشبكة
      final row = i ~/ gridSize;
      final col = i % gridSize;

      // إضافة عشوائية صغيرة لتجنب الاصطفاف المثالي
      final jitter = 20.0;
      final jitterX = (random.nextDouble() * 2 - 1) * jitter;
      final jitterY = (random.nextDouble() * 2 - 1) * jitter;

      // حساب الموقع النهائي
      final x = (col - gridSize / 2) * cellSize + jitterX;
      final y = (row - gridSize / 2) * cellSize + jitterY;

      _simulatedNodes.add(NetworkNode(
        id: node.id,
        label: node.label,
        value: node.value,
        color: node.color,
        data: node.data,
        position: Offset(x, y),
      ));
    }
  }

  /// تحديث المحاكاة
  void _updateSimulation() {
    if (!mounted) return;

    // تطبيق خوارزمية تخطيط القوة الموجهة
    _applyForceDirectedLayout();

    // استخدام mounted مرة أخرى للتأكد من أن المكون لا يزال موجودًا
    if (mounted) {
      // استخدام تأخير لتقليل عدد مرات إعادة البناء
      Future.microtask(() {
        if (mounted) setState(() {});
      });
    }
  }

  /// تطبيق خوارزمية تخطيط القوة الموجهة
  void _applyForceDirectedLayout() {
    // تجنب الحسابات إذا كان عدد العقد قليلًا جدًا
    if (_simulatedNodes.length <= 1) return;

    // معاملات لتحسين الأداء
    final dampingFactor = 0.05;
    final maxDistance = 300.0;
    final minDistance = 10.0;

    // حساب القوى بين العقد
    for (int i = 0; i < _simulatedNodes.length; i++) {
      final node1 = _simulatedNodes[i];

      // تجنب تحريك العقدة إذا كانت محددة (يتم سحبها)
      if (_isDragging && node1 == _draggedNode) continue;

      // قوة التنافر بين العقد
      for (int j = 0; j < _simulatedNodes.length; j++) {
        if (i == j) continue;

        final node2 = _simulatedNodes[j];
        final dx = node1.position.dx - node2.position.dx;
        final dy = node1.position.dy - node2.position.dy;
        final distanceSquared = dx * dx + dy * dy;

        // تحسين الأداء باستخدام مربع المسافة بدلاً من الجذر التربيعي
        if (distanceSquared > 0 && distanceSquared < maxDistance * maxDistance) {
          // استخدام مربع المسافة مباشرة لتجنب عملية الجذر التربيعي
          final force = widget.repulsionForce / math.max(distanceSquared, minDistance * minDistance);

          // حساب المسافة فقط عند الحاجة
          final distance = math.sqrt(distanceSquared);
          final forceX = dx / distance * force;
          final forceY = dy / distance * force;

          node1.position = Offset(
            node1.position.dx + forceX * dampingFactor,
            node1.position.dy + forceY * dampingFactor,
          );
        }
      }
    }

    // قوة الجذب للروابط
    for (final link in widget.links) {
      final sourceNode = _simulatedNodes.firstWhere(
        (node) => node.id == link.sourceId,
        orElse: () => _simulatedNodes.first,
      );

      final targetNode = _simulatedNodes.firstWhere(
        (node) => node.id == link.targetId,
        orElse: () => _simulatedNodes.last,
      );

      // تجنب تحريك العقد إذا كانت محددة (يتم سحبها)
      if (_isDragging && (sourceNode == _draggedNode || targetNode == _draggedNode)) continue;

      final dx = sourceNode.position.dx - targetNode.position.dx;
      final dy = sourceNode.position.dy - targetNode.position.dy;
      final distanceSquared = dx * dx + dy * dy;

      if (distanceSquared > 0) {
        final distance = math.sqrt(distanceSquared);
        final force = distance * widget.attractionForce;
        final forceX = dx / distance * force;
        final forceY = dy / distance * force;

        sourceNode.position = Offset(
          sourceNode.position.dx - forceX * dampingFactor,
          sourceNode.position.dy - forceY * dampingFactor,
        );

        targetNode.position = Offset(
          targetNode.position.dx + forceX * dampingFactor,
          targetNode.position.dy + forceY * dampingFactor,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // مكون التصفية والتصدير
        if (widget.showFilterOptions || widget.showExportOptions || widget.showChartTypeOptions)
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.showFilterOptions)
                IconButton(
                  icon: const Icon(Icons.filter_list),
                  onPressed: () {
                    // تنفيذ وظيفة التصفية
                    if (widget.onFilterChanged != null) {
                      widget.onFilterChanged!(null, null, 'all');
                    }
                  },
                  tooltip: 'تصفية',
                ),
              if (widget.showExportOptions)
                IconButton(
                  icon: const Icon(Icons.download),
                  onPressed: () {
                    // تنفيذ وظيفة التصدير
                    if (widget.onExport != null && widget.supportedExportFormats.isNotEmpty) {
                      widget.onExport!(widget.supportedExportFormats.first);
                    }
                  },
                  tooltip: 'تصدير',
                ),
              if (widget.showChartTypeOptions)
                IconButton(
                  icon: const Icon(Icons.bar_chart),
                  onPressed: () {
                    // تنفيذ وظيفة تغيير نوع المخطط
                    if (widget.onChartTypeChanged != null && widget.supportedChartTypes.isNotEmpty) {
                      widget.onChartTypeChanged!(widget.supportedChartTypes.first);
                    }
                  },
                  tooltip: 'تغيير نوع المخطط',
                ),
            ],
          ),

        // عنوان المخطط
        if (widget.title != null)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              widget.title!,
              style: AppStyles.titleMedium,
              textAlign: TextAlign.center,
            ),
          ),

        // مخطط الشبكة
        Expanded(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return Container(
                width: constraints.maxWidth,
                height: constraints.maxHeight,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onScaleStart: (details) {
                      _dragStartPosition = details.localFocalPoint;

                      // التحقق مما إذا كان النقر على عقدة
                      _draggedNode = _getNodeAtPosition(details.localFocalPoint);
                      _isDragging = _draggedNode != null;
                    },
                    onScaleUpdate: (details) {
                      if (_dragStartPosition == null) return;

                      setState(() {
                        if (_isDragging && _draggedNode != null) {
                          // تحريك العقدة
                          _draggedNode!.position = Offset(
                            _draggedNode!.position.dx + (details.localFocalPoint.dx - _dragStartPosition!.dx) / _scale,
                            _draggedNode!.position.dy + (details.localFocalPoint.dy - _dragStartPosition!.dy) / _scale,
                          );
                        } else {
                          // تحريك المخطط بأكمله
                          _offset = Offset(
                            _offset.dx + (details.localFocalPoint.dx - _dragStartPosition!.dx),
                            _offset.dy + (details.localFocalPoint.dy - _dragStartPosition!.dy),
                          );
                        }

                        _dragStartPosition = details.localFocalPoint;

                        // تغيير مقياس المخطط (مع حد أدنى وأقصى)
                        _scale = details.scale.clamp(0.5, 3.0);
                      });
                    },
                    onScaleEnd: (details) {
                      _isDragging = false;
                      _draggedNode = null;
                    },
                    onTapUp: (details) {
                      // التحقق مما إذا كان النقر على عقدة
                      final node = _getNodeAtPosition(details.localPosition);
                      if (node != null) {
                        setState(() {
                          _selectedNode = node;
                          _selectedLink = null;
                        });

                        if (widget.onNodeTap != null) {
                          widget.onNodeTap!(node);
                        }
                        return;
                      }

                      // التحقق مما إذا كان النقر على رابط
                      final link = _getLinkAtPosition(details.localPosition);
                      if (link != null) {
                        setState(() {
                          _selectedNode = null;
                          _selectedLink = link;
                        });

                        if (widget.onLinkTap != null) {
                          widget.onLinkTap!(link);
                        }
                        return;
                      }

                      // إلغاء التحديد
                      setState(() {
                        _selectedNode = null;
                        _selectedLink = null;
                      });
                    },
                    child: CustomPaint(
                      size: Size(constraints.maxWidth, constraints.maxHeight),
                      painter: _NetworkChartPainter(
                        nodes: _simulatedNodes,
                        links: widget.links,
                        scale: _scale,
                        offset: _offset,
                        showLabels: widget.showLabels,
                        showLinkValues: widget.showLinkValues,
                        showNodeValues: widget.showNodeValues,
                        selectedNode: _selectedNode,
                        selectedLink: _selectedLink,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// الحصول على العقدة عند موقع معين
  NetworkNode? _getNodeAtPosition(Offset position) {
    for (final node in _simulatedNodes) {
      final nodePosition = Offset(
        node.position.dx * _scale + _offset.dx,
        node.position.dy * _scale + _offset.dy,
      );

      final distance = (nodePosition - position).distance;
      final nodeRadius = 10 + node.value * 5;

      if (distance <= nodeRadius) {
        return node;
      }
    }

    return null;
  }

  /// الحصول على الرابط عند موقع معين
  NetworkLink? _getLinkAtPosition(Offset position) {
    for (final link in widget.links) {
      final sourceNode = _simulatedNodes.firstWhere(
        (node) => node.id == link.sourceId,
        orElse: () => _simulatedNodes.first,
      );

      final targetNode = _simulatedNodes.firstWhere(
        (node) => node.id == link.targetId,
        orElse: () => _simulatedNodes.last,
      );

      final sourcePosition = Offset(
        sourceNode.position.dx * _scale + _offset.dx,
        sourceNode.position.dy * _scale + _offset.dy,
      );

      final targetPosition = Offset(
        targetNode.position.dx * _scale + _offset.dx,
        targetNode.position.dy * _scale + _offset.dy,
      );

      final distance = _distanceToLine(position, sourcePosition, targetPosition);

      if (distance <= 5) {
        return link;
      }
    }

    return null;
  }

  /// حساب المسافة من نقطة إلى خط
  double _distanceToLine(Offset point, Offset lineStart, Offset lineEnd) {
    final dx = lineEnd.dx - lineStart.dx;
    final dy = lineEnd.dy - lineStart.dy;
    final length = math.sqrt(dx * dx + dy * dy);

    if (length == 0) return (point - lineStart).distance;

    final t = ((point.dx - lineStart.dx) * dx + (point.dy - lineStart.dy) * dy) / (length * length);

    if (t < 0) return (point - lineStart).distance;
    if (t > 1) return (point - lineEnd).distance;

    final projection = Offset(
      lineStart.dx + t * dx,
      lineStart.dy + t * dy,
    );

    return (point - projection).distance;
  }
}

/// رسام مخطط الشبكة
class _NetworkChartPainter extends CustomPainter {
  final List<NetworkNode> nodes;
  final List<NetworkLink> links;
  final double scale;
  final Offset offset;
  final bool showLabels;
  final bool showLinkValues;
  final bool showNodeValues;
  final NetworkNode? selectedNode;
  final NetworkLink? selectedLink;

  _NetworkChartPainter({
    required this.nodes,
    required this.links,
    required this.scale,
    required this.offset,
    required this.showLabels,
    required this.showLinkValues,
    required this.showNodeValues,
    this.selectedNode,
    this.selectedLink,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // رسم الروابط
    _drawLinks(canvas, size);

    // رسم العقد
    _drawNodes(canvas, size);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;

  /// رسم الروابط
  void _drawLinks(Canvas canvas, Size size) {
    for (final link in links) {
      final sourceNode = nodes.firstWhere(
        (node) => node.id == link.sourceId,
        orElse: () => nodes.first,
      );

      final targetNode = nodes.firstWhere(
        (node) => node.id == link.targetId,
        orElse: () => nodes.last,
      );

      final sourcePosition = Offset(
        sourceNode.position.dx * scale + offset.dx,
        sourceNode.position.dy * scale + offset.dy,
      );

      final targetPosition = Offset(
        targetNode.position.dx * scale + offset.dx,
        targetNode.position.dy * scale + offset.dy,
      );

      // تحديد لون الرابط
      final color = link.color ?? Colors.grey;

      // تحديد سمك الخط
      final strokeWidth = link == selectedLink ? 3.0 : 1.5;

      // رسم الخط
      final paint = Paint()
        ..color = color
        ..strokeWidth = strokeWidth
        ..style = PaintingStyle.stroke;

      // رسم الخط حسب النمط
      switch (link.style) {
        case NetworkLinkStyle.solid:
          canvas.drawLine(sourcePosition, targetPosition, paint);
          break;
        case NetworkLinkStyle.dashed:
          _drawDashedLine(canvas, sourcePosition, targetPosition, paint);
          break;
        case NetworkLinkStyle.dotted:
          _drawDottedLine(canvas, sourcePosition, targetPosition, paint);
          break;
        case NetworkLinkStyle.arrow:
          _drawArrow(canvas, sourcePosition, targetPosition, paint);
          break;
        case NetworkLinkStyle.doubleArrow:
          _drawDoubleArrow(canvas, sourcePosition, targetPosition, paint);
          break;
      }

      // رسم قيمة الرابط إذا كان مطلوبًا
      if (showLinkValues && link.value > 0) {
        final midPoint = Offset(
          (sourcePosition.dx + targetPosition.dx) / 2,
          (sourcePosition.dy + targetPosition.dy) / 2,
        );

        _drawText(
          canvas,
          link.value.toStringAsFixed(1),
          midPoint,
          const TextStyle(
            color: Colors.black,
            fontSize: 10,
            backgroundColor: Colors.white70,
          ),
        );
      }
    }
  }

  /// رسم العقد
  void _drawNodes(Canvas canvas, Size size) {
    for (final node in nodes) {
      final position = Offset(
        node.position.dx * scale + offset.dx,
        node.position.dy * scale + offset.dy,
      );

      // تحديد حجم العقدة
      final radius = 10 + node.value * 5;

      // تحديد لون العقدة
      final color = node.color ?? Colors.blue;

      // رسم العقدة
      final paint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;

      canvas.drawCircle(position, radius, paint);

      // رسم حدود العقدة
      final borderPaint = Paint()
        ..color = node == selectedNode ? Colors.yellow : Colors.black
        ..style = PaintingStyle.stroke
        ..strokeWidth = node == selectedNode ? 2.0 : 1.0;

      canvas.drawCircle(position, radius, borderPaint);

      // رسم تسمية العقدة إذا كان مطلوبًا
      if (showLabels) {
        _drawText(
          canvas,
          node.label,
          Offset(position.dx, position.dy + radius + 5),
          const TextStyle(
            color: Colors.black,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        );
      }

      // رسم قيمة العقدة إذا كان مطلوبًا
      if (showNodeValues && node.value > 0) {
        _drawText(
          canvas,
          node.value.toStringAsFixed(1),
          Offset(position.dx, position.dy),
          const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        );
      }
    }
  }

  /// رسم نص
  void _drawText(Canvas canvas, String text, Offset position, TextStyle style) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: style,
      ),
      textDirection: TextDirection.rtl,
      textAlign: TextAlign.center,
    );

    textPainter.layout();

    textPainter.paint(
      canvas,
      Offset(
        position.dx - textPainter.width / 2,
        position.dy - textPainter.height / 2,
      ),
    );
  }

  /// رسم خط متقطع
  void _drawDashedLine(Canvas canvas, Offset start, Offset end, Paint paint) {
    final dx = end.dx - start.dx;
    final dy = end.dy - start.dy;
    final distance = math.sqrt(dx * dx + dy * dy);
    final dashLength = 5.0;
    final gapLength = 3.0;
    final steps = (distance / (dashLength + gapLength)).floor();

    for (int i = 0; i < steps; i++) {
      final startFraction = i * (dashLength + gapLength) / distance;
      final endFraction = (i * (dashLength + gapLength) + dashLength) / distance;

      final dashStart = Offset(
        start.dx + dx * startFraction,
        start.dy + dy * startFraction,
      );

      final dashEnd = Offset(
        start.dx + dx * endFraction,
        start.dy + dy * endFraction,
      );

      canvas.drawLine(dashStart, dashEnd, paint);
    }
  }

  /// رسم خط منقط
  void _drawDottedLine(Canvas canvas, Offset start, Offset end, Paint paint) {
    final dx = end.dx - start.dx;
    final dy = end.dy - start.dy;
    final distance = math.sqrt(dx * dx + dy * dy);
    final dotSpacing = 5.0;
    final steps = (distance / dotSpacing).floor();

    for (int i = 0; i < steps; i++) {
      final fraction = i * dotSpacing / distance;

      final dotPosition = Offset(
        start.dx + dx * fraction,
        start.dy + dy * fraction,
      );

      canvas.drawCircle(dotPosition, 1.0, paint);
    }
  }

  /// رسم سهم
  void _drawArrow(Canvas canvas, Offset start, Offset end, Paint paint) {
    // رسم الخط
    canvas.drawLine(start, end, paint);

    // رسم رأس السهم
    final dx = end.dx - start.dx;
    final dy = end.dy - start.dy;
    final distance = math.sqrt(dx * dx + dy * dy);

    if (distance > 0) {
      final unitX = dx / distance;
      final unitY = dy / distance;

      final arrowSize = 10.0;

      final arrowPoint1 = Offset(
        end.dx - arrowSize * unitX + arrowSize * unitY * 0.5,
        end.dy - arrowSize * unitY - arrowSize * unitX * 0.5,
      );

      final arrowPoint2 = Offset(
        end.dx - arrowSize * unitX - arrowSize * unitY * 0.5,
        end.dy - arrowSize * unitY + arrowSize * unitX * 0.5,
      );

      final arrowPath = Path()
        ..moveTo(end.dx, end.dy)
        ..lineTo(arrowPoint1.dx, arrowPoint1.dy)
        ..lineTo(arrowPoint2.dx, arrowPoint2.dy)
        ..close();

      canvas.drawPath(arrowPath, paint..style = PaintingStyle.fill);
    }
  }

  /// رسم سهم مزدوج
  void _drawDoubleArrow(Canvas canvas, Offset start, Offset end, Paint paint) {
    // رسم الخط
    canvas.drawLine(start, end, paint);

    // رسم رأس السهم عند النهاية
    final dx = end.dx - start.dx;
    final dy = end.dy - start.dy;
    final distance = math.sqrt(dx * dx + dy * dy);

    if (distance > 0) {
      final unitX = dx / distance;
      final unitY = dy / distance;

      final arrowSize = 10.0;

      // رأس السهم عند النهاية
      final endArrowPoint1 = Offset(
        end.dx - arrowSize * unitX + arrowSize * unitY * 0.5,
        end.dy - arrowSize * unitY - arrowSize * unitX * 0.5,
      );

      final endArrowPoint2 = Offset(
        end.dx - arrowSize * unitX - arrowSize * unitY * 0.5,
        end.dy - arrowSize * unitY + arrowSize * unitX * 0.5,
      );

      final endArrowPath = Path()
        ..moveTo(end.dx, end.dy)
        ..lineTo(endArrowPoint1.dx, endArrowPoint1.dy)
        ..lineTo(endArrowPoint2.dx, endArrowPoint2.dy)
        ..close();

      canvas.drawPath(endArrowPath, paint..style = PaintingStyle.fill);

      // رأس السهم عند البداية
      final startArrowPoint1 = Offset(
        start.dx + arrowSize * unitX + arrowSize * unitY * 0.5,
        start.dy + arrowSize * unitY - arrowSize * unitX * 0.5,
      );

      final startArrowPoint2 = Offset(
        start.dx + arrowSize * unitX - arrowSize * unitY * 0.5,
        start.dy + arrowSize * unitY + arrowSize * unitX * 0.5,
      );

      final startArrowPath = Path()
        ..moveTo(start.dx, start.dy)
        ..lineTo(startArrowPoint1.dx, startArrowPoint1.dy)
        ..lineTo(startArrowPoint2.dx, startArrowPoint2.dy)
        ..close();

      canvas.drawPath(startArrowPath, paint..style = PaintingStyle.fill);
    }
  }
}
