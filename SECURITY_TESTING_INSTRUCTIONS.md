# 🧪 تعليمات اختبار الإصلاحات الأمنية

## 📋 كيفية اختبار الإصلاحات الأمنية

### 🔧 الإعداد المطلوب

1. **إنشاء مستخدم اختبار بدون صلاحيات:**
   ```sql
   -- في قاعدة البيانات، قم بإنشاء مستخدم جديد
   INSERT INTO Users (name, email, password, role_id) 
   VALUES ('Test User', '<EMAIL>', 'hashed_password', 2);
   
   -- تأكد من أن الدور لا يحتوي على صلاحية tasks.view_details
   DELETE FROM role_default_permissions 
   WHERE role_id = 2 AND permission_id = (
       SELECT id FROM permissions WHERE name = 'tasks.view_details'
   );
   ```

2. **تسجيل الدخول بالمستخدم الاختبار**

### 🧪 اختبارات الثغرات المصلحة

#### اختبار 1: ثغرة الإشعارات
```
الخطوات:
1. انتقل إلى صفحة الإشعارات
2. انقر على أي إشعار متعلق بمهمة

النتيجة المتوقعة:
✅ رسالة خطأ: "ليس لديك صلاحية لعرض تفاصيل المهام"
✅ عدم التنقل لصفحة تفاصيل المهمة
❌ إذا تم التنقل = الثغرة لم تُصلح
```

#### اختبار 2: ثغرة لوحة المستخدم
```
الخطوات:
1. انتقل إلى لوحة المستخدم
2. انقر على أي مهمة في القائمة

النتيجة المتوقعة:
✅ رسالة خطأ: "ليس لديك صلاحية لعرض تفاصيل المهام"
✅ عدم التنقل لصفحة تفاصيل المهمة
❌ إذا تم التنقل = الثغرة لم تُصلح
```

#### اختبار 3: ثغرة البحث الموحد
```
الخطوات:
1. انتقل إلى صفحة البحث
2. ابحث عن مهمة
3. انقر على نتيجة البحث

النتيجة المتوقعة:
✅ رسالة خطأ: "ليس لديك صلاحية لعرض تفاصيل المهام"
✅ عدم التنقل لصفحة تفاصيل المهمة
❌ إذا تم التنقل = الثغرة لم تُصلح
```

#### اختبار 4: ثغرة تفاصيل الأقسام
```
الخطوات:
1. انتقل إلى صفحة تفاصيل أي قسم
2. انقر على أي مهمة في قائمة مهام القسم

النتيجة المتوقعة:
✅ رسالة خطأ: "ليس لديك صلاحية لعرض تفاصيل المهام"
✅ عدم التنقل لصفحة تفاصيل المهمة
❌ إذا تم التنقل = الثغرة لم تُصلح
```

#### اختبار 5: اختبار الوصول المباشر
```
الخطوات:
1. في شريط العناوين، اكتب: /task/detail
2. اضغط Enter

النتيجة المتوقعة:
✅ إعادة توجيه للصفحة الرئيسية
✅ عدم الوصول لصفحة تفاصيل المهمة
❌ إذا تم الوصول = Middleware لم يعمل
```

#### اختبار 6: اختبار TaskDetailScreen مباشرة
```
الخطوات:
1. محاولة الوصول لـ TaskDetailScreen بأي طريقة

النتيجة المتوقعة:
✅ رسالة خطأ فورية
✅ العودة للصفحة السابقة تلقائياً
❌ إذا تم عرض الشاشة = الحماية لم تعمل
```

### 🔍 اختبارات إضافية

#### اختبار الصلاحيات الصحيحة
```
الخطوات:
1. سجل دخول بمستخدم لديه صلاحية tasks.view_details
2. كرر جميع الاختبارات السابقة

النتيجة المتوقعة:
✅ التنقل يعمل بشكل طبيعي
✅ لا توجد رسائل خطأ
❌ إذا ظهرت رسائل خطأ = مشكلة في فحص الصلاحيات
```

### 📊 تقرير النتائج

بعد إجراء الاختبارات، املأ هذا التقرير:

```
اختبار ثغرة الإشعارات: [ ] نجح [ ] فشل
اختبار ثغرة لوحة المستخدم: [ ] نجح [ ] فشل
اختبار ثغرة البحث الموحد: [ ] نجح [ ] فشل
اختبار ثغرة تفاصيل الأقسام: [ ] نجح [ ] فشل
اختبار الوصول المباشر: [ ] نجح [ ] فشل
اختبار TaskDetailScreen: [ ] نجح [ ] فشل

اختبار الصلاحيات الصحيحة: [ ] نجح [ ] فشل

التقييم العام: [ ] جميع الإصلاحات تعمل [ ] توجد مشاكل
```

### 🚨 في حالة فشل أي اختبار

1. **تحقق من الكود:**
   - تأكد من وجود فحص `canViewTaskDetails()`
   - تأكد من استيراد `UnifiedPermissionService`

2. **تحقق من قاعدة البيانات:**
   - تأكد من عدم وجود صلاحية `tasks.view_details` للمستخدم
   - تحقق من جدول `role_default_permissions`

3. **تحقق من الـ Middleware:**
   - تأكد من تطبيق `UnifiedPermissionMiddleware` على المسارات
   - تحقق من تسجيل الـ middleware في `app_routes.dart`

### 📝 ملاحظات مهمة

- **لا تختبر بحساب المدير:** المديرون لديهم جميع الصلاحيات
- **استخدم مستخدم عادي:** بدون صلاحيات خاصة
- **اختبر في بيئة آمنة:** لا تختبر في الإنتاج
- **وثق النتائج:** احتفظ بسجل للاختبارات

### 🎯 الهدف النهائي

جميع الاختبارات يجب أن تنجح لضمان:
- عدم إمكانية تجاوز الصلاحيات
- حماية شاملة لجميع نقاط الوصول
- تجربة مستخدم واضحة مع رسائل خطأ مفهومة
