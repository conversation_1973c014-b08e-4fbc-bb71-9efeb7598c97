-- ===================================================================
-- سكريبت التحقق من بنية قاعدة البيانات بعد الإصلاحات
-- تاريخ الإنشاء: 2025-01-05
-- الهدف: التحقق من تطبيق جميع الإصلاحات بنجاح
-- ===================================================================

USE [TaskManagementDB]
GO

PRINT '🔍 التحقق من بنية قاعدة البيانات...'
PRINT '=================================================='

-- ===================================================================
-- 1. التحقق من جدول user_permissions
-- ===================================================================

PRINT '📋 التحقق من جدول user_permissions:'

-- التحقق من وجود الأعمدة المطلوبة
SELECT 
    'user_permissions' as TableName,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user_permissions'
ORDER BY ORDINAL_POSITION

-- إحصائيات الجدول
SELECT 
    'user_permissions' as TableName,
    COUNT(*) as TotalRecords,
    COUNT(CASE WHEN created_at IS NOT NULL AND created_at > 0 THEN 1 END) as ValidCreatedAt,
    COUNT(CASE WHEN granted_at IS NOT NULL AND granted_at > 0 THEN 1 END) as ValidGrantedAt,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as ActiveRecords,
    COUNT(CASE WHEN is_deleted = 0 THEN 1 END) as NonDeletedRecords
FROM [dbo].[user_permissions]

-- ===================================================================
-- 2. التحقق من جدول custom_roles
-- ===================================================================

PRINT '📋 التحقق من جدول custom_roles:'

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'custom_roles')
BEGIN
    -- بنية الجدول
    SELECT 
        'custom_roles' as TableName,
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'custom_roles'
    ORDER BY ORDINAL_POSITION
    
    -- إحصائيات الجدول
    SELECT 
        'custom_roles' as TableName,
        COUNT(*) as TotalRecords,
        COUNT(CASE WHEN is_deleted = 0 THEN 1 END) as ActiveRecords
    FROM [dbo].[custom_roles]
END
ELSE
BEGIN
    PRINT '❌ جدول custom_roles غير موجود'
END

-- ===================================================================
-- 3. التحقق من جدول user_custom_roles
-- ===================================================================

PRINT '📋 التحقق من جدول user_custom_roles:'

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'user_custom_roles')
BEGIN
    -- بنية الجدول
    SELECT 
        'user_custom_roles' as TableName,
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'user_custom_roles'
    ORDER BY ORDINAL_POSITION
    
    -- إحصائيات الجدول
    SELECT 
        'user_custom_roles' as TableName,
        COUNT(*) as TotalRecords,
        COUNT(CASE WHEN is_deleted = 0 THEN 1 END) as ActiveRecords
    FROM [dbo].[user_custom_roles]
END
ELSE
BEGIN
    PRINT '❌ جدول user_custom_roles غير موجود'
END

-- ===================================================================
-- 4. التحقق من جدول custom_role_permissions
-- ===================================================================

PRINT '📋 التحقق من جدول custom_role_permissions:'

IF EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'custom_role_permissions')
BEGIN
    -- بنية الجدول
    SELECT 
        'custom_role_permissions' as TableName,
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'custom_role_permissions'
    ORDER BY ORDINAL_POSITION
    
    -- إحصائيات الجدول
    SELECT 
        'custom_role_permissions' as TableName,
        COUNT(*) as TotalRecords,
        COUNT(CASE WHEN is_deleted = 0 THEN 1 END) as ActiveRecords
    FROM [dbo].[custom_role_permissions]
END
ELSE
BEGIN
    PRINT '❌ جدول custom_role_permissions غير موجود'
END

-- ===================================================================
-- 5. التحقق من المفاتيح الخارجية
-- ===================================================================

PRINT '🔗 التحقق من المفاتيح الخارجية:'

SELECT 
    fk.name AS ForeignKeyName,
    tp.name AS ParentTable,
    cp.name AS ParentColumn,
    tr.name AS ReferencedTable,
    cr.name AS ReferencedColumn
FROM sys.foreign_keys fk
INNER JOIN sys.tables tp ON fk.parent_object_id = tp.object_id
INNER JOIN sys.tables tr ON fk.referenced_object_id = tr.object_id
INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
INNER JOIN sys.columns cp ON fkc.parent_column_id = cp.column_id AND fkc.parent_object_id = cp.object_id
INNER JOIN sys.columns cr ON fkc.referenced_column_id = cr.column_id AND fkc.referenced_object_id = cr.object_id
WHERE tp.name IN ('user_permissions', 'custom_roles', 'user_custom_roles', 'custom_role_permissions')
ORDER BY tp.name, fk.name

-- ===================================================================
-- 6. التحقق من الفهارس
-- ===================================================================

PRINT '📊 التحقق من الفهارس:'

SELECT 
    t.name AS TableName,
    i.name AS IndexName,
    i.type_desc AS IndexType,
    i.is_unique AS IsUnique
FROM sys.indexes i
INNER JOIN sys.tables t ON i.object_id = t.object_id
WHERE t.name IN ('user_permissions', 'custom_roles', 'user_custom_roles', 'custom_role_permissions')
AND i.name IS NOT NULL
ORDER BY t.name, i.name

-- ===================================================================
-- 7. التحقق من القيود (Constraints)
-- ===================================================================

PRINT '🔒 التحقق من القيود:'

SELECT 
    t.name AS TableName,
    c.name AS ConstraintName,
    c.type_desc AS ConstraintType
FROM sys.check_constraints c
INNER JOIN sys.tables t ON c.parent_object_id = t.object_id
WHERE t.name IN ('user_permissions', 'custom_roles', 'user_custom_roles', 'custom_role_permissions')

UNION ALL

SELECT 
    t.name AS TableName,
    kc.name AS ConstraintName,
    kc.type_desc AS ConstraintType
FROM sys.key_constraints kc
INNER JOIN sys.tables t ON kc.parent_object_id = t.object_id
WHERE t.name IN ('user_permissions', 'custom_roles', 'user_custom_roles', 'custom_role_permissions')

ORDER BY TableName, ConstraintName

-- ===================================================================
-- 8. اختبار إدراج بيانات تجريبية
-- ===================================================================

PRINT '🧪 اختبار إدراج بيانات تجريبية:'

BEGIN TRY
    BEGIN TRANSACTION
    
    -- اختبار إدراج في user_permissions
    DECLARE @TestUserId INT = (SELECT TOP 1 id FROM users WHERE is_deleted = 0)
    DECLARE @TestPermissionId INT = (SELECT TOP 1 id FROM permissions)
    DECLARE @TestGrantedBy INT = (SELECT TOP 1 id FROM users WHERE is_deleted = 0)
    DECLARE @CurrentTime BIGINT = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
    
    IF @TestUserId IS NOT NULL AND @TestPermissionId IS NOT NULL AND @TestGrantedBy IS NOT NULL
    BEGIN
        INSERT INTO [dbo].[user_permissions] 
        (user_id, permission_id, granted_by, granted_at, is_active, expires_at, is_deleted, created_at)
        VALUES 
        (@TestUserId, @TestPermissionId, @TestGrantedBy, @CurrentTime, 1, NULL, 0, @CurrentTime)
        
        PRINT '✅ اختبار إدراج user_permissions نجح'
        
        -- حذف البيانات التجريبية
        DELETE FROM [dbo].[user_permissions] 
        WHERE user_id = @TestUserId AND permission_id = @TestPermissionId AND granted_by = @TestGrantedBy AND granted_at = @CurrentTime
    END
    ELSE
    BEGIN
        PRINT '⚠️ لا توجد بيانات كافية لاختبار user_permissions'
    END
    
    ROLLBACK TRANSACTION
    PRINT '✅ تم التراجع عن البيانات التجريبية'
    
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION
    PRINT '❌ فشل اختبار الإدراج: ' + ERROR_MESSAGE()
END CATCH

-- ===================================================================
-- 9. ملخص النتائج
-- ===================================================================

PRINT '=================================================='
PRINT '📊 ملخص التحقق من قاعدة البيانات:'
PRINT '=================================================='

-- عدد الجداول المطلوبة
DECLARE @RequiredTables TABLE (TableName NVARCHAR(50))
INSERT INTO @RequiredTables VALUES ('user_permissions'), ('custom_roles'), ('user_custom_roles'), ('custom_role_permissions')

SELECT 
    rt.TableName,
    CASE WHEN t.TABLE_NAME IS NOT NULL THEN '✅ موجود' ELSE '❌ مفقود' END AS Status,
    ISNULL((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = rt.TableName), 0) AS ColumnCount
FROM @RequiredTables rt
LEFT JOIN INFORMATION_SCHEMA.TABLES t ON rt.TableName = t.TABLE_NAME

PRINT ''
PRINT '🎯 التوصيات:'
PRINT '1. تأكد من أن جميع الجداول موجودة'
PRINT '2. تأكد من أن عمود created_at موجود في user_permissions'
PRINT '3. أعد تشغيل التطبيق لاختبار الوظائف'
PRINT '4. اختبر إضافة صلاحية جديدة من التطبيق'

PRINT '=================================================='
PRINT '🎉 انتهى التحقق من قاعدة البيانات'
PRINT '=================================================='

GO
