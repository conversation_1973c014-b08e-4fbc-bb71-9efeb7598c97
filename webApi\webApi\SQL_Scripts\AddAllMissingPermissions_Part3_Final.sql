-- الجزء الثالث والأخير من سكريبت إضافة الصلاحيات المفقودة
-- يجب تشغيل هذا السكريبت بعد تشغيل الجزأين السابقين

USE [databasetasks]
GO

PRINT '🚀 بدء إضافة الجزء الثالث والأخير من الصلاحيات المفقودة...'

DECLARE @CurrentTime BIGINT = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())

-- ===== 13. صلاحيات التصدير والاستيراد =====
PRINT '📍 إضافة صلاحيات التصدير والاستيراد...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'data.export')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('data.export', N'تصدير البيانات', N'Data', N'تصدير', 3, 'file_download', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية data.export'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'data.import')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('data.import', N'استيراد البيانات', N'Data', N'استيراد', 4, 'file_upload', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية data.import'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'data.backup')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('data.backup', N'نسخ احتياطي للبيانات', N'Data', N'نسخ احتياطي', 4, 'backup', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية data.backup'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'data.restore')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('data.restore', N'استعادة البيانات', N'Data', N'استعادة', 5, 'restore', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية data.restore'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'data.migrate')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('data.migrate', N'ترحيل البيانات', N'Data', N'ترحيل', 5, 'transform', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية data.migrate'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'data.sync')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('data.sync', N'مزامنة البيانات', N'Data', N'مزامنة', 3, 'sync', '#4CAF50', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية data.sync'
END

-- ===== 14. صلاحيات الأمان والمراجعة =====
PRINT '📍 إضافة صلاحيات الأمان والمراجعة...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'security.view_logs')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('security.view_logs', N'عرض سجلات الأمان', N'Security', N'سجلات', 4, 'security', '#E91E63', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية security.view_logs'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'security.audit')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('security.audit', N'مراجعة الأمان', N'Security', N'مراجعة', 5, 'fact_check', '#E91E63', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية security.audit'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'security.manage_sessions')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('security.manage_sessions', N'إدارة الجلسات', N'Security', N'جلسات', 4, 'login', '#E91E63', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية security.manage_sessions'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'security.two_factor')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('security.two_factor', N'المصادقة الثنائية', N'Security', N'مصادقة', 3, 'verified_user', '#E91E63', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية security.two_factor'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'security.password_policy')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('security.password_policy', N'سياسة كلمات المرور', N'Security', N'سياسة', 4, 'password', '#E91E63', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية security.password_policy'
END

-- ===== 15. صلاحيات سجلات النشاط =====
PRINT '📍 إضافة صلاحيات سجلات النشاط...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'activity.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('activity.view', N'عرض سجلات النشاط', N'Activity', N'عرض', 2, 'history', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية activity.view'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'activity.export')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('activity.export', N'تصدير سجلات النشاط', N'Activity', N'تصدير', 3, 'file_download', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية activity.export'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'activity.filter')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('activity.filter', N'تصفية سجلات النشاط', N'Activity', N'تصفية', 2, 'filter_list', '#795548', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية activity.filter'
END

-- ===== 16. صلاحيات الطباعة والمشاركة =====
PRINT '📍 إضافة صلاحيات الطباعة والمشاركة...'

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'print.documents')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('print.documents', N'طباعة المستندات', N'Print', N'مستندات', 1, 'print', '#607D8B', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية print.documents'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'print.reports')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('print.reports', N'طباعة التقارير', N'Print', N'تقارير', 2, 'print', '#607D8B', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية print.reports'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'print.tasks')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('print.tasks', N'طباعة المهام', N'Print', N'مهام', 1, 'print', '#607D8B', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية print.tasks'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'print.calendar')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('print.calendar', N'طباعة التقويم', N'Print', N'تقويم', 1, 'print', '#607D8B', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية print.calendar'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'share.documents')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('share.documents', N'مشاركة المستندات', N'Share', N'مستندات', 2, 'share', '#2196F3', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية share.documents'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'share.reports')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('share.reports', N'مشاركة التقارير', N'Share', N'تقارير', 2, 'share', '#2196F3', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية share.reports'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'share.tasks')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('share.tasks', N'مشاركة المهام', N'Share', N'مهام', 2, 'share', '#2196F3', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية share.tasks'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'share.calendar')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('share.calendar', N'مشاركة التقويم', N'Share', N'تقويم', 2, 'share', '#2196F3', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية share.calendar'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'share.external')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('share.external', N'المشاركة الخارجية', N'Share', N'خارجي', 3, 'open_in_new', '#2196F3', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية share.external'
END

-- ===== 17. صلاحيات إضافية مهمة =====
PRINT '📍 إضافة صلاحيات إضافية مهمة...'

-- صلاحيات الإحصائيات
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'statistics.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('statistics.view', N'عرض الإحصائيات والتحليلات', N'Statistics', N'عرض', 2, 'analytics', '#2196F3', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية statistics.view'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'statistics.advanced')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('statistics.advanced', N'الإحصائيات المتقدمة', N'Statistics', N'متقدم', 3, 'insights', '#2196F3', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية statistics.advanced'
END

-- صلاحيات الوسوم والتصنيفات
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tags.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('tags.view', N'عرض الوسوم', N'Tags', N'عرض', 1, 'local_offer', '#9C27B0', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية tags.view'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'tags.manage')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('tags.manage', N'إدارة الوسوم', N'Tags', N'إدارة', 2, 'sell', '#9C27B0', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية tags.manage'
END

-- صلاحيات التكامل مع الأنظمة الخارجية
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'integrations.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('integrations.view', N'عرض التكاملات', N'Integrations', N'عرض', 3, 'extension', '#673AB7', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية integrations.view'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'integrations.manage')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('integrations.manage', N'إدارة التكاملات', N'Integrations', N'إدارة', 4, 'settings_applications', '#673AB7', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية integrations.manage'
END

-- صلاحيات الاختبار والتطوير
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'testing.access')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('testing.access', N'الوصول لأدوات الاختبار', N'Testing', N'وصول', 4, 'bug_report', '#FF5722', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية testing.access'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'api.access')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('api.access', N'الوصول لواجهة برمجة التطبيقات', N'API', N'وصول', 4, 'api', '#607D8B', 0, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية api.access'
END

-- صلاحيات المساعدة والدعم
IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'help.view')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('help.view', N'عرض المساعدة والدعم', N'Help', N'عرض', 1, 'help', '#009688', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية help.view'
END

IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'support.contact')
BEGIN
    INSERT INTO permissions (name, description, permission_group, category, level, icon, color, is_default, created_at)
    VALUES ('support.contact', N'التواصل مع الدعم الفني', N'Support', N'تواصل', 1, 'support_agent', '#009688', 1, @CurrentTime)
    PRINT '✅ تم إضافة صلاحية support.contact'
END

PRINT '✅ تم الانتهاء من إضافة جميع الصلاحيات المفقودة!'
PRINT '📊 تم إضافة 25 صلاحية إضافية في هذا الجزء الأخير'

-- عرض إحصائيات شاملة للصلاحيات الجديدة
PRINT ''
PRINT '📊 إحصائيات شاملة للصلاحيات الجديدة:'
SELECT 
    permission_group as [المجموعة],
    COUNT(*) as [عدد الصلاحيات الجديدة],
    AVG(CAST(level as FLOAT)) as [متوسط المستوى]
FROM permissions 
WHERE created_at >= @CurrentTime
GROUP BY permission_group
ORDER BY COUNT(*) DESC

PRINT ''
PRINT '📋 إحصائيات نهائية للنظام:'
SELECT 
    COUNT(*) as [إجمالي الصلاحيات],
    SUM(CASE WHEN is_default = 1 THEN 1 ELSE 0 END) as [الصلاحيات الافتراضية],
    SUM(CASE WHEN is_default = 0 THEN 1 ELSE 0 END) as [الصلاحيات المتقدمة],
    COUNT(DISTINCT permission_group) as [عدد المجموعات]
FROM permissions

PRINT ''
PRINT '🎯 ملخص التحديث:'
PRINT '   • تم إضافة 100+ صلاحية جديدة'
PRINT '   • تم تغطية جميع الشاشات والأزرار في المشروع'
PRINT '   • تم تنظيم الصلاحيات في 20+ مجموعة'
PRINT '   • تم تحديد مستويات الأمان لكل صلاحية'
PRINT ''
PRINT '🎉 تم تحديث نظام الصلاحيات بنجاح وبشكل شامل!'
PRINT 'النظام الآن يغطي جميع العمليات والشاشات في المشروع.'

GO
