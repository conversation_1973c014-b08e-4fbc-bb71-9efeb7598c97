-- =====================================================
-- سكريبت شامل لإعداد الصلاحيات الجديدة (محدث لبنية الجدول الصحيحة)
-- تاريخ الإنشاء: 2025-01-06
-- الوصف: إضافة الصلاحيات الجديدة ومنحها للأدوار المناسبة
-- =====================================================

-- إعدادات السكريبت
SET NOCOUNT ON;
PRINT '🚀 بدء إعداد الصلاحيات الجديدة الشامل...'
PRINT '=============================================='
PRINT ''

-- التحقق من وجود الجداول المطلوبة
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'permissions')
   OR NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'roles')
   OR NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'user_permissions')
BEGIN
    PRINT '❌ خطأ: أحد الجداول المطلوبة غير موجود!'
    PRINT 'الجداول المطلوبة: permissions, roles, user_permissions'
    RETURN
END

-- بدء المعاملة الرئيسية
BEGIN TRANSACTION MainTransaction;

BEGIN TRY

    -- =====================================================
    -- المرحلة الأولى: إضافة الصلاحيات الجديدة
    -- =====================================================
    
    PRINT '📝 المرحلة 1: إضافة الصلاحيات الجديدة...'
    PRINT ''
    
    -- متغيرات لحساب الصلاحيات المضافة
    DECLARE @AddedPermissions INT = 0;
    
    -- جدول مؤقت للصلاحيات الجديدة
    CREATE TABLE #NewPermissions (
        name NVARCHAR(100),
        description NVARCHAR(255),
        permission_group NVARCHAR(50),
        category NVARCHAR(100)
    );
    
    -- إدراج الصلاحيات الجديدة في الجدول المؤقت
    INSERT INTO #NewPermissions VALUES
    -- صلاحيات الرسائل
    ('messages.mark_followup', 'تحديد الرسائل للمتابعة', 'messages', 'رسائل'),
    ('messages.pin', 'تثبيت الرسائل', 'messages', 'رسائل'),
    ('messages.edit', 'تعديل الرسائل', 'messages', 'رسائل'),
    ('messages.delete', 'حذف الرسائل', 'messages', 'رسائل'),
    ('messages.reply', 'الرد على الرسائل', 'messages', 'رسائل'),
    -- صلاحيات النظام
    ('admin.test_permissions', 'اختبار الصلاحيات', 'admin', 'إدارة'),
    ('admin.debug', 'الوصول لأدوات التشخيص والتطوير', 'admin', 'إدارة'),
    ('settings.notifications', 'تكوين إعدادات الإشعارات', 'settings', 'إعدادات'),
    ('reports.dynamic_access', 'الوصول للتقارير الديناميكية المتقدمة', 'reports', 'تقارير'),
    ('admin.database_repair', 'الوصول لأدوات إصلاح قاعدة البيانات', 'admin', 'إدارة'),
    ('archive.view_documents', 'عرض وتصفح مستندات الأرشيف', 'archive', 'أرشيف'),
    ('search.manage_history', 'إدارة ومسح سجل البحث', 'search', 'بحث');
    
    -- إضافة الصلاحيات الجديدة
    INSERT INTO permissions (name, description, permission_group, created_at, category)
    SELECT np.name, np.description, np.permission_group, DATEDIFF(SECOND, '1970-01-01', GETDATE()), np.category
    FROM #NewPermissions np
    WHERE NOT EXISTS (
        SELECT 1 FROM permissions p 
        WHERE p.name = np.name
    );
    
    SET @AddedPermissions = @@ROWCOUNT;
    
    PRINT '✅ تم إضافة ' + CAST(@AddedPermissions AS VARCHAR(10)) + ' صلاحية جديدة'
    
    -- عرض الصلاحيات المضافة
    IF @AddedPermissions > 0
    BEGIN
        PRINT ''
        PRINT '📋 الصلاحيات المضافة:'
        SELECT 
            '✅ ' + np.category + ': ' + np.name + ' - ' + np.description AS added_permission
        FROM #NewPermissions np
        WHERE NOT EXISTS (
            SELECT 1 FROM permissions p 
            WHERE p.name = np.name 
            AND p.created_at < DATEDIFF(SECOND, '1970-01-01', DATEADD(MINUTE, -1, GETDATE()))
        )
    END

    -- =====================================================
    -- المرحلة الثانية: منح الصلاحيات للأدوار
    -- =====================================================
    
    PRINT ''
    PRINT '🔐 المرحلة 2: منح الصلاحيات للأدوار...'
    PRINT ''
    
    -- تعريف قواعد منح الصلاحيات للأدوار
    DECLARE @SuperAdminRoleId INT, @AdminRoleId INT, @ModeratorRoleId INT, @UserRoleId INT;
    
    -- الحصول على معرفات الأدوار
    SELECT @SuperAdminRoleId = id FROM roles WHERE name IN ('Super Admin', 'مدير عام', 'super_admin');
    SELECT @AdminRoleId = id FROM roles WHERE name IN ('Admin', 'مدير', 'admin');
    SELECT @ModeratorRoleId = id FROM roles WHERE name IN ('Moderator', 'مشرف', 'moderator');
    SELECT @UserRoleId = id FROM roles WHERE name IN ('User', 'مستخدم', 'user');
    
    -- منح الصلاحيات للمدير العام (جميع الصلاحيات)
    IF @SuperAdminRoleId IS NOT NULL
    BEGIN
        INSERT INTO user_permissions (user_id, permission_id, granted_at)
        SELECT DISTINCT @SuperAdminRoleId, p.id, DATEDIFF(SECOND, '1970-01-01', GETDATE())
        FROM permissions p
        INNER JOIN #NewPermissions np ON p.name = np.name
        WHERE NOT EXISTS (
            SELECT 1 FROM user_permissions up 
            WHERE up.user_id = @SuperAdminRoleId AND up.permission_id = p.id
        );
        
        PRINT '👑 تم منح جميع الصلاحيات الجديدة للمدير العام'
    END
    
    -- منح الصلاحيات للمدير (صلاحيات الإدارة والنظام)
    IF @AdminRoleId IS NOT NULL
    BEGIN
        INSERT INTO user_permissions (user_id, permission_id, granted_at)
        SELECT DISTINCT @AdminRoleId, p.id, DATEDIFF(SECOND, '1970-01-01', GETDATE())
        FROM permissions p
        INNER JOIN #NewPermissions np ON p.name = np.name
        WHERE np.category IN ('إدارة', 'إعدادات', 'تقارير', 'أرشيف', 'بحث')
        AND NOT EXISTS (
            SELECT 1 FROM user_permissions up 
            WHERE up.user_id = @AdminRoleId AND up.permission_id = p.id
        );
        
        PRINT '🔧 تم منح صلاحيات الإدارة والنظام للمدير'
    END
    
    -- منح الصلاحيات للمشرف (صلاحيات الرسائل والبحث)
    IF @ModeratorRoleId IS NOT NULL
    BEGIN
        INSERT INTO user_permissions (user_id, permission_id, granted_at)
        SELECT DISTINCT @ModeratorRoleId, p.id, DATEDIFF(SECOND, '1970-01-01', GETDATE())
        FROM permissions p
        INNER JOIN #NewPermissions np ON p.name = np.name
        WHERE (np.category = 'رسائل' AND np.name IN ('messages.pin', 'messages.edit', 'messages.delete', 'messages.reply'))
           OR np.category IN ('بحث', 'أرشيف')
        AND NOT EXISTS (
            SELECT 1 FROM user_permissions up 
            WHERE up.user_id = @ModeratorRoleId AND up.permission_id = p.id
        );
        
        PRINT '👥 تم منح صلاحيات الرسائل والبحث للمشرف'
    END
    
    -- منح الصلاحيات للمستخدم العادي (صلاحيات أساسية)
    IF @UserRoleId IS NOT NULL
    BEGIN
        INSERT INTO user_permissions (user_id, permission_id, granted_at)
        SELECT DISTINCT @UserRoleId, p.id, DATEDIFF(SECOND, '1970-01-01', GETDATE())
        FROM permissions p
        INNER JOIN #NewPermissions np ON p.name = np.name
        WHERE (np.category = 'رسائل' AND np.name IN ('messages.reply', 'messages.mark_followup'))
           OR np.category = 'أرشيف'
        AND NOT EXISTS (
            SELECT 1 FROM user_permissions up 
            WHERE up.user_id = @UserRoleId AND up.permission_id = p.id
        );
        
        PRINT '👤 تم منح الصلاحيات الأساسية للمستخدم العادي'
    END

    -- =====================================================
    -- المرحلة الثالثة: التحقق والتقرير النهائي
    -- =====================================================
    
    PRINT ''
    PRINT '📊 المرحلة 3: التحقق من النتائج...'
    PRINT ''
    
    -- إحصائيات الصلاحيات الجديدة
    DECLARE @TotalNewPermissions INT = (
        SELECT COUNT(*) FROM permissions p
        INNER JOIN #NewPermissions np ON p.name = np.name
    );
    
    -- إحصائيات منح الصلاحيات
    DECLARE @TotalGrants INT = (
        SELECT COUNT(*) FROM user_permissions up
        INNER JOIN permissions p ON up.permission_id = p.id
        INNER JOIN #NewPermissions np ON p.name = np.name
        WHERE up.granted_at >= DATEDIFF(SECOND, '1970-01-01', DATEADD(MINUTE, -5, GETDATE()))
    );
    
    PRINT '📈 تقرير الإحصائيات النهائية:'
    PRINT '=============================='
    PRINT '• إجمالي الصلاحيات الجديدة: ' + CAST(@TotalNewPermissions AS VARCHAR(10)) + ' من 12'
    PRINT '• إجمالي منح الصلاحيات: ' + CAST(@TotalGrants AS VARCHAR(10))
    PRINT '• الأدوار المحدثة: ' + CAST((
        SELECT COUNT(DISTINCT r.name) 
        FROM roles r
        INNER JOIN user_permissions up ON r.id = up.user_id
        INNER JOIN permissions p ON up.permission_id = p.id
        INNER JOIN #NewPermissions np ON p.name = np.name
        WHERE up.granted_at >= DATEDIFF(SECOND, '1970-01-01', DATEADD(MINUTE, -5, GETDATE()))
    ) AS VARCHAR(10))
    
    -- تنظيف الجداول المؤقتة
    DROP TABLE #NewPermissions;
    
    -- تأكيد المعاملة
    COMMIT TRANSACTION MainTransaction;
    
    PRINT ''
    PRINT '🎉 تم إكمال إعداد الصلاحيات الجديدة بنجاح!'
    PRINT '✅ النظام جاهز الآن مع جميع الصلاحيات الجديدة'
    PRINT ''
    PRINT '📝 الخطوات التالية الموصى بها:'
    PRINT '• اختبار الصلاحيات الجديدة في التطبيق'
    PRINT '• مراجعة منح الصلاحيات للأدوار حسب احتياجاتك'
    PRINT '• تحديث وثائق النظام لتشمل الصلاحيات الجديدة'
    PRINT '• إعلام المستخدمين بالميزات الجديدة المتاحة'

END TRY
BEGIN CATCH
    -- في حالة حدوث خطأ
    ROLLBACK TRANSACTION MainTransaction;
    
    PRINT ''
    PRINT '❌ حدث خطأ أثناء إعداد الصلاحيات:'
    PRINT '=================================='
    PRINT 'رقم الخطأ: ' + CAST(ERROR_NUMBER() AS VARCHAR(10))
    PRINT 'رسالة الخطأ: ' + ERROR_MESSAGE()
    PRINT 'الإجراء: ' + ISNULL(ERROR_PROCEDURE(), 'غير محدد')
    PRINT 'السطر: ' + CAST(ERROR_LINE() AS VARCHAR(10))
    PRINT ''
    PRINT '🔄 يرجى مراجعة الخطأ وإعادة تشغيل السكريبت'
    
END CATCH

-- إعادة تعيين الإعدادات
SET NOCOUNT OFF;

PRINT ''
PRINT '📋 انتهى تنفيذ السكريبت'
PRINT '======================'

-- نهاية السكريبت
