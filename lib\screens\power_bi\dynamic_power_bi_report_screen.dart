import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

// Syncfusion Charts - مكتبة الرسوم البيانية المتقدمة
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../constants/app_styles.dart';
import '../../models/power_bi_models.dart';
import '../../models/chart_enums.dart';
import '../../models/advanced_filter_options.dart';
import '../../controllers/power_bi_controller.dart';
import '../widgets/app_drawer.dart';
import '../widgets/charts/enhanced_bar_chart.dart';
import '../widgets/charts/enhanced_pie_chart.dart';
import '../widgets/charts/enhanced_line_chart.dart';

// تم نقل TimeFilterType إلى chart_enums.dart لتجنب التكرار

/// فئة بيانات النقاط للرسم البياني النقطي
class ScatterData {
  final double x;
  final double y;

  ScatterData(this.x, this.y);
}

/// شاشة تقارير باور بي آي الديناميكية
///
/// توفر واجهة لإنشاء تقارير ديناميكية من عدة جداول دون الحاجة لحفظها
class DynamicPowerBIReportScreen extends StatefulWidget {
  const DynamicPowerBIReportScreen({super.key});

  @override
  State<DynamicPowerBIReportScreen> createState() =>
      _DynamicPowerBIReportScreenState();
}

class _DynamicPowerBIReportScreenState
    extends State<DynamicPowerBIReportScreen> {
  final PowerBIController _powerBIController = Get.find<PowerBIController>();

  // حالة الشاشة
  bool _isLoading = false;
  String _errorMessage = '';

  // بيانات التقرير
  String _selectedTable = '';
  List<String> _selectedColumns = [];
  String _xAxisColumn = '';
  String _yAxisColumn = '';
  String _sizeColumn = '';
  String _colorColumn = '';
  PowerBIChartType _selectedChartType = PowerBIChartType.bar;
  String _filterCriteria = '';

  // دالة التجميع للمحور الرأسي
  AggregateFunction _yAxisAggregateFunction = AggregateFunction.none;

  // متغيرات للتصفية حسب التاريخ
  DateTime? _startDate;
  DateTime? _endDate;
  TimeFilterType _selectedFilterType = TimeFilterType.month;
  String _dateField = 'createdAt'; // الحقل الافتراضي للتاريخ

  // متغيرات للجداول المتعددة
  bool _useMultipleTables = false;
  List<String> _relatedTables = [];
  List<String> _joinConditions = [];
  List<String> _joinTypes = [];
  List<Map<String, String>> _suggestedRelations = [];

  // بيانات الرسم البياني
  Map<String, dynamic> _chartData = {};

  @override
  void initState() {
    super.initState();

    // تأخير تحميل البيانات حتى اكتمال بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      await _powerBIController.loadAvailableTables();
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تحميل البيانات: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحميل العلاقات المقترحة بين الجداول
  Future<void> _loadSuggestedRelations(String relatedTable, int index) async {
    if (_selectedTable.isEmpty || relatedTable.isEmpty) {
      return;
    }

    setState(() {
      _isLoading = true;
      _suggestedRelations = [];
    });

    try {
      // الحصول على العلاقات المقترحة من وحدة التحكم
      final suggestions = await _powerBIController.suggestTableRelations(
          _selectedTable, relatedTable);

      // إضافة علاقات مقترحة إضافية للمهام والمستخدمين والأقسام
      List<Map<String, String>> enhancedSuggestions = [...suggestions];

      // إضافة علاقات خاصة بين المهام والمستخدمين
      if ((_selectedTable == 'tasks' && relatedTable == 'users') ||
          (_selectedTable == 'users' && relatedTable == 'tasks')) {
        // علاقة المهام بالمستخدم المسند إليه
        enhancedSuggestions.add({
          'table1': 'users',
          'column1': 'id',
          'table2': 'tasks',
          'column2': 'assigneeId',
          'joinCondition': 'users.id = tasks.assigneeId',
          'joinType': 'LEFT JOIN',
          'description': 'المهام المسندة للمستخدم',
        });

        // علاقة المهام بالمستخدم المنشئ
        enhancedSuggestions.add({
          'table1': 'users',
          'column1': 'id',
          'table2': 'tasks',
          'column2': 'creatorId',
          'joinCondition': 'users.id = tasks.creatorId',
          'joinType': 'LEFT JOIN',
          'description': 'المهام التي أنشأها المستخدم',
        });
      }

      // إضافة علاقات خاصة بين المهام والأقسام
      if ((_selectedTable == 'tasks' && relatedTable == 'departments') ||
          (_selectedTable == 'departments' && relatedTable == 'tasks')) {
        enhancedSuggestions.add({
          'table1': 'departments',
          'column1': 'id',
          'table2': 'tasks',
          'column2': 'departmentId',
          'joinCondition': 'departments.id = tasks.departmentId',
          'joinType': 'LEFT JOIN',
          'description': 'المهام في القسم',
        });
      }

      // إضافة علاقات خاصة بين المستخدمين والأقسام
      if ((_selectedTable == 'users' && relatedTable == 'departments') ||
          (_selectedTable == 'departments' && relatedTable == 'users')) {
        enhancedSuggestions.add({
          'table1': 'departments',
          'column1': 'id',
          'table2': 'users',
          'column2': 'departmentId',
          'joinCondition': 'departments.id = users.departmentId',
          'joinType': 'LEFT JOIN',
          'description': 'المستخدمين في القسم',
        });
      }

      if (mounted) {
        setState(() {
          _suggestedRelations = enhancedSuggestions;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'خطأ في تحميل العلاقات المقترحة: $e';
          _isLoading = false;
        });
      }
      debugPrint('خطأ في تحميل العلاقات المقترحة: $e');
    }
  }

  /// عرض العلاقات المقترحة في نافذة منبثقة
  void _showRelationSuggestions(String relatedTable, int index) async {
    // تحميل العلاقات المقترحة
    await _loadSuggestedRelations(relatedTable, index);

    if (!mounted) return;

    // قائمة بأسماء الجداول المعروضة للمستخدم
    final Map<String, String> tableDisplayNames = {
      'tasks': 'المهام',
      'users': 'المستخدمين',
      'departments': 'الأقسام',
      'task_types': 'أنواع المهام',
      'task_statuses': 'حالات المهام',
      'task_priorities': 'أولويات المهام',
      'task_comments': 'تعليقات المهام',
      'task_attachments': 'مرفقات المهام',
    };

    if (_suggestedRelations.isNotEmpty) {
      // إزالة العلاقات المكررة
      final List<Map<String, String>> uniqueRelations = [];
      final Set<String> uniqueJoinConditions = {};

      for (var relation in _suggestedRelations) {
        if (!uniqueJoinConditions.contains(relation['joinCondition'])) {
          uniqueJoinConditions.add(relation['joinCondition']!);
          uniqueRelations.add(relation);
        }
      }

      // عرض العلاقات المقترحة في نافذة منبثقة
      showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('اختر طريقة ربط البيانات'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: uniqueRelations.length,
              itemBuilder: (context, i) {
                final relation = uniqueRelations[i];

                // تحويل أسماء الجداول والأعمدة إلى أسماء عرض مفهومة
                final table1Display = tableDisplayNames[relation['table1']] ??
                    relation['table1']!;
                final table2Display = tableDisplayNames[relation['table2']] ??
                    relation['table2']!;
                final column1Display = relation['column1']!;
                final column2Display = relation['column2']!;

                // تحسين عرض العلاقة
                String relationDisplay = 'ربط $table1Display مع $table2Display';

                // تحديد نوع العلاقة (مفتاح أساسي مع مفتاح أجنبي)
                bool isPrimaryToForeign = relation['column1']!.contains('id') &&
                    !relation['column2']!.contains('id');
                bool isForeignToPrimary =
                    !relation['column1']!.contains('id') &&
                        relation['column2']!.contains('id');

                String relationDetail;
                if (isPrimaryToForeign) {
                  relationDetail =
                      'المفتاح الأساسي $column1Display = المفتاح الأجنبي $column2Display';
                } else if (isForeignToPrimary) {
                  relationDetail =
                      'المفتاح الأجنبي $column1Display = المفتاح الأساسي $column2Display';
                } else {
                  relationDetail =
                      'عمود $column1Display = عمود $column2Display';
                }

                return ListTile(
                  leading: const Icon(Icons.link, color: Colors.blue),
                  title: Text(relationDisplay),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(relationDetail),
                      if (relation.containsKey('description'))
                        Text(
                          relation['description']!,
                          style: const TextStyle(
                            fontStyle: FontStyle.italic,
                            color: Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                    ],
                  ),
                  onTap: () {
                    setState(() {
                      if (index < _joinConditions.length) {
                        _joinConditions[index] = relation['joinCondition']!;
                      } else {
                        _joinConditions.add(relation['joinCondition']!);
                      }
                    });
                    Navigator.of(dialogContext).pop();
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      );
    } else {
      // إذا لم يتم العثور على علاقات مقترحة
      showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('لا توجد علاقات'),
          content: const Text(
              'لم نتمكن من إيجاد طريقة تلقائية لربط هذه البيانات. يرجى اختيار قسم بيانات آخر.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('حسناً'),
            ),
          ],
        ),
      );
    }
  }

  /// تنفيذ الاستعلام وعرض النتائج
  Future<void> _executeQuery() async {
    if (_selectedTable.isEmpty) {
      setState(() {
        _errorMessage = 'يرجى اختيار جدول على الأقل';
      });
      return;
    }

    if (_selectedColumns.isEmpty) {
      setState(() {
        _errorMessage = 'يرجى اختيار عمود واحد على الأقل';
      });
      return;
    }

    if (_xAxisColumn.isEmpty || _yAxisColumn.isEmpty) {
      setState(() {
        _errorMessage = 'يرجى اختيار عمودي المحور الأفقي والرأسي';
      });
      return;
    }

    // التحقق من شروط الربط إذا كانت هناك جداول مرتبطة
    if (_useMultipleTables && _relatedTables.isNotEmpty) {
      for (int i = 0; i < _relatedTables.length; i++) {
        if (i >= _joinConditions.length || _joinConditions[i].isEmpty) {
          setState(() {
            _errorMessage = 'يرجى إدخال شروط الربط لجميع الجداول المرتبطة';
          });
          return;
        }
      }
    }

    // التحقق من وجود أسماء الأعمدة في البيانات
    debugPrint('المحور الأفقي: $_xAxisColumn');
    debugPrint('المحور الرأسي: $_yAxisColumn');

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // إنشاء فلتر تاريخ إذا كان هناك تاريخ بداية ونهاية
      String dateFilter = '';
      if (_startDate != null && _endDate != null) {
        final startDateStr = DateFormat('yyyy-MM-dd').format(_startDate!);
        final endDateStr = DateFormat('yyyy-MM-dd')
            .format(_endDate!.add(const Duration(days: 1)));

        // إضافة فلتر التاريخ حسب الحقل المحدد
        dateFilter = "$_dateField BETWEEN '$startDateStr' AND '$endDateStr'";

        // دمج فلتر التاريخ مع الفلتر الحالي إذا وجد
        if (_filterCriteria.isNotEmpty) {
          _filterCriteria = "($_filterCriteria) AND ($dateFilter)";
        } else {
          _filterCriteria = dateFilter;
        }
      }

      // تحضير قائمة الأعمدة المؤهلة (مع إضافة اسم الجدول)
      List<String> qualifiedColumns = [];

      // إضافة أعمدة الجدول الرئيسي
      for (final column in _selectedColumns) {
        // التحقق مما إذا كان العمود يحتوي بالفعل على اسم الجدول
        if (!column.contains('.')) {
          qualifiedColumns.add('$_selectedTable.$column');
        } else {
          qualifiedColumns.add(column);
        }
      }

      // تأكد من أن المحور الأفقي والرأسي مؤهلين بشكل صحيح
      String qualifiedXAxisColumn = _xAxisColumn;
      String qualifiedYAxisColumn = _yAxisColumn;

      // تأهيل المحور الأفقي إذا لم يكن مؤهلاً بالفعل
      if (!_xAxisColumn.contains('.') && _xAxisColumn.isNotEmpty) {
        qualifiedXAxisColumn = '$_selectedTable.$_xAxisColumn';
      }

      // تأهيل المحور الرأسي إذا لم يكن مؤهلاً بالفعل
      if (!_yAxisColumn.contains('.') && _yAxisColumn.isNotEmpty) {
        qualifiedYAxisColumn = '$_selectedTable.$_yAxisColumn';
      }

      // طباعة معلومات التصحيح
      debugPrint('الأعمدة المؤهلة: $qualifiedColumns');
      debugPrint('المحور الأفقي الأصلي: $_xAxisColumn');
      debugPrint('المحور الأفقي المؤهل: $qualifiedXAxisColumn');
      debugPrint('المحور الرأسي الأصلي: $_yAxisColumn');
      debugPrint('المحور الرأسي المؤهل: $qualifiedYAxisColumn');

      // إنشاء كائن تقرير مؤقت (لن يتم حفظه)
      final tempReport = PowerBIReport(
        id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
        title: 'تقرير مؤقت',
        chartType: _selectedChartType,
        createdById: 'temp_user',
        createdAt: DateTime.now(),
        tableName: _selectedTable,
        columnNames: qualifiedColumns, // استخدام الأعمدة المؤهلة
        xAxisColumn: qualifiedXAxisColumn, // استخدام المحور الأفقي المؤهل
        yAxisColumn: qualifiedYAxisColumn, // استخدام المحور الرأسي المؤهل
        yAxisAggregateFunction: _yAxisAggregateFunction,
        sizeColumn: _sizeColumn.isEmpty
            ? null
            : (_sizeColumn.contains('.')
                ? _sizeColumn
                : '$_selectedTable.$_sizeColumn'),
        colorColumn: _colorColumn.isEmpty
            ? null
            : (_colorColumn.contains('.')
                ? _colorColumn
                : '$_selectedTable.$_colorColumn'),
        filterCriteria: _filterCriteria.isEmpty ? null : _filterCriteria,
        relatedTables: _useMultipleTables && _relatedTables.isNotEmpty
            ? _relatedTables
            : null,
        joinConditions: _useMultipleTables && _joinConditions.isNotEmpty
            ? _joinConditions
            : null,
        joinTypes:
            _useMultipleTables && _joinTypes.isNotEmpty ? _joinTypes : null,
      );

      // تنفيذ الاستعلام وتحميل البيانات
      final data = await _powerBIController.getChartData(tempReport);

      // التحقق من وجود الأعمدة المطلوبة في البيانات
      if (data.containsKey('data') &&
          data['data'] is List &&
          (data['data'] as List).isNotEmpty) {
        final rawData = data['data'] as List;
        if (rawData.isNotEmpty && rawData.first is Map) {
          final firstRow = rawData.first as Map;
          final availableColumns = firstRow.keys.toList();

          // التحقق من وجود أعمدة المحاور (مع مراعاة التأهيل)
          bool hasXAxis = false;
          bool hasYAxis = false;

          // التحقق من وجود المحور الأفقي بجميع الصيغ المحتملة
          hasXAxis = availableColumns.contains(qualifiedXAxisColumn) ||
              availableColumns.contains(_xAxisColumn) ||
              (_xAxisColumn.contains('.') &&
                  availableColumns.contains(_xAxisColumn.split('.').last));

          // التحقق من وجود المحور الرأسي بجميع الصيغ المحتملة
          hasYAxis = availableColumns.contains(qualifiedYAxisColumn) ||
              availableColumns.contains(_yAxisColumn) ||
              (_yAxisColumn.contains('.') &&
                  availableColumns.contains(_yAxisColumn.split('.').last));

          debugPrint('الأعمدة المتاحة في البيانات: $availableColumns');
          debugPrint(
              'عمود المحور الأفقي الأصلي ($_xAxisColumn): ${availableColumns.contains(_xAxisColumn) ? 'موجود' : 'غير موجود'}');
          debugPrint(
              'عمود المحور الأفقي المؤهل ($qualifiedXAxisColumn): ${availableColumns.contains(qualifiedXAxisColumn) ? 'موجود' : 'غير موجود'}');
          debugPrint(
              'عمود المحور الرأسي الأصلي ($_yAxisColumn): ${availableColumns.contains(_yAxisColumn) ? 'موجود' : 'غير موجود'}');
          debugPrint(
              'عمود المحور الرأسي المؤهل ($qualifiedYAxisColumn): ${availableColumns.contains(qualifiedYAxisColumn) ? 'موجود' : 'غير موجود'}');
          debugPrint('نتيجة التحقق من المحور الأفقي: $hasXAxis');
          debugPrint('نتيجة التحقق من المحور الرأسي: $hasYAxis');

          if (!hasXAxis || !hasYAxis) {
            // إضافة رسالة خطأ إذا كانت الأعمدة غير موجودة
            data['error'] = '''تحذير: الأعمدة المطلوبة غير موجودة في البيانات
${!hasXAxis ? 'عمود المحور الأفقي ($qualifiedXAxisColumn): غير موجود' : ''}
${!hasYAxis ? 'عمود المحور الرأسي ($qualifiedYAxisColumn): غير موجود' : ''}''';
          }
        }
      }

      setState(() {
        _chartData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في تنفيذ الاستعلام: $e';
        _isLoading = false;
      });
      debugPrint('خطأ في تنفيذ الاستعلام: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const AppDrawer(),
      appBar: AppBar(
        title: const Text('تقارير ديناميكية'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
            onPressed: _loadData,
          ),
        ],
      ),
      body: SafeArea(
        child: _isLoading
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'جاري تحميل البيانات...',
                      style: TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              )
            : _errorMessage.isNotEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, color: Colors.red, size: 48),
                        SizedBox(height: 16),
                        Text(
                          _errorMessage,
                          style:
                              const TextStyle(color: Colors.red, fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: _loadData,
                          icon: const Icon(Icons.refresh),
                          label: const Text('إعادة المحاولة'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red[400],
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  )
                : _buildContent(),
      ),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildContent() {
    return Row(
      children: [
        // الجزء الأيمن: إعدادات التقرير
        Expanded(
          flex: 3,
          child: _buildReportSettings(),
        ),

        // الجزء الأيسر: عرض الرسم البياني
        Expanded(
          flex: 5,
          child: _buildChartDisplay(),
        ),
      ],
    );
  }

  /// بناء إعدادات التقرير
  Widget _buildReportSettings() {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'إعدادات التقرير',
                  style: AppStyles.titleLarge,
                ),
                Tooltip(
                  message: 'مساعدة حول إعدادات التقرير',
                  child: IconButton(
                    icon: const Icon(Icons.help_outline),
                    onPressed: () {
                      _showHelpDialog(context);
                    },
                  ),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),

            // اختيار مصدر البيانات
            _buildDataSourceSection(),
            const SizedBox(height: 24),

            // اختيار نوع الرسم البياني
            _buildChartTypeSection(),
            const SizedBox(height: 24),

            // إعدادات الرسم البياني
            if (_selectedTable.isNotEmpty && _selectedColumns.isNotEmpty)
              _buildChartSettingsSection(),
            const SizedBox(height: 24),

            // زر تنفيذ الاستعلام
            Center(
              child: ElevatedButton.icon(
                onPressed: _executeQuery,
                icon: const Icon(Icons.play_arrow),
                label: const Text('عرض التقرير'),
                style: ElevatedButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: Theme.of(context).primaryColor,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  textStyle: const TextStyle(fontSize: 18),
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// بناء قسم اختيار مصدر البيانات
  Widget _buildDataSourceSection() {
    // قائمة بأسماء الجداول المعروضة للمستخدم
    final Map<String, String> tableDisplayNames = {
      'tasks': 'المهام',
      'users': 'المستخدمين',
      'departments': 'الأقسام',
      'task_types': 'أنواع المهام',
      'task_statuses': 'حالات المهام',
      'task_priorities': 'أولويات المهام',
      'task_comments': 'تعليقات المهام',
      'task_attachments': 'مرفقات المهام',
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مصدر البيانات',
          style: AppStyles.titleMedium,
        ),
        const Divider(),
        const SizedBox(height: 16),

        // خيار استخدام جداول متعددة
        SwitchListTile(
          title: const Text('دمج بيانات من أقسام مختلفة'),
          subtitle: const Text(
              'إضافة بيانات من أقسام أخرى مثل المستخدمين، المهام، الأقسام، إلخ'),
          value: _useMultipleTables,
          onChanged: (value) {
            setState(() {
              _useMultipleTables = value;
            });
          },
        ),
        const SizedBox(height: 16),

        // اختيار الجدول الرئيسي
        DropdownButtonFormField<String>(
          decoration: const InputDecoration(
            labelText: 'اختر قسم البيانات الرئيسي',
            hintText: 'مثال: المهام، المستخدمين، الأقسام',
            border: OutlineInputBorder(),
          ),
          value: _selectedTable.isNotEmpty &&
                  _powerBIController.availableTables.contains(_selectedTable)
              ? _selectedTable
              : null,
          items: _powerBIController.availableTables.isEmpty
              ? [
                  const DropdownMenuItem<String>(
                      value: null, child: Text('لا توجد جداول متاحة'))
                ]
              : _powerBIController.availableTables.map((table) {
                  return DropdownMenuItem<String>(
                    value: table,
                    child: Text(tableDisplayNames[table] ?? table),
                  );
                }).toList(),
          onChanged: _powerBIController.availableTables.isEmpty
              ? null
              : (value) {
                  if (value != null) {
                    setState(() {
                      _selectedTable = value;
                      _selectedColumns = [];
                      _xAxisColumn = '';
                      _yAxisColumn = '';
                      _sizeColumn = '';
                      _colorColumn = '';

                      // إعادة تعيين الجداول المرتبطة
                      _relatedTables = [];
                      _joinConditions = [];
                      _joinTypes = [];
                      _suggestedRelations = [];
                    });

                    _powerBIController.loadTableColumns(_selectedTable);
                  }
                },
        ),
        const SizedBox(height: 16),

        // إضافة جداول مرتبطة إذا تم تفعيل الخيار
        if (_useMultipleTables && _selectedTable.isNotEmpty)
          _buildRelatedTablesSelector(),

        // اختيار الأعمدة
        if (_selectedTable.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildColumnSelector(),
        ],
      ],
    );
  }

  /// بناء محدد الجداول المرتبطة
  Widget _buildRelatedTablesSelector() {
    // قائمة بأسماء الجداول المعروضة للمستخدم
    final Map<String, String> tableDisplayNames = {
      'tasks': 'المهام',
      'users': 'المستخدمين',
      'departments': 'الأقسام',
      'task_types': 'أنواع المهام',
      'task_statuses': 'حالات المهام',
      'task_priorities': 'أولويات المهام',
      'task_comments': 'تعليقات المهام',
      'task_attachments': 'مرفقات المهام',
    };

    // قائمة بأنواع الربط المعروضة للمستخدم
    final Map<String, String> joinTypeDisplayNames = {
      'INNER JOIN': 'ربط البيانات المتطابقة فقط',
      'LEFT JOIN': 'عرض كل البيانات من القسم الرئيسي',
      'RIGHT JOIN': 'عرض كل البيانات من القسم المضاف',
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إضافة بيانات من أقسام أخرى:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),

        // عرض الجداول المرتبطة المضافة
        if (_relatedTables.isNotEmpty) ...[
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _relatedTables.length,
            itemBuilder: (context, index) {
              final relatedTable = _relatedTables[index];
              final displayName =
                  tableDisplayNames[relatedTable] ?? relatedTable;

              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(color: Colors.grey.shade300),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Icon(
                                _getTableIcon(relatedTable),
                                color: Colors.blue,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'بيانات من: $displayName',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            tooltip: 'إزالة هذا القسم',
                            onPressed: () {
                              setState(() {
                                // حذف الجدول المرتبط وشروط الربط المرتبطة به
                                _joinConditions.removeAt(index);
                                _joinTypes.removeAt(index);
                                _relatedTables.removeAt(index);
                              });
                            },
                          ),
                        ],
                      ),
                      const Divider(),
                      const SizedBox(height: 8),

                      // نوع الربط
                      const Text(
                        'كيفية ربط البيانات:',
                        style: TextStyle(fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'اختر طريقة الربط',
                          border: OutlineInputBorder(),
                        ),
                        value: index < _joinTypes.length
                            ? _joinTypes[index]
                            : 'INNER JOIN',
                        items: joinTypeDisplayNames.entries.map((entry) {
                          return DropdownMenuItem<String>(
                            value: entry.key,
                            child: Text(entry.value),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              if (index < _joinTypes.length) {
                                _joinTypes[index] = value;
                              } else {
                                _joinTypes.add(value);
                              }
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // زر اقتراح العلاقات
                      ElevatedButton.icon(
                        icon: const Icon(Icons.auto_fix_high),
                        label: const Text('ربط البيانات تلقائياً'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                        onPressed: () {
                          // تحميل العلاقات المقترحة وعرضها
                          _showRelationSuggestions(
                              _relatedTables[index], index);
                        },
                      ),

                      // عرض حالة الربط الحالية
                      if (index < _joinConditions.length &&
                          _joinConditions[index].isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.green.shade200),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.check_circle,
                                  color: Colors.green),
                              const SizedBox(width: 8),
                              const Expanded(
                                child: Text(
                                  'تم ربط البيانات بنجاح',
                                  style: TextStyle(color: Colors.green),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.delete,
                                    color: Colors.red, size: 16),
                                tooltip: 'إلغاء الربط',
                                onPressed: () {
                                  setState(() {
                                    _joinConditions[index] = '';
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 16),
        ],

        // زر إضافة جدول مرتبط
        _buildAddTableWidget(),
      ],
    );
  }

  /// بناء ويدجت إضافة جدول مرتبط
  Widget _buildAddTableWidget() {
    // قائمة بأسماء الجداول المعروضة للمستخدم
    final Map<String, String> tableDisplayNames = {
      'tasks': 'المهام',
      'users': 'المستخدمين',
      'departments': 'الأقسام',
      'task_types': 'أنواع المهام',
      'task_statuses': 'حالات المهام',
      'task_priorities': 'أولويات المهام',
      'task_comments': 'تعليقات المهام',
      'task_attachments': 'مرفقات المهام',
    };

    // تحضير قائمة الجداول المتاحة للإضافة
    final availableTables = _powerBIController.availableTables
        .where((table) =>
            table != _selectedTable && !_relatedTables.contains(table))
        .toList();

    // التحقق من وجود جداول متاحة للإضافة
    if (availableTables.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: const Row(
          children: [
            Icon(Icons.info, color: Colors.blue),
            SizedBox(width: 8),
            Expanded(
              child: Text('لا توجد أقسام بيانات إضافية متاحة للإضافة'),
            ),
          ],
        ),
      );
    } else {
      return DropdownButtonFormField<String>(
        decoration: const InputDecoration(
          labelText: 'إضافة بيانات من قسم آخر',
          hintText: 'اختر قسم لإضافة بياناته',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.add_circle),
        ),
        value: null,
        items: availableTables.map((table) {
          return DropdownMenuItem<String>(
            value: table,
            child: Text(tableDisplayNames[table] ?? table),
          );
        }).toList(),
        onChanged: (value) {
          if (value != null) {
            setState(() {
              _relatedTables.add(value);
              _joinConditions.add('');
              _joinTypes.add('INNER JOIN');
            });

            // تحميل أعمدة الجدول المرتبط
            _powerBIController.loadTableColumns(value);

            // تحميل العلاقات المقترحة وعرضها
            final newIndex = _relatedTables.length - 1;
            _showRelationSuggestions(value, newIndex);
          }
        },
      );
    }
  }

  /// الحصول على أيقونة مناسبة للجدول
  IconData _getTableIcon(String tableName) {
    switch (tableName) {
      case 'tasks':
        return Icons.task;
      case 'users':
        return Icons.people;
      case 'departments':
        return Icons.business;
      case 'task_types':
        return Icons.category;
      case 'task_statuses':
        return Icons.pending_actions;
      case 'task_priorities':
        return Icons.priority_high;
      case 'task_comments':
        return Icons.comment;
      case 'task_attachments':
        return Icons.attach_file;
      default:
        return Icons.table_chart;
    }
  }

  /// بناء محدد الأعمدة
  Widget _buildColumnSelector() {
    // جمع أعمدة الجدول الرئيسي والجداول المرتبطة
    List<Map<String, dynamic>> allColumns = [];
    Map<String, List<Map<String, dynamic>>> columnsByTable = {};

    // إضافة أعمدة الجدول الرئيسي
    final mainTableColumns =
        _powerBIController.tableColumns[_selectedTable] ?? [];
    if (mainTableColumns.isNotEmpty) {
      allColumns.addAll(mainTableColumns);
      columnsByTable[_selectedTable] = mainTableColumns;
    }

    // إضافة أعمدة الجداول المرتبطة
    if (_useMultipleTables) {
      for (final relatedTable in _relatedTables) {
        final relatedTableColumns =
            _powerBIController.tableColumns[relatedTable] ?? [];
        if (relatedTableColumns.isNotEmpty) {
          allColumns.addAll(relatedTableColumns);
          columnsByTable[relatedTable] = relatedTableColumns;
        }
      }
    }

    // قائمة بأسماء الأعمدة المعروضة للمستخدم
    final Map<String, String> columnDisplayNames = {
      'id': 'المعرف',
      'title': 'العنوان',
      'name': 'الاسم',
      'description': 'الوصف',
      'status': 'الحالة',
      'priority': 'الأولوية',
      'created_at': 'تاريخ الإنشاء',
      'updated_at': 'تاريخ التحديث',
      'due_date': 'تاريخ الاستحقاق',
      'completed_at': 'تاريخ الإكمال',
      'user_id': 'المستخدم',
      'department_id': 'القسم',
      'task_type_id': 'نوع المهمة',
      'task_status_id': 'حالة المهمة',
      'task_priority_id': 'أولوية المهمة',
      'assigned_to': 'مسند إلى',
      'created_by': 'أنشئت بواسطة',
      'completion_percentage': 'نسبة الإكمال',
      'email': 'البريد الإلكتروني',
      'phone': 'رقم الهاتف',
      'address': 'العنوان',
      'role': 'الدور',
      'is_active': 'نشط',
      'count': 'العدد',
      'total': 'المجموع',
      'average': 'المتوسط',
    };

    if (allColumns.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: const [
            Icon(Icons.info, color: Colors.blue),
            SizedBox(width: 8),
            Text('جاري تحميل البيانات...'),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.view_column, color: Colors.blue, size: 20),
            const SizedBox(width: 8),
            const Text(
              'اختر البيانات التي تريد عرضها:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // شرح بسيط
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade100),
          ),
          child: Row(
            children: const [
              Icon(Icons.lightbulb, color: Colors.amber),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'اختر البيانات التي تريد تضمينها في التقرير. يمكنك اختيار عدة عناصر.',
                  style: TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // عرض الأعمدة مقسمة حسب الجداول
        for (final tableEntry in columnsByTable.entries) ...[
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان الجدول
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(_getTableIcon(tableEntry.key),
                            color: Colors.blue, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'بيانات ${_getTableDisplayName(tableEntry.key)}:',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    // زر تحديد الكل للجدول الحالي
                    TextButton.icon(
                      icon: Icon(
                        _isAllColumnsSelectedForTable(tableEntry.value)
                            ? Icons.deselect
                            : Icons.select_all,
                        size: 18,
                      ),
                      label: Text(
                        _isAllColumnsSelectedForTable(tableEntry.value)
                            ? 'إلغاء تحديد الكل'
                            : 'تحديد الكل',
                      ),
                      onPressed: () {
                        setState(() {
                          final tableColumns = tableEntry.value
                              .map((col) => col['name'] as String)
                              .toList();
                          if (_isAllColumnsSelectedForTable(tableEntry.value)) {
                            // إلغاء تحديد كل أعمدة هذا الجدول
                            _selectedColumns.removeWhere(
                                (col) => tableColumns.contains(col));

                            // إعادة تعيين أعمدة الرسم البياني إذا كانت من هذا الجدول
                            if (tableColumns.contains(_xAxisColumn)) {
                              _xAxisColumn = '';
                            }
                            if (tableColumns.contains(_yAxisColumn)) {
                              _yAxisColumn = '';
                            }
                            if (tableColumns.contains(_sizeColumn)) {
                              _sizeColumn = '';
                            }
                            if (tableColumns.contains(_colorColumn)) {
                              _colorColumn = '';
                            }
                          } else {
                            // تحديد كل أعمدة هذا الجدول
                            for (final col in tableColumns) {
                              if (!_selectedColumns.contains(col)) {
                                _selectedColumns.add(col);
                              }
                            }
                          }
                        });
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // عرض أعمدة الجدول
                Wrap(
                  spacing: 10,
                  runSpacing: 10,
                  children: tableEntry.value.map((column) {
                    final columnName = column['name'] as String;
                    final fullColumnName = _useMultipleTables
                        ? '${tableEntry.key}.$columnName'
                        : columnName;
                    final displayName =
                        columnDisplayNames[columnName] ?? columnName;
                    final isSelected = _selectedColumns.contains(columnName);

                    // تحديد لون ورمز مناسب للعمود
                    Color chipColor;
                    IconData chipIcon;

                    if (columnName.contains('date') ||
                        columnName.contains('_at') ||
                        columnName.contains('time')) {
                      chipColor = Colors.purple;
                      chipIcon = Icons.calendar_today;
                    } else if (columnName.contains('id')) {
                      chipColor = Colors.grey;
                      chipIcon = Icons.key;
                    } else if (columnName.contains('name') ||
                        columnName.contains('title') ||
                        columnName.contains('description')) {
                      chipColor = Colors.green;
                      chipIcon = Icons.text_fields;
                    } else if (columnName.contains('count') ||
                        columnName.contains('total') ||
                        columnName.contains('sum') ||
                        columnName.contains('average') ||
                        columnName.contains('percentage')) {
                      chipColor = Colors.orange;
                      chipIcon = Icons.numbers;
                    } else if (columnName.contains('status') ||
                        columnName.contains('priority') ||
                        columnName.contains('type')) {
                      chipColor = Colors.blue;
                      chipIcon = Icons.category;
                    } else if (columnName.contains('user') ||
                        columnName.contains('assigned') ||
                        columnName.contains('created_by')) {
                      chipColor = Colors.teal;
                      chipIcon = Icons.person;
                    } else {
                      chipColor = Colors.blueGrey;
                      chipIcon = Icons.data_array;
                    }

                    return Tooltip(
                      message: fullColumnName,
                      child: FilterChip(
                        label: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(chipIcon, size: 16, color: chipColor),
                            const SizedBox(width: 4),
                            Text(displayName),
                          ],
                        ),
                        selected: isSelected,
                        selectedColor: Color.fromARGB(51, chipColor.r.toInt(),
                            chipColor.g.toInt(), chipColor.b.toInt()),
                        checkmarkColor: chipColor,
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _selectedColumns.add(columnName);
                            } else {
                              _selectedColumns.remove(columnName);

                              // إذا تم إلغاء تحديد عمود مستخدم في الرسم البياني، إعادة تعيينه
                              if (_xAxisColumn == columnName) {
                                _xAxisColumn = '';
                              }
                              if (_yAxisColumn == columnName) {
                                _yAxisColumn = '';
                              }
                              if (_sizeColumn == columnName) {
                                _sizeColumn = '';
                              }
                              if (_colorColumn == columnName) {
                                _colorColumn = '';
                              }
                            }
                          });
                        },
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],

        // عرض الأعمدة المحددة
        if (_selectedColumns.isNotEmpty) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.green),
                    const SizedBox(width: 8),
                    Text(
                      'تم اختيار ${_selectedColumns.length} عنصر',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// بناء قسم اختيار نوع الرسم البياني
  Widget _buildChartTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع الرسم البياني',
          style: AppStyles.titleMedium,
        ),
        const Divider(),
        const SizedBox(height: 16),

        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildChartTypeOption(
                PowerBIChartType.bar, Icons.bar_chart, 'شريطي'),
            _buildChartTypeOption(
                PowerBIChartType.line, Icons.show_chart, 'خطي'),
            _buildChartTypeOption(
                PowerBIChartType.pie, Icons.pie_chart, 'دائري'),
            _buildChartTypeOption(
                PowerBIChartType.scatter, Icons.scatter_plot, 'نقطي'),
            _buildChartTypeOption(
                PowerBIChartType.bubble, Icons.bubble_chart, 'فقاعي'),
            _buildChartTypeOption(
                PowerBIChartType.table, Icons.table_chart, 'جدول'),
          ],
        ),

        const SizedBox(height: 16),

        // شرح نوع الرسم البياني
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade100),
          ),
          child: Row(
            children: [
              const Icon(Icons.lightbulb, color: Colors.amber),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _getChartTypeDescription(_selectedChartType),
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء خيار نوع الرسم البياني
  Widget _buildChartTypeOption(
      PowerBIChartType type, IconData icon, String label) {
    final isSelected = _selectedChartType == type;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedChartType = type;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor.withAlpha(51)
              : null, // 0.2 * 255 = 51
          border: Border.all(
            color: isSelected ? Theme.of(context).primaryColor : Colors.grey,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? Theme.of(context).primaryColor : Colors.grey,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Theme.of(context).primaryColor : null,
                fontWeight: isSelected ? FontWeight.bold : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على وصف لنوع الرسم البياني
  String _getChartTypeDescription(PowerBIChartType chartType) {
    switch (chartType) {
      case PowerBIChartType.bar:
        return 'الرسم البياني الشريطي مناسب لمقارنة القيم بين فئات مختلفة، مثل عدد المهام لكل قسم.';
      case PowerBIChartType.line:
        return 'الرسم البياني الخطي مناسب لعرض البيانات على مدار الزمن، مثل تطور عدد المهام المكتملة شهريًا.';
      case PowerBIChartType.pie:
        return 'الرسم البياني الدائري مناسب لعرض النسب المئوية، مثل توزيع المهام حسب الحالة.';
      case PowerBIChartType.scatter:
        return 'الرسم البياني النقطي مناسب لعرض العلاقة بين متغيرين، مثل العلاقة بين مدة المهمة ونسبة إكمالها.';
      case PowerBIChartType.bubble:
        return 'الرسم البياني الفقاعي مناسب لعرض ثلاثة متغيرات، حيث يمثل حجم الفقاعة المتغير الثالث.';
      case PowerBIChartType.table:
        return 'جدول البيانات مناسب لعرض البيانات التفصيلية بشكل مباشر.';
      default:
        return 'اختر نوع الرسم البياني المناسب لبياناتك.';
    }
  }

  /// الحصول على اسم دالة التجميع
  String getAggregateFunctionName(AggregateFunction function) {
    switch (function) {
      case AggregateFunction.none:
        return 'بدون تجميع';
      case AggregateFunction.count:
        return 'العدد (COUNT)';
      case AggregateFunction.sum:
        return 'المجموع (SUM)';
      case AggregateFunction.avg:
        return 'المتوسط (AVG)';
      case AggregateFunction.min:
        return 'الحد الأدنى (MIN)';
      case AggregateFunction.max:
        return 'الحد الأقصى (MAX)';
      default:
        return 'بدون تجميع';
    }
  }

  /// الحصول على وصف دالة التجميع
  String getAggregateFunctionDescription(AggregateFunction function) {
    switch (function) {
      case AggregateFunction.none:
        return 'عرض البيانات كما هي بدون تجميع';
      case AggregateFunction.count:
        return 'حساب عدد العناصر في كل مجموعة';
      case AggregateFunction.sum:
        return 'حساب مجموع القيم في كل مجموعة';
      case AggregateFunction.avg:
        return 'حساب متوسط القيم في كل مجموعة';
      case AggregateFunction.min:
        return 'إيجاد أصغر قيمة في كل مجموعة';
      case AggregateFunction.max:
        return 'إيجاد أكبر قيمة في كل مجموعة';
      default:
        return 'عرض البيانات كما هي بدون تجميع';
    }
  }

  /// بناء قسم تصفية التاريخ
  Widget _buildDateFilterSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تصفية حسب التاريخ',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),

        // اختيار حقل التاريخ
        DropdownButtonFormField<String>(
          decoration: const InputDecoration(
            labelText: 'حقل التاريخ',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          ),
          value: _dateField,
          items: [
            const DropdownMenuItem<String>(
              value: 'createdAt',
              child: Text('تاريخ الإنشاء'),
            ),
            const DropdownMenuItem<String>(
              value: 'updatedAt',
              child: Text('تاريخ التحديث'),
            ),
            const DropdownMenuItem<String>(
              value: 'completedAt',
              child: Text('تاريخ الإكمال'),
            ),
            const DropdownMenuItem<String>(
              value: 'dueDate',
              child: Text('تاريخ الاستحقاق'),
            ),
            const DropdownMenuItem<String>(
              value: 'startDate',
              child: Text('تاريخ البدء'),
            ),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _dateField = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),

        // اختيار نوع الفترة الزمنية
        DropdownButtonFormField<TimeFilterType>(
          decoration: const InputDecoration(
            labelText: 'الفترة الزمنية',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          ),
          value: _selectedFilterType,
          items: TimeFilterType.values.map((type) {
            String label;
            switch (type) {
              case TimeFilterType.day:
                label = 'اليوم';
                break;
              case TimeFilterType.week:
                label = 'الأسبوع';
                break;
              case TimeFilterType.month:
                label = 'الشهر';
                break;
              case TimeFilterType.quarter:
                label = 'الربع';
                break;
              case TimeFilterType.year:
                label = 'السنة';
                break;
              case TimeFilterType.custom:
                label = 'مخصص';
                break;
              case TimeFilterType.all:
                label = 'الكل';
                break;
            }
            return DropdownMenuItem<TimeFilterType>(
              value: type,
              child: Text(label),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedFilterType = value;
                _updateDateRange(value);
              });
            }
          },
        ),
        const SizedBox(height: 16),

        // عرض الفترة المحددة إذا كانت مخصصة
        if (_selectedFilterType == TimeFilterType.custom) ...[
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () => _selectDate(true),
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'تاريخ البداية',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                    ),
                    child: Text(
                      _startDate != null
                          ? DateFormat('yyyy-MM-dd').format(_startDate!)
                          : 'اختر التاريخ',
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: InkWell(
                  onTap: () => _selectDate(false),
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'تاريخ النهاية',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                    ),
                    child: Text(
                      _endDate != null
                          ? DateFormat('yyyy-MM-dd').format(_endDate!)
                          : 'اختر التاريخ',
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],

        // عرض الفترة المحددة
        if (_startDate != null &&
            _endDate != null &&
            _selectedFilterType != TimeFilterType.custom) ...[
          const SizedBox(height: 8),
          Text(
            'الفترة: ${DateFormat('yyyy-MM-dd').format(_startDate!)} إلى ${DateFormat('yyyy-MM-dd').format(_endDate!)}',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ],
    );
  }

  /// تحديث نطاق التاريخ بناءً على نوع الفلتر
  void _updateDateRange(TimeFilterType filterType) {
    final now = DateTime.now();

    switch (filterType) {
      case TimeFilterType.day:
        _startDate = DateTime(now.year, now.month, now.day);
        _endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      case TimeFilterType.week:
        // الحصول على بداية الأسبوع (الأحد)
        final firstDayOfWeek = now.subtract(Duration(days: now.weekday % 7));
        _startDate = DateTime(
            firstDayOfWeek.year, firstDayOfWeek.month, firstDayOfWeek.day);
        _endDate = _startDate!
            .add(const Duration(days: 6, hours: 23, minutes: 59, seconds: 59));
        break;
      case TimeFilterType.month:
        _startDate = DateTime(now.year, now.month, 1);
        _endDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
        break;
      case TimeFilterType.quarter:
        final quarterMonth = (now.month - 1) ~/ 3 * 3 + 1;
        _startDate = DateTime(now.year, quarterMonth, 1);
        _endDate = DateTime(now.year, quarterMonth + 3, 0, 23, 59, 59);
        break;
      case TimeFilterType.year:
        _startDate = DateTime(now.year, 1, 1);
        _endDate = DateTime(now.year, 12, 31, 23, 59, 59);
        break;
      case TimeFilterType.all:
        _startDate = null;
        _endDate = null;
        break;
      case TimeFilterType.custom:
        // لا تغير التواريخ للتصفية المخصصة
        _startDate ??= DateTime(now.year, now.month, 1);
        _endDate ??= now;
        break;
    }
  }

  /// اختيار التاريخ
  Future<void> _selectDate(bool isStartDate) async {
    final initialDate =
        isStartDate ? _startDate ?? DateTime.now() : _endDate ?? DateTime.now();

    final date = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );

    if (date != null) {
      setState(() {
        if (isStartDate) {
          _startDate = date;
        } else {
          _endDate = date;
        }
      });
    }
  }

  /// بناء قسم إعدادات الرسم البياني
  Widget _buildChartSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إعدادات الرسم البياني',
          style: AppStyles.titleMedium,
        ),
        const Divider(),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // المحور الأفقي
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Text(
                        'X',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'المحور الأفقي:',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'اختر البيانات التي ستظهر على المحور الأفقي (X)',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'اختر البيانات',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 12),
                          ),
                          value: _xAxisColumn.isNotEmpty ? _xAxisColumn : null,
                          items: _selectedColumns.isEmpty
                              ? [
                                  const DropdownMenuItem<String>(
                                      value: null,
                                      child: Text('لا توجد بيانات متاحة'))
                                ]
                              : _getAllColumnsWithTablePrefix()
                                  .map((columnInfo) {
                                  final displayName =
                                      columnInfo['displayName'] as String;
                                  final fullColumnName =
                                      columnInfo['fullColumnName'] as String;
                                  return DropdownMenuItem<String>(
                                    value: fullColumnName,
                                    child: Text(displayName),
                                  );
                                }).toList(),
                          onChanged: _selectedColumns.isEmpty
                              ? null
                              : (value) {
                                  if (value != null) {
                                    setState(() {
                                      _xAxisColumn = value;
                                    });
                                  }
                                },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى اختيار بيانات المحور الأفقي';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // المحور الرأسي
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Text(
                        'Y',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'المحور الرأسي:',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'اختر البيانات التي ستظهر على المحور الرأسي (Y)',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        // اختيار دالة التجميع للمحور الرأسي
                        DropdownButtonFormField<AggregateFunction>(
                          decoration: const InputDecoration(
                            labelText: 'دالة التجميع',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                          ),
                          value: _yAxisAggregateFunction,
                          items: AggregateFunction.values.map((function) {
                            return DropdownMenuItem<AggregateFunction>(
                              value: function,
                              child: Text(getAggregateFunctionName(function)),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _yAxisAggregateFunction = value;
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 4),
                        Text(
                          getAggregateFunctionDescription(
                              _yAxisAggregateFunction),
                          style:
                              const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'اختر البيانات',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 12),
                          ),
                          value: _yAxisColumn.isNotEmpty ? _yAxisColumn : null,
                          items: _selectedColumns.isEmpty
                              ? [
                                  const DropdownMenuItem<String>(
                                      value: null,
                                      child: Text('لا توجد بيانات متاحة'))
                                ]
                              : _getAllColumnsWithTablePrefix()
                                  .map((columnInfo) {
                                  final displayName =
                                      columnInfo['displayName'] as String;
                                  final fullColumnName =
                                      columnInfo['fullColumnName'] as String;
                                  return DropdownMenuItem<String>(
                                    value: fullColumnName,
                                    child: Text(displayName),
                                  );
                                }).toList(),
                          onChanged: _selectedColumns.isEmpty
                              ? null
                              : (value) {
                                  if (value != null) {
                                    setState(() {
                                      _yAxisColumn = value;
                                    });
                                  }
                                },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى اختيار بيانات المحور الرأسي';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // عمود الحجم (للرسوم البيانية الفقاعية)
              if (_selectedChartType == PowerBIChartType.bubble) ...[
                const SizedBox(height: 20),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.bubble_chart,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'حجم الفقاعات:',
                            style: TextStyle(fontWeight: FontWeight.w500),
                          ),
                          const SizedBox(height: 4),
                          const Text(
                            'اختر البيانات التي ستحدد حجم الفقاعات في الرسم البياني',
                            style: TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                          const SizedBox(height: 8),
                          DropdownButtonFormField<String>(
                            decoration: const InputDecoration(
                              labelText: 'اختر البيانات',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 12),
                            ),
                            value: _sizeColumn.isEmpty ? null : _sizeColumn,
                            items: [
                              const DropdownMenuItem<String>(
                                value: null,
                                child: Text('بدون'),
                              ),
                              ..._getAllColumnsWithTablePrefix()
                                  .map((columnInfo) {
                                final displayName =
                                    columnInfo['displayName'] as String;
                                final fullColumnName =
                                    columnInfo['fullColumnName'] as String;
                                return DropdownMenuItem<String>(
                                  value: fullColumnName,
                                  child: Text(displayName),
                                );
                              }),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _sizeColumn = value ?? '';
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],

              // عمود اللون (للتصنيف)
              const SizedBox(height: 20),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.purple,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.color_lens,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'تصنيف البيانات بالألوان:',
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'اختر البيانات التي ستستخدم لتصنيف وتلوين العناصر في الرسم البياني',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'اختر البيانات',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 12),
                          ),
                          value: _colorColumn.isEmpty ? null : _colorColumn,
                          items: [
                            const DropdownMenuItem<String>(
                              value: null,
                              child: Text('بدون'),
                            ),
                            ..._getAllColumnsWithTablePrefix()
                                .map((columnInfo) {
                              final displayName =
                                  columnInfo['displayName'] as String;
                              final fullColumnName =
                                  columnInfo['fullColumnName'] as String;
                              return DropdownMenuItem<String>(
                                value: fullColumnName,
                                child: Text(displayName),
                              );
                            }),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _colorColumn = value ?? '';
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // تصفية التاريخ
              const SizedBox(height: 20),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.purple,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.date_range,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildDateFilterSection(),
                  ),
                ],
              ),

              // معايير التصفية
              const SizedBox(height: 20),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.filter_alt,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Text(
                              'معايير التصفية:',
                              style: TextStyle(fontWeight: FontWeight.w500),
                            ),
                            const SizedBox(width: 8),
                            Tooltip(
                              message:
                                  'استخدم شروط SQL لتصفية البيانات (مثال: status = 1 AND priority > 2)',
                              child: const Icon(Icons.info_outline,
                                  size: 16, color: Colors.blue),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'أدخل شروط تصفية البيانات (اختياري)',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          decoration: InputDecoration(
                            labelText: 'معايير التصفية (SQL WHERE)',
                            border: const OutlineInputBorder(),
                            hintText:
                                'مثال: column1 > 10 AND column2 = "value"',
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 12),
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.help_outline),
                              onPressed: () => _showFilterHelpDialog(context),
                              tooltip: 'مساعدة حول معايير التصفية',
                            ),
                          ),
                          maxLines: 3,
                          onChanged: (value) {
                            setState(() {
                              _filterCriteria = value;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // زر تنفيذ الاستعلام
              const SizedBox(height: 30),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.play_arrow, size: 28),
                  label: const Text(
                    'تنفيذ الاستعلام وعرض التقرير',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    elevation: 4,
                    shadowColor: const Color.fromRGBO(76, 175, 80, 0.5),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: _executeQuery,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء عرض الرسم البياني
  Widget _buildChartDisplay() {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'عرض التقرير',
              style: AppStyles.titleLarge,
            ),
            const Divider(),
            const SizedBox(height: 16),

            // استخدام Expanded لمنع مشكلة تجاوز الحجم
            Expanded(
              child: _chartData.isEmpty
                  ? _buildEmptyChartView()
                  : _errorMessage.isNotEmpty
                      ? _buildErrorView()
                      : _buildChartAndDataView(),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض عندما لا توجد بيانات
  Widget _buildEmptyChartView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bar_chart,
            size: 80,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            'اختر البيانات وانقر على "عرض التقرير" لإنشاء الرسم البياني',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// عرض عندما يوجد خطأ
  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// عرض الرسم البياني والبيانات
  Widget _buildChartAndDataView() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // تقسيم المساحة المتاحة بين الرسم البياني والجدول
        final availableHeight = constraints.maxHeight;
        final chartHeight = availableHeight * 0.5; // 50% للرسم البياني
        final tableHeight = availableHeight * 0.5; // 50% للجدول

        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عرض الرسم البياني
              SizedBox(
                height: chartHeight,
                child: _buildChart(),
              ),

              // عرض البيانات في جدول
              const SizedBox(height: 16),
              const Text(
                'بيانات التقرير',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Divider(),
              const SizedBox(height: 8),
              SizedBox(
                height: tableHeight,
                child: _buildDataTable(),
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء الرسم البياني
  Widget _buildChart() {
    // التحقق من وجود بيانات
    if (_chartData.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bar_chart_outlined, color: Colors.grey, size: 48),
            const SizedBox(height: 16),
            const Text(
              'لا توجد بيانات للرسم البياني',
              style: TextStyle(fontSize: 16, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'تأكد من اختيار البيانات المناسبة وتنفيذ الاستعلام',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // التحقق من وجود رسالة خطأ
    if (_chartData.containsKey('error')) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.orange, size: 48),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل البيانات:',
              style: const TextStyle(
                  color: Colors.orange,
                  fontSize: 18,
                  fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color.fromRGBO(255, 152, 0, 0.1),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: const Color.fromRGBO(255, 152, 0, 0.5)),
              ),
              child: Text(
                '${_chartData['error']}',
                style: const TextStyle(color: Color(0xFFE65100), fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: _executeQuery,
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة المحاولة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                OutlinedButton.icon(
                  onPressed: () {
                    // عرض مربع حوار المساعدة مع اقتراحات لحل المشكلة
                    _showFixSuggestionsDialog(context);
                  },
                  icon: const Icon(Icons.help_outline),
                  label: const Text('مساعدة'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    switch (_selectedChartType) {
      case PowerBIChartType.bar:
        final data = _chartData['chartData'] as Map<String, double>? ?? {};
        if (data.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.bar_chart_outlined, color: Colors.grey, size: 48),
                const SizedBox(height: 16),
                const Text(
                  'لا توجد بيانات كافية للرسم البياني الشريطي',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'تأكد من اختيار الأعمدة المناسبة وتوفر البيانات',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        // استخدام مكون الرسم البياني المحسن
        return Padding(
          padding: const EdgeInsets.all(16),
          child: EnhancedBarChart(
            data: data,
            title: 'تقرير ديناميكي',
            xAxisTitle: _getDisplayColumnName(_xAxisColumn),
            yAxisTitle: _getDisplayColumnName(_yAxisColumn),
            showGrid: true,
            showValues: true,
            chartType: ChartType.bar,
            advancedFilterOptions: const AdvancedFilterOptions(),
          ),
        );

      case PowerBIChartType.line:
        // تحويل البيانات إلى التنسيق المطلوب لـ Syncfusion Charts
        final rawData = _chartData['chartData'] as Map<String, dynamic>? ?? {};
        final Map<String, Map<String, double>> data = {};

        // تحويل البيانات من fl_chart format إلى Syncfusion format
        for (final entry in rawData.entries) {
          if (entry.value is List) {
            final Map<String, double> seriesData = {};
            final List<dynamic> spots = entry.value;

            for (int i = 0; i < spots.length; i++) {
              if (spots[i] is Map && spots[i].containsKey('x') && spots[i].containsKey('y')) {
                seriesData[spots[i]['x'].toString()] = spots[i]['y'].toDouble();
              } else {
                seriesData[i.toString()] = spots[i].toDouble();
              }
            }

            data[entry.key] = seriesData;
          }
        }
        if (data.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.show_chart, color: Colors.grey, size: 48),
                const SizedBox(height: 16),
                const Text(
                  'لا توجد بيانات كافية للرسم البياني الخطي',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'تأكد من اختيار الأعمدة المناسبة وتوفر البيانات الرقمية',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        // استخدام مكون الرسم البياني المحسن
        return Padding(
          padding: const EdgeInsets.all(16),
          child: EnhancedLineChart(
            data: data,
            title: 'تقرير ديناميكي',
            xAxisTitle: _getDisplayColumnName(_xAxisColumn),
            yAxisTitle: _getDisplayColumnName(_yAxisColumn),
            showGrid: true,
            showDots: true,
            showBelowArea: true,
            chartType: ChartType.line,
            advancedFilterOptions: const AdvancedFilterOptions(),
          ),
        );

      case PowerBIChartType.pie:
        final data = _chartData['chartData'] as Map<String, double>? ?? {};
        if (data.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.pie_chart_outline, color: Colors.grey, size: 48),
                const SizedBox(height: 16),
                const Text(
                  'لا توجد بيانات كافية للرسم البياني الدائري',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'تأكد من اختيار الأعمدة المناسبة وتوفر البيانات الرقمية الموجبة',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        // استخدام مكون الرسم البياني المحسن
        return Padding(
          padding: const EdgeInsets.all(16),
          child: EnhancedPieChart(
            data: data,
            title: 'تقرير ديناميكي',
            showValues: true,
            showLegend: true,
            chartType: ChartType.pie,
            advancedFilterOptions: const AdvancedFilterOptions(),
          ),
        );

      case PowerBIChartType.scatter:
      case PowerBIChartType.bubble:
        // تحويل البيانات إلى التنسيق المطلوب لـ Syncfusion Scatter Chart
        final rawSpots = _chartData['spots'] as List<dynamic>? ?? [];
        final List<ScatterData> scatterData = [];

        for (final spot in rawSpots) {
          if (spot is Map && spot.containsKey('x') && spot.containsKey('y')) {
            scatterData.add(ScatterData(
              spot['x'].toDouble(),
              spot['y'].toDouble(),
            ));
          }
        }
        if (scatterData.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                    _selectedChartType == PowerBIChartType.scatter
                        ? Icons.scatter_plot
                        : Icons.bubble_chart,
                    color: Colors.grey,
                    size: 48),
                const SizedBox(height: 16),
                Text(
                  _selectedChartType == PowerBIChartType.scatter
                      ? 'لا توجد بيانات كافية للرسم البياني النقطي'
                      : 'لا توجد بيانات كافية للرسم البياني الفقاعي',
                  style: const TextStyle(fontSize: 16, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'تأكد من اختيار الأعمدة المناسبة للمحورين X و Y وتوفر البيانات الرقمية',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        // استخدام مكون الرسم البياني النقطي من Syncfusion
        return Padding(
          padding: const EdgeInsets.all(16),
          child: SfCartesianChart(
            primaryXAxis: NumericAxis(
              title: AxisTitle(text: _getDisplayColumnName(_xAxisColumn)),
            ),
            primaryYAxis: NumericAxis(
              title: AxisTitle(text: _getDisplayColumnName(_yAxisColumn)),
            ),
            series: <CartesianSeries>[
              ScatterSeries<ScatterData, double>(
                dataSource: scatterData,
                xValueMapper: (ScatterData data, _) => data.x,
                yValueMapper: (ScatterData data, _) => data.y,
                color: Colors.blue,
                markerSettings: const MarkerSettings(
                  isVisible: true,
                  height: 8,
                  width: 8,
                ),
              ),
            ],
            tooltipBehavior: TooltipBehavior(enable: true),
            zoomPanBehavior: ZoomPanBehavior(
              enablePinching: true,
              enablePanning: true,
              enableDoubleTapZooming: true,
            ),
          ),
        );

      case PowerBIChartType.table:
        return _buildDataTable();

      default:
        return const Center(
          child: Text('نوع الرسم البياني غير مدعوم'),
        );
    }
  }

  /// بناء جدول البيانات
  Widget _buildDataTable() {
    // التعامل مع أنواع البيانات المختلفة
    List<Map<String, dynamic>> data = [];

    try {
      final rawData = _chartData['data'];

      if (rawData is List<Map<String, dynamic>>) {
        data = rawData;
      } else if (rawData is List) {
        // تحويل List<dynamic> إلى List<Map<String, dynamic>>
        data = rawData.map((item) {
          if (item is Map<String, dynamic>) {
            return item;
          } else if (item is Map) {
            // تحويل Map<dynamic, dynamic> إلى Map<String, dynamic>
            return item.map((key, value) => MapEntry(key.toString(), value));
          }
          return <String, dynamic>{};
        }).toList();
      }
    } catch (e) {
      debugPrint('خطأ في معالجة بيانات الجدول: $e');
    }

    if (data.isEmpty) {
      return Center(
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.info_outline, color: Colors.blue.shade300, size: 48),
              const SizedBox(height: 16),
              const Text(
                'لا توجد بيانات متاحة',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'جرب تغيير معايير التصفية أو اختيار جدول بيانات آخر',
                style: TextStyle(fontSize: 14, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // طباعة عينة من البيانات للتصحيح
    debugPrint('تم استلام ${data.length} صفوف من البيانات');
    if (data.isNotEmpty) {
      debugPrint('نموذج البيانات: ${data.first}');
    }

    // تحديد الأعمدة التي سيتم عرضها
    List<String> displayColumns = [];

    // إذا كانت قائمة الأعمدة المحددة فارغة، استخدم جميع المفاتيح المتاحة في البيانات
    if (_selectedColumns.isEmpty) {
      if (data.isNotEmpty) {
        displayColumns = data.first.keys.toList();
      }
    } else {
      // استخدم الأعمدة المحددة مع مراعاة أسماء الجداول
      for (final column in _selectedColumns) {
        // التحقق مما إذا كان العمود يحتوي بالفعل على اسم الجدول
        if (!column.contains('.')) {
          // البحث عن العمود في البيانات (قد يكون مؤهلاً بالجدول)
          final qualifiedColumn = '$_selectedTable.$column';
          if (data.isNotEmpty && data.first.containsKey(qualifiedColumn)) {
            displayColumns.add(qualifiedColumn);
          } else if (data.isNotEmpty && data.first.containsKey(column)) {
            displayColumns.add(column);
          }
        } else {
          // العمود مؤهل بالفعل، تحقق من وجوده في البيانات
          if (data.isNotEmpty && data.first.containsKey(column)) {
            displayColumns.add(column);
          }
        }
      }
    }

    // إذا كانت قائمة الأعمدة لا تزال فارغة، أضف جميع الأعمدة المتاحة
    if (displayColumns.isEmpty && data.isNotEmpty) {
      displayColumns = data.first.keys.toList();
    }

    // إذا كانت قائمة الأعمدة لا تزال فارغة، أضف عمودًا افتراضيًا
    if (displayColumns.isEmpty) {
      displayColumns = ['البيانات'];
    }

    // طباعة الأعمدة المعروضة للتصحيح
    debugPrint('الأعمدة المعروضة: $displayColumns');

    // إنشاء أعمدة الجدول مع أسماء عرض أفضل
    final columns = displayColumns.map((column) {
      // استخراج اسم العمود بدون اسم الجدول للعرض
      String displayName = column;
      if (column.contains('.')) {
        final parts = column.split('.');
        if (parts.length > 1) {
          displayName = parts[1]; // استخدام اسم العمود فقط للعرض
        }
      }

      // تحسين اسم العرض باستخدام أسماء العرض المعرفة
      final Map<String, String> columnDisplayNames = {
        'id': 'المعرف',
        'title': 'العنوان',
        'name': 'الاسم',
        'description': 'الوصف',
        'status': 'الحالة',
        'priority': 'الأولوية',
        'created_at': 'تاريخ الإنشاء',
        'updated_at': 'تاريخ التحديث',
        'due_date': 'تاريخ الاستحقاق',
        'completed_at': 'تاريخ الإكمال',
        'user_id': 'المستخدم',
        'department_id': 'القسم',
        'count': 'العدد',
      };

      final betterDisplayName = columnDisplayNames[displayName] ?? displayName;

      return DataColumn(
        label: Text(
          betterDisplayName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      );
    }).toList();

    // إنشاء صفوف الجدول
    List<DataRow> rows = [];

    if (displayColumns.length == 1 && displayColumns[0] == 'البيانات') {
      // إذا كان هناك عمود افتراضي واحد فقط، اعرض جميع البيانات في عمود واحد
      rows = data.map((row) {
        return DataRow(
          cells: [
            DataCell(
              Text(row.toString()),
            ),
          ],
        );
      }).toList();
    } else {
      // إنشاء صفوف الجدول باستخدام الأعمدة المحددة
      rows = data.map((row) {
        return DataRow(
          cells: displayColumns.map((column) {
            final value = row[column];
            String displayValue = '';

            // التعامل مع القيم الفارغة
            if (value == null) {
              return DataCell(
                Text(
                  '-',
                  style: TextStyle(
                    color: Colors.grey.shade400,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              );
            }

            // تنسيق القيم حسب نوعها
            if (value is DateTime) {
              displayValue = DateFormat('yyyy-MM-dd HH:mm').format(value);
            } else if (value is double) {
              displayValue = value.toStringAsFixed(2);
            } else if (value is int && column.toLowerCase().contains('id')) {
              // تنسيق المعرفات بشكل أفضل
              displayValue = '#$value';
            } else {
              displayValue = value.toString();
            }

            // التعامل مع القيم الفارغة بعد التحويل
            if (displayValue.isEmpty) {
              displayValue = '-';
            }

            return DataCell(
              Text(
                displayValue,
                style: TextStyle(
                  color: _getCellTextColor(column, value),
                  fontWeight: column.toLowerCase().contains('id')
                      ? FontWeight.w500
                      : null,
                ),
              ),
            );
          }).toList(),
        );
      }).toList();
    }

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // تقليل حجم العمود إلى الحد الأدنى
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'بيانات التقرير',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${data.length} سجل',
                  style: TextStyle(color: Colors.grey.shade600),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 8),
            // استخدام Expanded داخل SizedBox محدد الارتفاع لمنع تجاوز الحجم
            Flexible(
              child: SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: DataTable(
                    headingRowColor:
                        WidgetStateProperty.all(Colors.grey.shade100),
                    dataRowMinHeight: 48,
                    dataRowMaxHeight: 64,
                    columnSpacing: 24,
                    horizontalMargin: 16,
                    showCheckboxColumn: false,
                    columns: columns,
                    rows: rows.isEmpty
                        ? [
                            DataRow(
                                cells: List.generate(
                                    columns.length,
                                    (index) =>
                                        const DataCell(Text('لا توجد بيانات'))))
                          ]
                        : rows,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على لون النص للخلية بناءً على نوع العمود والقيمة
  Color? _getCellTextColor(String column, dynamic value) {
    // تلوين الحالة
    if (column.toLowerCase().contains('status') ||
        column.toLowerCase().contains('حالة')) {
      if (value == 0 ||
          value == '0' ||
          value.toString().toLowerCase() == 'pending' ||
          value.toString() == 'قيد الانتظار') {
        return Colors.orange[700]; // قيد الانتظار
      } else if (value == 1 ||
          value == '1' ||
          value.toString().toLowerCase() == 'in_progress' ||
          value.toString() == 'قيد التنفيذ') {
        return Colors.blue[700]; // قيد التنفيذ
      } else if (value == 2 ||
          value == '2' ||
          value.toString().toLowerCase() == 'completed' ||
          value.toString() == 'مكتمل') {
        return Colors.green[700]; // مكتمل
      } else if (value == 3 ||
          value == '3' ||
          value.toString().toLowerCase() == 'cancelled' ||
          value.toString() == 'ملغي') {
        return Colors.red[700]; // ملغي
      }
    }

    // تلوين الأولوية
    if (column.toLowerCase().contains('priority') ||
        column.toLowerCase().contains('أولوية')) {
      if (value == 0 ||
          value == '0' ||
          value.toString().toLowerCase() == 'low' ||
          value.toString() == 'منخفضة') {
        return Colors.green[700]; // منخفضة
      } else if (value == 1 ||
          value == '1' ||
          value.toString().toLowerCase() == 'medium' ||
          value.toString() == 'متوسطة') {
        return Colors.orange[700]; // متوسطة
      } else if (value == 2 ||
          value == '2' ||
          value.toString().toLowerCase() == 'high' ||
          value.toString() == 'عالية') {
        return Colors.red[700]; // عالية
      }
    }

    // تلوين المعرفات
    if (column.toLowerCase().contains('id')) {
      return Colors.blue[800];
    }

    // تلوين التواريخ
    if (column.toLowerCase().contains('date') ||
        column.toLowerCase().contains('تاريخ') ||
        column.toLowerCase().contains('createdAt') ||
        column.toLowerCase().contains('updatedAt')) {
      return Colors.purple[700];
    }

    // القيمة الافتراضية
    return null;
  }

  /// التحقق مما إذا كانت جميع أعمدة الجدول محددة
  bool _isAllColumnsSelectedForTable(List<Map<String, dynamic>> tableColumns) {
    final tableColumnNames =
        tableColumns.map((col) => col['name'] as String).toList();
    return tableColumnNames.every((col) => _selectedColumns.contains(col));
  }

  /// الحصول على اسم العرض للجدول
  String _getTableDisplayName(String tableName) {
    final Map<String, String> tableDisplayNames = {
      'tasks': 'المهام',
      'users': 'المستخدمين',
      'departments': 'الأقسام',
      'task_types': 'أنواع المهام',
      'task_statuses': 'حالات المهام',
      'task_priorities': 'أولويات المهام',
      'task_comments': 'تعليقات المهام',
      'task_attachments': 'مرفقات المهام',
    };

    return tableDisplayNames[tableName] ?? tableName;
  }

  /// عرض مربع حوار المساعدة
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مساعدة حول إعدادات التقرير'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHelpSection(
                'المهام الحالة',
                'اختر جدول البيانات الرئيسي مثل المهام أو المستخدمين.',
              ),
              const Divider(),
              _buildHelpSection(
                'المحور الرأسي',
                'اختر العمود الذي سيتم عرضه على المحور الرأسي (Y) في الرسم البياني.',
              ),
              const Divider(),
              _buildHelpSection(
                'تعداد (COUNT)',
                'اختر دالة التجميع المناسبة مثل العدد أو المجموع أو المتوسط.',
              ),
              const Divider(),
              _buildHelpSection(
                'المهام التعريف',
                'اختر العمود الذي سيتم عرضه على المحور الأفقي (X) في الرسم البياني.',
              ),
              const Divider(),
              _buildHelpSection(
                'تصنيف البيانات باللون',
                'اختر عمود لتصنيف البيانات حسب اللون في الرسم البياني.',
              ),
              const Divider(),
              _buildHelpSection(
                'معايير التصفية',
                'أدخل شروط SQL لتصفية البيانات المعروضة في التقرير.',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// بناء قسم المساعدة
  Widget _buildHelpSection(String title, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار اقتراحات الإصلاح
  void _showFixSuggestionsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اقتراحات لإصلاح المشكلة'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildFixSuggestion(
                'تحقق من اسم العمود',
                'تأكد من أن اسم العمود المحدد موجود في جدول البيانات. قد يكون هناك خطأ في كتابة الاسم أو قد يكون العمود غير متاح.',
              ),
              const Divider(),
              _buildFixSuggestion(
                'استخدم الاسم المؤهل للعمود',
                'تأكد من استخدام اسم الجدول مع اسم العمود (مثال: tasks.status بدلاً من status).',
              ),
              const Divider(),
              _buildFixSuggestion(
                'تحقق من العلاقات بين الجداول',
                'إذا كنت تستخدم أكثر من جدول، تأكد من تعريف العلاقات بشكل صحيح بين الجداول.',
              ),
              const Divider(),
              _buildFixSuggestion(
                'اختر عمودًا آخر',
                'جرب اختيار عمود آخر للمحور الأفقي أو الرأسي.',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// بناء اقتراح إصلاح
  Widget _buildFixSuggestion(String title, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.lightbulb_outline, color: Colors.amber),
              const SizedBox(width: 8),
              Text(
                title,
                style:
                    const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار مساعدة معايير التصفية
  void _showFilterHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مساعدة حول معايير التصفية'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'أمثلة على معايير التصفية:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 16),
              _buildFilterExample(
                'تصفية حسب القيمة المساوية:',
                'status = 1',
                'يعرض فقط السجلات التي حالتها تساوي 1',
              ),
              const Divider(),
              _buildFilterExample(
                'تصفية حسب النص:',
                'title LIKE "%مهمة%"',
                'يعرض السجلات التي يحتوي عنوانها على كلمة "مهمة"',
              ),
              const Divider(),
              _buildFilterExample(
                'تصفية متعددة الشروط:',
                'status = 1 AND priority > 2',
                'يعرض السجلات التي حالتها 1 وأولويتها أكبر من 2',
              ),
              const Divider(),
              _buildFilterExample(
                'تصفية حسب التاريخ:',
                'createdAt > "2023-01-01"',
                'يعرض السجلات التي تم إنشاؤها بعد 1 يناير 2023',
              ),
              const Divider(),
              _buildFilterExample(
                'تصفية بشروط OR:',
                'status = 1 OR status = 2',
                'يعرض السجلات التي حالتها 1 أو 2',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// بناء مثال على معايير التصفية
  Widget _buildFilterExample(String title, String example, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              example,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم العرض لعمود
  String _getDisplayColumnName(String column) {
    // استخراج اسم العمود بدون اسم الجدول للعرض
    String displayName = column;
    if (column.contains('.')) {
      final parts = column.split('.');
      if (parts.length > 1) {
        displayName = parts[1]; // استخدام اسم العمود فقط للعرض
      }
    }

    // تحسين اسم العرض باستخدام أسماء العرض المعرفة
    final Map<String, String> columnDisplayNames = {
      'id': 'المعرف',
      'title': 'العنوان',
      'name': 'الاسم',
      'description': 'الوصف',
      'status': 'الحالة',
      'priority': 'الأولوية',
      'created_at': 'تاريخ الإنشاء',
      'updated_at': 'تاريخ التحديث',
      'due_date': 'تاريخ الاستحقاق',
      'completed_at': 'تاريخ الإكمال',
      'user_id': 'المستخدم',
      'department_id': 'القسم',
      'task_type_id': 'نوع المهمة',
      'task_status_id': 'حالة المهمة',
      'task_priority_id': 'أولوية المهمة',
      'assigned_to': 'مسند إلى',
      'created_by': 'أنشئت بواسطة',
      'completion_percentage': 'نسبة الإكمال',
      'email': 'البريد الإلكتروني',
      'phone': 'رقم الهاتف',
      'address': 'العنوان',
      'role': 'الدور',
      'is_active': 'نشط',
      'count': 'العدد',
      'total': 'المجموع',
      'average': 'المتوسط',
    };

    return columnDisplayNames[displayName] ?? displayName;
  }

  /// الحصول على جميع الأعمدة المحددة مع إضافة اسم الجدول
  List<Map<String, String>> _getAllColumnsWithTablePrefix() {
    final List<Map<String, String>> result = [];

    // قائمة بأسماء الأعمدة المعروضة للمستخدم
    final Map<String, String> columnDisplayNames = {
      'id': 'المعرف',
      'title': 'العنوان',
      'name': 'الاسم',
      'description': 'الوصف',
      'status': 'الحالة',
      'priority': 'الأولوية',
      'created_at': 'تاريخ الإنشاء',
      'updated_at': 'تاريخ التحديث',
      'due_date': 'تاريخ الاستحقاق',
      'completed_at': 'تاريخ الإكمال',
      'user_id': 'المستخدم',
      'department_id': 'القسم',
      'task_type_id': 'نوع المهمة',
      'task_status_id': 'حالة المهمة',
      'task_priority_id': 'أولوية المهمة',
      'assigned_to': 'مسند إلى',
      'created_by': 'أنشئت بواسطة',
      'completion_percentage': 'نسبة الإكمال',
      'email': 'البريد الإلكتروني',
      'phone': 'رقم الهاتف',
      'address': 'العنوان',
      'role': 'الدور',
      'is_active': 'نشط',
      'count': 'العدد',
      'total': 'المجموع',
      'average': 'المتوسط',
    };

    // جمع أعمدة الجدول الرئيسي والجداول المرتبطة
    Map<String, List<Map<String, dynamic>>> columnsByTable = {};

    // إضافة أعمدة الجدول الرئيسي
    final mainTableColumns =
        _powerBIController.tableColumns[_selectedTable] ?? [];
    if (mainTableColumns.isNotEmpty) {
      columnsByTable[_selectedTable] = mainTableColumns;
    }

    // إضافة أعمدة الجداول المرتبطة
    if (_useMultipleTables) {
      for (final relatedTable in _relatedTables) {
        final relatedTableColumns =
            _powerBIController.tableColumns[relatedTable] ?? [];
        if (relatedTableColumns.isNotEmpty) {
          columnsByTable[relatedTable] = relatedTableColumns;
        }
      }
    }

    // إنشاء قائمة بجميع الأعمدة مع إضافة اسم الجدول
    for (final tableEntry in columnsByTable.entries) {
      final tableName = tableEntry.key;
      final tableDisplayName = _getTableDisplayName(tableName);

      for (final column in tableEntry.value) {
        final columnName = column['name'] as String;
        final fullColumnName = '$tableName.$columnName';
        final displayName = columnDisplayNames[columnName] ?? columnName;
        final fullDisplayName = '$tableDisplayName: $displayName';

        // إضافة العمود إلى النتيجة فقط إذا كان محددًا
        if (_selectedColumns.contains(columnName)) {
          result.add({
            'fullColumnName': fullColumnName,
            'displayName': fullDisplayName,
          });
        }
      }
    }

    return result;
  }
}
