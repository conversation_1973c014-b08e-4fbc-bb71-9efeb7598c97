import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/user_permission_model.dart';

/// ملف اختبار لحل مشكلة UserPermissions
void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'User Permissions Test',
      home: UserPermissionsTestScreen(),
    );
  }
}

class UserPermissionsTestScreen extends StatefulWidget {
  @override
  _UserPermissionsTestScreenState createState() => _UserPermissionsTestScreenState();
}

class _UserPermissionsTestScreenState extends State<UserPermissionsTestScreen> {
  
  void testUserPermissionCreation() {
    print('🧪 اختبار إنشاء UserPermission...');
    
    try {
      // إنشاء UserPermission بالطريقة الصحيحة
      final userPermission = UserPermission(
        id: 0,
        userId: 20,
        permissionId: 82,
        grantedBy: 21,
        grantedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        isActive: true,
        isDeleted: false,
      );
      
      print('✅ تم إنشاء UserPermission بنجاح');
      print('📋 البيانات: ${userPermission.toJson()}');
      
      // التحقق من صحة البيانات
      final json = userPermission.toJson();
      
      // التحقق من الحقول المطلوبة
      assert(json['userId'] != null && json['userId'] > 0, 'userId مطلوب');
      assert(json['permissionId'] != null && json['permissionId'] > 0, 'permissionId مطلوب');
      assert(json['grantedBy'] != null && json['grantedBy'] > 0, 'grantedBy مطلوب');
      assert(json['grantedAt'] != null && json['grantedAt'] > 0, 'grantedAt مطلوب');
      assert(json['isActive'] != null, 'isActive مطلوب');
      assert(json['isDeleted'] != null, 'isDeleted مطلوب');
      
      print('✅ جميع التحققات نجحت');
      
    } catch (e) {
      print('❌ خطأ في اختبار UserPermission: $e');
    }
  }
  
  void testUserPermissionFromJson() {
    print('🧪 اختبار تحليل UserPermission من JSON...');
    
    try {
      // JSON كما يأتي من الخادم
      final json = {
        'id': 1,
        'userId': 20,
        'permissionId': 82,
        'grantedBy': 21,
        'grantedAt': 1751753396,
        'isActive': true,
        'expiresAt': null,
        'isDeleted': false,
        'user': null,
        'permission': null,
        'grantedByNavigation': null,
      };
      
      final userPermission = UserPermission.fromJson(json);
      print('✅ تم تحليل UserPermission من JSON بنجاح');
      print('📋 البيانات المحللة: ${userPermission.toJson()}');
      
    } catch (e) {
      print('❌ خطأ في تحليل UserPermission من JSON: $e');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('User Permissions Test'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: testUserPermissionCreation,
              child: Text('اختبار إنشاء UserPermission'),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: testUserPermissionFromJson,
              child: Text('اختبار تحليل UserPermission من JSON'),
            ),
            SizedBox(height: 20),
            Text(
              'تحقق من Console للنتائج',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}
