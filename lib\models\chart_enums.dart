/// أنواع المخططات
enum ChartType {
  pie('pie', 'مخطط دائري'),
  donut('donut', 'مخطط حلقي'),
  bar('bar', 'مخطط شريطي'),
  line('line', 'مخطط خطي'),
  area('area', 'مخطط مساحي'),
  scatter('scatter', 'مخطط انتشاري'),
  bubble('bubble', 'مخطط فقاعي'),
  radar('radar', 'مخطط راداري'),
  gauge('gauge', 'مخطط مقياس'),
  funnel('funnel', 'مخطط قمعي'),
  treemap('treemap', 'خريطة شجرية'),
  heatmap('heatmap', 'خريطة حرارية'),
  gantt('gantt', 'مخطط جانت'),
  table('table', 'جدول'),
  waterfall('waterfall', 'مخطط شلال'),
  candlestick('candlestick', 'مخطط شموع'),
  boxplot('boxplot', 'مخطط صندوقي'),
  network('network', 'مخطط العلاقات'),
   sankey('sankey', 'مخطط سانكي'),
  stackedBar('stackedBar', 'مخطط شريطي تراكمي');

  const ChartType(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static ChartType fromValue(String value) {
    return ChartType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ChartType.bar,
    );
  }
}

/// أنواع تصفية الوقت
enum TimeFilterType {
  day('day', 'اليوم'),
  week('week', 'الأسبوع'),
  month('month', 'الشهر'),
  quarter('quarter', 'الربع'),
  year('year', 'السنة'),
  custom('custom', 'فترة مخصصة'),
  all('all', 'الكل');

  const TimeFilterType(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static TimeFilterType fromValue(String value) {
    return TimeFilterType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => TimeFilterType.month,
    );
  }
}

/// نطاق عرض مخطط جانت
enum GanttViewRange {
  day('day', 'يومي'),
  week('week', 'أسبوعي'),
  month('month', 'شهري'),
  quarter('quarter', 'ربع سنوي'),
  year('year', 'سنوي');

  const GanttViewRange(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static GanttViewRange fromValue(String value) {
    return GanttViewRange.values.firstWhere(
      (range) => range.value == value,
      orElse: () => GanttViewRange.month,
    );
  }
}

/// خيارات التصفية المتقدمة للمخططات
class ChartFilterOptions {
  final bool enableDateFilter;
  final bool enableCategoryFilter;
  final bool enableValueFilter;
  final bool enableCustomFilter;
  final Map<String, dynamic>? customFilters;

  const ChartFilterOptions({
    this.enableDateFilter = true,
    this.enableCategoryFilter = true,
    this.enableValueFilter = true,
    this.enableCustomFilter = false,
    this.customFilters,
  });

  factory ChartFilterOptions.fromJson(Map<String, dynamic> json) {
    return ChartFilterOptions(
      enableDateFilter: json['enableDateFilter'] as bool? ?? true,
      enableCategoryFilter: json['enableCategoryFilter'] as bool? ?? true,
      enableValueFilter: json['enableValueFilter'] as bool? ?? true,
      enableCustomFilter: json['enableCustomFilter'] as bool? ?? false,
      customFilters: json['customFilters'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enableDateFilter': enableDateFilter,
      'enableCategoryFilter': enableCategoryFilter,
      'enableValueFilter': enableValueFilter,
      'enableCustomFilter': enableCustomFilter,
      'customFilters': customFilters,
    };
  }

  ChartFilterOptions copyWith({
    bool? enableDateFilter,
    bool? enableCategoryFilter,
    bool? enableValueFilter,
    bool? enableCustomFilter,
    Map<String, dynamic>? customFilters,
  }) {
    return ChartFilterOptions(
      enableDateFilter: enableDateFilter ?? this.enableDateFilter,
      enableCategoryFilter: enableCategoryFilter ?? this.enableCategoryFilter,
      enableValueFilter: enableValueFilter ?? this.enableValueFilter,
      enableCustomFilter: enableCustomFilter ?? this.enableCustomFilter,
      customFilters: customFilters ?? this.customFilters,
    );
  }
}
