import 'package:flutter/material.dart';
import '../../../../models/chart_enums.dart';
import '../charts/base/chart_interface.dart';
import '../charts/implementations/bar_chart_impl.dart';
import '../charts/implementations/pie_chart_impl.dart';
import '../charts/implementations/line_chart_impl.dart';

/// مصنع المخططات
/// 
/// يوفر طريقة موحدة لإنشاء جميع أنواع المخططات
/// مع دعم التسجيل الديناميكي لأنواع جديدة
class ChartFactory {
  // خريطة مسجلات المخططات
  static final Map<ChartType, ChartBuilder> _chartBuilders = {};
  
  // تهيئة المصنع مع المخططات الافتراضية
  static bool _initialized = false;

  /// تهيئة المصنع
  static void initialize() {
    if (_initialized) return;

    // تسجيل المخططات الأساسية فقط
    registerChart(ChartType.bar, (data, settings, theme) =>
        BarChartImpl(data: data, settings: settings, theme: theme));

    registerChart(ChartType.pie, (data, settings, theme) =>
        PieChartImpl(data: data, settings: settings, theme: theme));

    registerChart(ChartType.line, (data, settings, theme) =>
        LineChartImpl(data: data, settings: settings, theme: theme));

    _initialized = true;
  }

  /// تسجيل نوع مخطط جديد
  static void registerChart(ChartType type, ChartBuilder builder) {
    _chartBuilders[type] = builder;
  }

  /// إلغاء تسجيل نوع مخطط
  static void unregisterChart(ChartType type) {
    _chartBuilders.remove(type);
  }

  /// إنشاء مخطط
  static Widget createChart({
    required ChartType type,
    required Map<String, dynamic> data,
    Map<String, dynamic> settings = const {},
    ChartTheme? theme,
  }) {
    // تهيئة المصنع إذا لم يكن مهيأ
    if (!_initialized) {
      initialize();
    }

    // البحث عن منشئ المخطط
    final builder = _chartBuilders[type];
    if (builder == null) {
      return _buildUnsupportedChart(type);
    }

    try {
      // إنشاء المخطط
      final chart = builder(data, settings, theme ?? ChartTheme.defaultTheme());
      
      // التحقق من صحة البيانات
      if (!chart.validateData(data)) {
        return _buildInvalidDataChart(type);
      }

      return chart.build();
    } catch (e) {
      debugPrint('خطأ في إنشاء المخطط $type: $e');
      return _buildErrorChart(type, e.toString());
    }
  }

  /// الحصول على قائمة الأنواع المدعومة
  static List<ChartType> getSupportedTypes() {
    if (!_initialized) {
      initialize();
    }
    return _chartBuilders.keys.toList();
  }

  /// التحقق من دعم نوع معين
  static bool isSupported(ChartType type) {
    if (!_initialized) {
      initialize();
    }
    return _chartBuilders.containsKey(type);
  }

  /// بناء مخطط غير مدعوم
  static Widget _buildUnsupportedChart(ChartType type) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.orange.shade600,
          ),
          const SizedBox(height: 16),
          Text(
            'نوع المخطط غير مدعوم',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.orange.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'النوع: ${type.displayName}',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مخطط بيانات غير صحيحة
  static Widget _buildInvalidDataChart(ChartType type) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.warning_amber_outlined,
            size: 48,
            color: Colors.amber.shade600,
          ),
          const SizedBox(height: 16),
          Text(
            'بيانات غير صحيحة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.amber.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'البيانات المقدمة لا تتوافق مع نوع المخطط',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء مخطط خطأ
  static Widget _buildErrorChart(ChartType type, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error,
            size: 48,
            color: Colors.red.shade600,
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في إنشاء المخطط',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.red.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

/// نوع دالة بناء المخطط
typedef ChartBuilder = ChartInterface Function(
  Map<String, dynamic> data,
  Map<String, dynamic> settings,
  ChartTheme theme,
);
