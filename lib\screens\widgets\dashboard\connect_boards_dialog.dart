import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/dashboard_model.dart';
import '../../../models/board_model.dart';
import '../../../controllers/board_controller.dart';
import '../../../services/dashboard_service.dart';


/// مربع حوار ربط اللوحات بلوحة المعلومات
class ConnectBoardsDialog extends StatefulWidget {
  /// لوحة المعلومات
  final Dashboard dashboard;

  const ConnectBoardsDialog({
    super.key,
    required this.dashboard,
  });

  @override
  State<ConnectBoardsDialog> createState() => _ConnectBoardsDialogState();
}

class _ConnectBoardsDialogState extends State<ConnectBoardsDialog> {
  // خدمة لوحة المعلومات
  final DashboardService _dashboardService = Get.find<DashboardService>();

  // تحكم اللوحات
  late final BoardController _boardController;

  // اللوحات المتصلة
  final RxList<int> _connectedBoardIds = <int>[].obs;

  // حالة البحث
  final TextEditingController _searchController = TextEditingController();
  final RxString _searchQuery = ''.obs;

  // حالة الحفظ
  final RxBool _isSaving = false.obs;

  // إضافة اللوحات المتصلة حديثًا إلى العناصر الموجودة
  final RxBool _addToExistingWidgets = true.obs;

  @override
  void initState() {
    super.initState();
    // تهيئة متحكم اللوحات
    _boardController = Get.put(BoardController());
    _connectedBoardIds.assignAll(_dashboardService.connectedBoardIds);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  /// عند تغيير البحث
  void _onSearchChanged() {
    _searchQuery.value = _searchController.text;
  }

  @override
  Widget build(BuildContext context) {
    // تحديد ألوان حسب السمة - مستوحاة من Monday.com
    final Color accentColor = const Color(0xFF00A9FF); // أزرق Monday.com

    final Color cardColor = Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF292F4C)
        : Colors.white;

    // تحديد لون الخلفية حسب السمة
    Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF181B34)
        : const Color(0xFFF6F7FB);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: cardColor,
      child: Container(
        width: 600,
        height: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(
                  Icons.link,
                  color: accentColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'ربط اللوحات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Get.back(),
                  tooltip: 'إغلاق',
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),

            // حقل البحث
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'بحث',
                hintText: 'ابحث عن لوحة...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.search),
                suffixIcon: Obx(() => _searchQuery.value.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : const SizedBox.shrink()),
              ),
            ),
            const SizedBox(height: 16),

            // قائمة اللوحات
            Expanded(
              child: Obx(() {
                final boards = _boardController.boards
                    .where((board) => _searchQuery.value.isEmpty ||
                        board.name.toLowerCase().contains(_searchQuery.value.toLowerCase()))
                    .toList();

                if (boards.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 48,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _searchQuery.value.isEmpty
                              ? 'لا توجد لوحات متاحة'
                              : 'لا توجد نتائج للبحث',
                          style: TextStyle(
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: boards.length,
                  itemBuilder: (context, index) {
                    final board = boards[index];
                    return _buildBoardItem(board);
                  },
                );
              }),
            ),
            const SizedBox(height: 16),

            // خيار إضافة اللوحات المتصلة حديثًا إلى العناصر الموجودة
            Obx(() => CheckboxListTile(
                  value: _addToExistingWidgets.value,
                  onChanged: (value) {
                    _addToExistingWidgets.value = value ?? true;
                  },
                  title: const Text('إضافة اللوحات المتصلة حديثًا إلى العناصر الموجودة'),
                  controlAffinity: ListTileControlAffinity.leading,
                  activeColor: accentColor,
                )),
            const SizedBox(height: 16),

            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 12),
                Obx(() => ElevatedButton(
                      onPressed: _isSaving.value ? null : _saveConnectedBoards,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: accentColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                      child: _isSaving.value
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Text('حفظ'),
                    )),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر اللوحة
  Widget _buildBoardItem(Board board) {
    final isConnected = _connectedBoardIds.contains(board.id);

    return ListTile(
      title: Text(board.name),
      subtitle: Text(board.description),
      leading: Icon(
        Icons.dashboard,
        color: isConnected ? const Color(0xFF00A9FF) : Colors.grey,
      ),
      trailing: Checkbox(
        value: isConnected,
        onChanged: (value) {
          if (value == true) {
            _connectedBoardIds.add(board.id);
          } else {
            _connectedBoardIds.remove(board.id);
          }
        },
        activeColor: const Color(0xFF00A9FF),
      ),
      onTap: () {
        if (_connectedBoardIds.contains(board.id)) {
          _connectedBoardIds.remove(board.id);
        } else {
          _connectedBoardIds.add(board.id);
        }
      },
    );
  }

  /// حفظ اللوحات المتصلة
  void _saveConnectedBoards() async {
    _isSaving.value = true;

    try {
      // تحديث اللوحات المتصلة
      _dashboardService.connectedBoardIds.assignAll(_connectedBoardIds);

      // TODO: تنفيذ منطق حفظ اللوحات المتصلة في قاعدة البيانات

      Get.back(result: true);
      Get.snackbar(
        'تم',
        'تم حفظ اللوحات المتصلة بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حفظ اللوحات المتصلة: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isSaving.value = false;
    }
  }
}
