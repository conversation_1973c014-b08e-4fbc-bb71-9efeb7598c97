using System.ComponentModel.DataAnnotations;

namespace webApi.Models
{
    /// <summary>
    /// نموذج طلب إنشاء نسخة احتياطية
    /// </summary>
    public class CreateBackupRequest
    {
        /// <summary>
        /// وصف النسخة الاحتياطية
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// معرف المستخدم الذي ينشئ النسخة الاحتياطية
        /// </summary>
        [Required]
        public int CreatedBy { get; set; }

        /// <summary>
        /// هل هي نسخة احتياطية تلقائية
        /// </summary>
        public bool IsAutoBackup { get; set; } = false;

        /// <summary>
        /// هل تتضمن الملفات المرفقة
        /// </summary>
        public bool IncludeFiles { get; set; } = true;
    }
}
