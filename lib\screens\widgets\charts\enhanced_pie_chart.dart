import 'package:flutter/material.dart';
// import 'package:fl_chart/fl_chart.dart'; // تم تعليقها لاستخدام Syncfusion Charts
import 'package:syncfusion_flutter_charts/charts.dart'; // استخدام Syncfusion Charts بدلاً من fl_chart

import '../../../models/chart_enums.dart';
import '../../../models/advanced_filter_options.dart';

/// مكون الرسم البياني الدائري المحسن
class EnhancedPieChart extends StatefulWidget {
  /// بيانات المخطط
  final Map<String, double> data;

  /// ألوان القطاعات
  final Map<String, Color>? sectionColors;

  /// عنوان المخطط
  final String? title;

  /// إظهار القيم
  final bool showValues;

  /// إظهار النسب المئوية
  final bool showPercentages;

  /// إظهار المفتاح
  final bool showLegend;

  /// نصف القطر
  final double radius;

  /// عرض الحدود
  final double borderWidth;

  /// لون الحدود
  final Color borderColor;

  /// المسافة بين القطاعات
  final double sectionsSpace;

  /// دالة التصفية
  final Function(DateTime? startDate, DateTime? endDate, TimeFilterType filterType)? onFilterChanged;

  /// دالة التصدير
  final Function(String format)? onExport;

  /// دالة تغيير نوع المخطط
  final Function(ChartType chartType)? onChartTypeChanged;

  /// إظهار خيارات التصفية
  final bool showFilterOptions;

  /// إظهار خيارات التصدير
  final bool showExportOptions;

  /// تنسيقات التصدير المدعومة
  final List<String> supportedExportFormats;

  /// نوع المخطط
  final ChartType chartType;

  /// خيارات التصفية المتقدمة
  final AdvancedFilterOptions advancedFilterOptions;

  const EnhancedPieChart({
    super.key,
    required this.data,
    this.sectionColors,
    this.title,
    this.showValues = true,
    this.showPercentages = true,
    this.showLegend = true,
    this.radius = 100,
    this.borderWidth = 1,
    this.borderColor = Colors.white,
    this.sectionsSpace = 2,
    this.onFilterChanged,
    this.onExport,
    this.onChartTypeChanged,
    this.showFilterOptions = false,
    this.showExportOptions = false,
    this.supportedExportFormats = const ['pdf', 'excel', 'csv'],
    required this.chartType,
    required this.advancedFilterOptions,
  });

  @override
  State<EnhancedPieChart> createState() => _EnhancedPieChartState();
}

class _EnhancedPieChartState extends State<EnhancedPieChart> {
  int _touchedIndex = -1;

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات لعرضها'),
      );
    }

    return Column(
      children: [
        if (widget.title != null)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              widget.title!,
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
          ),
        Expanded(
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: SfCircularChart(
                  // تفعيل الميزات التفاعلية المتقدمة من Syncfusion
                  enableMultiSelection: true,
                  selectionGesture: ActivationMode.singleTap,

                  // تفعيل Tooltip المتقدم
                  tooltipBehavior: TooltipBehavior(
                    enable: true,
                    format: 'point.x: point.y (point.percentage%)',
                    header: '',
                    canShowMarker: false,
                    activationMode: ActivationMode.singleTap,
                    animationDuration: 500,
                  ),

                  // إعدادات المفتاح
                  legend: widget.showLegend ? Legend(
                    isVisible: true,
                    position: LegendPosition.right,
                    overflowMode: LegendItemOverflowMode.wrap,
                    textStyle: const TextStyle(fontSize: 12),
                    itemPadding: 8,
                  ) : Legend(isVisible: false),

                  series: <CircularSeries>[
                    PieSeries<MapEntry<String, double>, String>(
                      dataSource: widget.data.entries.toList(),
                      xValueMapper: (MapEntry<String, double> data, _) => data.key,
                      yValueMapper: (MapEntry<String, double> data, _) => data.value,

                      // تخصيص الألوان
                      pointColorMapper: (MapEntry<String, double> data, int index) {
                        final defaultColors = [
                          Colors.blue, Colors.green, Colors.orange, Colors.red,
                          Colors.purple, Colors.teal, Colors.amber, Colors.indigo,
                        ];
                        return widget.sectionColors?[data.key] ??
                               defaultColors[index % defaultColors.length];
                      },

                      // إعدادات العرض
                      radius: '${widget.radius}%',
                      explode: true,
                      explodeIndex: _touchedIndex,
                      explodeOffset: '10%',

                      // إعدادات التسميات
                      dataLabelSettings: DataLabelSettings(
                        isVisible: widget.showPercentages,
                        labelPosition: ChartDataLabelPosition.outside,
                        textStyle: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        labelIntersectAction: LabelIntersectAction.shift,
                      ),

                      // إعدادات التحديد
                      selectionBehavior: SelectionBehavior(
                        enable: true,
                        selectedColor: Colors.grey.shade300,
                        unselectedColor: Colors.grey.shade600,
                        selectedBorderColor: Colors.black,
                        selectedBorderWidth: 2,
                      ),

                      // إعدادات الحدود - استخدام strokeColor و strokeWidth في Syncfusion
                      strokeColor: widget.borderColor,
                      strokeWidth: widget.borderWidth,

                      // معالج النقر
                      onPointTap: (ChartPointDetails details) {
                        setState(() {
                          _touchedIndex = details.pointIndex!;
                        });
                      },
                    ),
                  ],
                ),
              ),
              // إزالة المفتاح المخصص لأن Syncfusion يوفر مفتاح مدمج أفضل
            ],
          ),
        ),
      ],
    );
  }

  // تم حذف الدوال _buildSections و _buildLegend لأن Syncfusion Charts يوفر هذه الوظائف مدمجة
  // مع ميزات أكثر تقدماً وأداء أفضل
}
