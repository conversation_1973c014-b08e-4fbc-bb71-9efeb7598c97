-- تنفيذ إصلاح الصلاحيات المرمزة
-- تاريخ التنفيذ: 2025-01-06

USE databasetasks;
GO

-- بدء المعاملة
BEGIN TRANSACTION;

-- إصلاح المجموعة الأولى: السجلات 40-71
UPDATE permissions SET description = N'إدارة لوحة التحكم' WHERE id = 40;
UPDATE permissions SET description = N'عرض المهام' WHERE id = 41;
UPDATE permissions SET description = N'إنشاء مهام جديدة' WHERE id = 42;
UPDATE permissions SET description = N'تعديل المهام' WHERE id = 43;
UPDATE permissions SET description = N'حذف المهام' WHERE id = 44;
UPDATE permissions SET description = N'تعيين المهام للمستخدمين' WHERE id = 45;
UPDATE permissions SET description = N'تحديث المهام الخاصة' WHERE id = 46;
UPDATE permissions SET description = N'عرض المستخدمين' WHERE id = 47;
UPDATE permissions SET description = N'إنشاء مستخدمين جدد' WHERE id = 48;
UPDATE permissions SET description = N'تعديل بيانات المستخدمين' WHERE id = 49;
UPDATE permissions SET description = N'إدارة أدوار المستخدمين' WHERE id = 51;
UPDATE permissions SET description = N'عرض التقارير' WHERE id = 52;
UPDATE permissions SET description = N'إنشاء تقارير جديدة' WHERE id = 53;
UPDATE permissions SET description = N'تصدير التقارير' WHERE id = 54;
UPDATE permissions SET description = N'إدارة النظام' WHERE id = 55;
UPDATE permissions SET description = N'إنشاء نسخ احتياطية' WHERE id = 56;
UPDATE permissions SET description = N'استعادة النسخ الاحتياطية' WHERE id = 57;
UPDATE permissions SET description = N'إدارة قاعدة البيانات' WHERE id = 58;
UPDATE permissions SET description = N'عرض الملف الشخصي' WHERE id = 59;
UPDATE permissions SET description = N'تعديل الملف الشخصي' WHERE id = 60;
UPDATE permissions SET description = N'عرض الإشعارات' WHERE id = 61;
UPDATE permissions SET description = N'إدارة الإشعارات' WHERE id = 62;
UPDATE permissions SET description = N'إدارة التقويم' WHERE id = 64;
UPDATE permissions SET description = N'عرض الأقسام' WHERE id = 65;
UPDATE permissions SET description = N'إدارة الأقسام' WHERE id = 66;
UPDATE permissions SET description = N'عرض جميع المهام' WHERE id = 71;

-- إصلاح المجموعة الثانية: السجلات 1205-1227
UPDATE permissions SET description = N'عرض تفاصيل المهام والانتقال إليها' WHERE id = 1205;
UPDATE permissions SET description = N'تحديث نسبة إنجاز المهام' WHERE id = 1206;
UPDATE permissions SET description = N'تصفية وفلترة المهام' WHERE id = 1207;
UPDATE permissions SET description = N'ترتيب المهام حسب معايير مختلفة' WHERE id = 1208;
UPDATE permissions SET description = N'إدارة لوحة المهام وإضافة أعمدة' WHERE id = 1209;
UPDATE permissions SET description = N'تثبيت وإلغاء تثبيت الرسائل' WHERE id = 1210;
UPDATE permissions SET description = N'تعديل الرسائل المرسلة' WHERE id = 1211;
UPDATE permissions SET description = N'حذف الرسائل' WHERE id = 1212;
UPDATE permissions SET description = N'الرد على الرسائل' WHERE id = 1213;
UPDATE permissions SET description = N'تحديد الرسائل للمتابعة' WHERE id = 1214;
UPDATE permissions SET description = N'تعيين مدير للقسم' WHERE id = 1215;
UPDATE permissions SET description = N'إضافة مستخدمين للقسم' WHERE id = 1216;
UPDATE permissions SET description = N'إزالة مستخدمين من القسم' WHERE id = 1217;
UPDATE permissions SET description = N'إدارة شاملة لمستخدمي القسم' WHERE id = 1218;
UPDATE permissions SET description = N'عرض تقارير المساهمات' WHERE id = 1219;
UPDATE permissions SET description = N'تصدير التقارير كـ PDF' WHERE id = 1220;
UPDATE permissions SET description = N'تقارير عبء العمل والأداء' WHERE id = 1221;
UPDATE permissions SET description = N'تغيير كلمة المرور' WHERE id = 1222;
UPDATE permissions SET description = N'إصلاح قاعدة البيانات' WHERE id = 1223;
UPDATE permissions SET description = N'إنشاء نسخ احتياطية لقاعدة البيانات' WHERE id = 1224;
UPDATE permissions SET description = N'استعادة النسخ الاحتياطية لقاعدة البيانات' WHERE id = 1225;
UPDATE permissions SET description = N'اختبار الصلاحيات والتحقق منها' WHERE id = 1226;
UPDATE permissions SET description = N'أدوات التشخيص والتطوير' WHERE id = 1227;

-- إصلاح المجموعة الثالثة: السجلات 1232-1236
UPDATE permissions SET 
    name = 'settings.notifications',
    description = N'إعدادات الإشعارات',
    permission_group = 'Settings',
    category = N'إعدادات'
WHERE id = 1232;

UPDATE permissions SET 
    name = 'reports.dynamic_access',
    description = N'الوصول الديناميكي للتقارير',
    permission_group = 'Reports',
    category = N'تقارير'
WHERE id = 1233;

UPDATE permissions SET 
    name = 'admin.database_repair',
    description = N'إصلاح وصيانة قاعدة البيانات',
    permission_group = 'Admin',
    category = N'إدارة'
WHERE id = 1234;

UPDATE permissions SET 
    name = 'archive.view_documents',
    description = N'عرض وثائق الأرشيف',
    permission_group = 'Archive',
    category = N'أرشيف'
WHERE id = 1235;

UPDATE permissions SET 
    name = 'search.manage_history',
    description = N'إدارة تاريخ البحث',
    permission_group = 'Search',
    category = N'بحث'
WHERE id = 1236;

-- تحديث تاريخ التعديل
UPDATE permissions 
SET updated_at = DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
);

-- التحقق من النتائج
SELECT 
    id, 
    name, 
    description, 
    permission_group,
    category,
    'تم الإصلاح' as status
FROM permissions 
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
)
ORDER BY id;

-- إحصائيات نهائية
SELECT 
    'تقرير الإصلاح' as report,
    COUNT(*) as total_fixed_records
FROM permissions 
WHERE id IN (
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 65, 66, 71,
    1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227,
    1232, 1233, 1234, 1235, 1236
);

-- إنهاء المعاملة
COMMIT TRANSACTION;

PRINT N'تم إصلاح جميع الأوصاف المرمزة بنجاح!';
GO
