-- إضافة سريعة للصلاحيات الجديدة
USE [databasetasks]
GO

-- إضافة صلاحية تغيير حالة المهام
INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
SELECT 'tasks.change_status', N'تغيير حالة المهام عبر السحب والإفلات', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
WHERE NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.change_status')

-- إضافة صلاحية تغيير أولوية المهام
INSERT INTO [dbo].[permissions] ([name], [description], [permission_group], [is_active], [created_at])
SELECT 'tasks.change_priority', N'تغيير أولوية المهام عبر الأزرار والسحب والإفلات', 'tasks', 1, DATEDIFF(SECOND, '1970-01-01', GETUTCDATE())
WHERE NOT EXISTS (SELECT 1 FROM [dbo].[permissions] WHERE [name] = 'tasks.change_priority')

-- التحقق من النتائج
SELECT [name], [description] FROM [dbo].[permissions] 
WHERE [name] IN ('tasks.change_status', 'tasks.change_priority')
