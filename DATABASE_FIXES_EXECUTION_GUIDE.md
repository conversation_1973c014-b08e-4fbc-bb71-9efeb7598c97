# دليل تنفيذ إصلاحات قاعدة البيانات لحل مشاكل UserPermissions

## 🎯 **الهدف**
تطبيق جميع الإصلاحات المطلوبة على قاعدة البيانات لحل مشاكل إضافة الصلاحيات للمستخدمين.

## 📋 **المشاكل التي سيتم حلها**
1. ✅ `Invalid column name 'created_at'` في user_permissions
2. ✅ `Invalid column name 'parent_role_id'` في custom_roles  
3. ✅ `Invalid column name 'UserId'` في user_custom_roles
4. ✅ `One or more validation errors occurred` عند إضافة صلاحيات

## 🛠️ **السكريبتات المتوفرة**

### 1. **execute_all_fixes.sql** (الرئيسي - مُوصى به)
- **الوصف**: ينفذ جميع الإصلاحات تلقائياً
- **الميزات**: 
  - نسخ احتياطي تلقائي
  - اختبار البنية
  - تقارير مفصلة
  - آمن للتنفيذ

### 2. **fix_user_permissions_database.sql** (مفصل)
- **الوصف**: إصلاحات مفصلة مع شرح كل خطوة
- **الاستخدام**: للفهم التفصيلي أو التنفيذ اليدوي

### 3. **verify_database_structure.sql** (تحقق)
- **الوصف**: يتحقق من تطبيق الإصلاحات بنجاح
- **الاستخدام**: بعد تنفيذ الإصلاحات

### 4. **rollback_user_permissions_fixes.sql** (تراجع)
- **الوصف**: يتراجع عن الإصلاحات في حالة وجود مشاكل
- **تحذير**: استخدم بحذر!

## 🚀 **خطوات التنفيذ**

### الطريقة الأولى: التنفيذ التلقائي (مُوصى به)

#### 1. افتح SQL Server Management Studio
```sql
-- اتصل بقاعدة البيانات
USE [TaskManagementDB]
GO
```

#### 2. نفذ السكريبت الرئيسي
```sql
-- نفذ الملف: webApi/webApi/SQL_Scripts/execute_all_fixes.sql
```

#### 3. تحقق من النتائج
```sql
-- نفذ الملف: webApi/webApi/SQL_Scripts/verify_database_structure.sql
```

### الطريقة الثانية: التنفيذ اليدوي

#### 1. نفذ الإصلاحات المفصلة
```sql
-- نفذ الملف: webApi/webApi/SQL_Scripts/fix_user_permissions_database.sql
```

#### 2. تحقق من النتائج
```sql
-- نفذ الملف: webApi/webApi/SQL_Scripts/verify_database_structure.sql
```

## 📊 **ما سيتم تطبيقه**

### 1. إصلاح جدول user_permissions
```sql
-- إضافة عمود created_at
ALTER TABLE [dbo].[user_permissions]
ADD [created_at] BIGINT NOT NULL 
DEFAULT (datediff(second,'1970-01-01',getutcdate()))

-- تحديث القيم الموجودة
UPDATE [dbo].[user_permissions] 
SET [created_at] = [granted_at]
WHERE [created_at] IS NULL OR [created_at] = 0
```

### 2. إنشاء جدول custom_roles
```sql
CREATE TABLE [dbo].[custom_roles] (
    [id] INT IDENTITY(1,1) NOT NULL,
    [name] NVARCHAR(100) NOT NULL,
    [description] NVARCHAR(255) NULL,
    [parent_role_id] INT NULL,
    [created_by] INT NOT NULL,
    [created_at] BIGINT NOT NULL,
    [updated_at] BIGINT NULL,
    [is_deleted] BIT NOT NULL DEFAULT (0),
    -- المفاتيح والقيود...
)
```

### 3. إنشاء جدول user_custom_roles
```sql
CREATE TABLE [dbo].[user_custom_roles] (
    [id] INT IDENTITY(1,1) NOT NULL,
    [user_id] INT NOT NULL,
    [custom_role_id] INT NOT NULL,
    [assigned_at] BIGINT NOT NULL,
    [is_deleted] BIT NOT NULL DEFAULT (0),
    -- المفاتيح والقيود...
)
```

### 4. إنشاء جدول custom_role_permissions
```sql
CREATE TABLE [dbo].[custom_role_permissions] (
    [id] INT IDENTITY(1,1) NOT NULL,
    [custom_role_id] INT NOT NULL,
    [permission_id] INT NOT NULL,
    [created_at] BIGINT NOT NULL,
    [created_by] INT NOT NULL,
    [is_deleted] BIT NOT NULL DEFAULT (0),
    -- المفاتيح والقيود...
)
```

### 5. إنشاء فهارس للأداء
```sql
-- فهارس user_permissions
CREATE INDEX [IX_user_permissions_created_at] ON [user_permissions] ([created_at])
CREATE INDEX [IX_user_permissions_active_deleted] ON [user_permissions] ([is_active], [is_deleted])

-- فهارس الجداول الجديدة
CREATE INDEX [IX_custom_roles_is_deleted] ON [custom_roles] ([is_deleted])
CREATE INDEX [IX_user_custom_roles_is_deleted] ON [user_custom_roles] ([is_deleted])
```

## 🧪 **التحقق من النجاح**

### 1. رسائل النجاح المتوقعة
```
✅ تم إضافة عمود created_at إلى user_permissions
✅ تم إنشاء جدول custom_roles
✅ تم إنشاء جدول user_custom_roles
✅ تم إنشاء جدول custom_role_permissions
✅ تم إنشاء الفهارس لتحسين الأداء
✅ اختبار إدراج user_permissions نجح
🎉 تم تطبيق جميع الإصلاحات بنجاح!
```

### 2. اختبار من التطبيق
بعد تنفيذ الإصلاحات:
1. أعد تشغيل الباك اند
2. افتح Flutter وجرب إضافة صلاحية لمستخدم
3. يجب أن ترى:
```
✅ تم إضافة الصلاحية بنجاح: 77
🎉 تم حفظ جميع تغييرات الصلاحيات بنجاح
```

## ⚠️ **احتياطات الأمان**

### 1. نسخ احتياطي تلقائي
السكريبت ينشئ نسخة احتياطية تلقائياً:
```sql
SELECT * INTO user_permissions_backup FROM [dbo].[user_permissions]
```

### 2. اختبار البنية
السكريبت يختبر الإدراج قبل الانتهاء:
```sql
-- اختبار إدراج تجريبي ثم التراجع عنه
BEGIN TRANSACTION TestInsert
INSERT INTO [dbo].[user_permissions] (...)
ROLLBACK TRANSACTION TestInsert
```

### 3. في حالة وجود مشاكل
```sql
-- نفذ سكريبت التراجع
-- webApi/webApi/SQL_Scripts/rollback_user_permissions_fixes.sql
```

## 🎯 **النتائج المتوقعة**

### قبل الإصلاح:
```
❌ Invalid column name 'created_at'
❌ Invalid column name 'parent_role_id'  
❌ Invalid column name 'UserId'
❌ One or more validation errors occurred
```

### بعد الإصلاح:
```
✅ API Request: POST /api/UserPermissions - Status: 201
✅ تم إضافة الصلاحية بنجاح
✅ لا توجد أخطاء في قاعدة البيانات
✅ التطبيق يعمل بشكل طبيعي
```

## 📞 **الدعم**

### في حالة وجود مشاكل:
1. **تحقق من رسائل الخطأ** في SQL Server
2. **نفذ سكريبت التحقق** `verify_database_structure.sql`
3. **تحقق من logs الباك اند** بعد إعادة التشغيل
4. **في الحالات الطارئة** نفذ `rollback_user_permissions_fixes.sql`

## 🚀 **ابدأ الآن!**

```sql
-- نفذ هذا الأمر في SQL Server Management Studio:
-- File > Open > webApi/webApi/SQL_Scripts/execute_all_fixes.sql
-- ثم اضغط F5 أو Execute
```

**الوقت المتوقع للتنفيذ: 2-5 دقائق**

**مستوى الصعوبة: سهل** ✅

**مستوى الأمان: عالي** 🔒
